@extends('layouts.main')

@section('title', $title)

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary to-secondary text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4" data-aos="fade-up">
                    Jelajahi Kategori Event
                </h1>
                <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Temukan berbagai macam event menarik sesuai dengan minat dan passion Anda
                </p>
                
                <!-- Search Bar -->
                <div class="max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                    <div class="relative">
                        <input type="text" 
                               id="categorySearch"
                               placeholder="Cari kategori event..." 
                               class="w-full px-6 py-4 pl-12 rounded-full text-gray-900 focus:outline-none focus:ring-4 focus:ring-white/30 shadow-lg">
                        <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Trending Categories -->
    @if($trending->isNotEmpty())
    <div class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    🔥 Kategori Trending
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Kategori paling populer minggu ini berdasarkan aktivitas pembelian tiket
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
                @foreach($trending as $index => $category)
                <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" 
                     data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    <div class="aspect-square bg-gradient-to-br from-{{ $category->color }}/20 to-{{ $category->color }}/40 p-6 flex flex-col items-center justify-center text-center relative">
                        <!-- Trending Badge -->
                        <div class="absolute top-3 right-3 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                            #{{ $index + 1 }}
                        </div>
                        
                        <!-- Icon -->
                        <div class="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="{{ $category->icon }}" class="w-8 h-8" style="color: {{ $category->color }}"></i>
                        </div>
                        
                        <!-- Category Info -->
                        <h3 class="font-bold text-gray-900 mb-2 group-hover:text-primary transition-colors">
                            {{ $category->name }}
                        </h3>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                            {{ $category->description }}
                        </p>
                        
                        <!-- Stats -->
                        <div class="text-xs text-gray-500">
                            {{ $category->active_events_count ?? 0 }} event aktif
                        </div>
                        
                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                            <a href="{{ route('categories.show', $category->slug) }}" 
                               class="w-full bg-white text-gray-900 py-2 px-4 rounded-lg text-center font-semibold hover:bg-gray-100 transition-colors">
                                Lihat Event
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- All Categories -->
    <div class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    Semua Kategori
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Jelajahi semua kategori event yang tersedia dan temukan yang sesuai dengan minat Anda
                </p>
            </div>

            <!-- Filter & Sort -->
            <div class="flex flex-wrap items-center justify-between mb-8 gap-4" data-aos="fade-up" data-aos-delay="200">
                <div class="flex flex-wrap gap-2">
                    <button class="filter-btn active px-4 py-2 rounded-full bg-primary text-white text-sm font-medium" data-filter="all">
                        Semua
                    </button>
                    <button class="filter-btn px-4 py-2 rounded-full bg-gray-200 text-gray-700 text-sm font-medium hover:bg-gray-300" data-filter="popular">
                        Populer
                    </button>
                    <button class="filter-btn px-4 py-2 rounded-full bg-gray-200 text-gray-700 text-sm font-medium hover:bg-gray-300" data-filter="new">
                        Terbaru
                    </button>
                </div>
                
                <div class="flex items-center gap-2">
                    <label class="text-sm text-gray-600">Urutkan:</label>
                    <select id="sortCategories" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                        <option value="name">Nama A-Z</option>
                        <option value="events">Jumlah Event</option>
                        <option value="popular">Popularitas</option>
                    </select>
                </div>
            </div>

            <!-- Categories Grid -->
            <div id="categoriesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                @foreach($categories as $index => $category)
                <div class="category-card group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden transform hover:-translate-y-1" 
                     data-aos="fade-up" data-aos-delay="{{ $index * 50 }}"
                     data-events="{{ $category->active_events_count ?? 0 }}"
                     data-name="{{ $category->name }}">
                    
                    <!-- Category Header -->
                    <div class="relative h-32 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-br opacity-80" style="background: linear-gradient(135deg, {{ $category->color }}20, {{ $category->color }}40)"></div>
                        
                        <!-- Decorative Elements -->
                        <div class="absolute top-4 right-4 w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm"></div>
                        <div class="absolute bottom-4 left-4 w-8 h-8 rounded-full bg-white/30 backdrop-blur-sm"></div>
                        
                        <!-- Icon -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-16 h-16 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                                <i data-lucide="{{ $category->icon }}" class="w-8 h-8" style="color: {{ $category->color }}"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Category Content -->
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary transition-colors">
                            {{ $category->name }}
                        </h3>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                            {{ $category->description }}
                        </p>
                        
                        <!-- Stats -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-4 text-sm text-gray-500">
                                <span class="flex items-center gap-1">
                                    <i data-lucide="calendar" class="w-4 h-4"></i>
                                    {{ $category->active_events_count ?? 0 }} event
                                </span>
                                @if(isset($category->upcoming_events))
                                <span class="flex items-center gap-1">
                                    <i data-lucide="clock" class="w-4 h-4"></i>
                                    {{ $category->upcoming_events }} mendatang
                                </span>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Action Button -->
                        <a href="{{ route('categories.show', $category->slug) }}" 
                           class="block w-full bg-gradient-to-r from-primary to-secondary text-white text-center py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 group-hover:from-secondary group-hover:to-primary">
                            Jelajahi Event
                        </a>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="hidden text-center py-16">
                <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <i data-lucide="search" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Kategori tidak ditemukan</h3>
                <p class="text-gray-600">Coba gunakan kata kunci yang berbeda atau lihat semua kategori</p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('categorySearch');
    const categoriesGrid = document.getElementById('categoriesGrid');
    const emptyState = document.getElementById('emptyState');
    const filterBtns = document.querySelectorAll('.filter-btn');
    const sortSelect = document.getElementById('sortCategories');
    
    let allCategories = Array.from(document.querySelectorAll('.category-card'));
    let currentFilter = 'all';
    
    // Search functionality
    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        filterAndSort();
    });
    
    // Filter functionality
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => {
                b.classList.remove('active', 'bg-primary', 'text-white');
                b.classList.add('bg-gray-200', 'text-gray-700');
            });
            
            this.classList.add('active', 'bg-primary', 'text-white');
            this.classList.remove('bg-gray-200', 'text-gray-700');
            
            currentFilter = this.dataset.filter;
            filterAndSort();
        });
    });
    
    // Sort functionality
    sortSelect.addEventListener('change', filterAndSort);
    
    function filterAndSort() {
        const query = searchInput.value.toLowerCase();
        const sortBy = sortSelect.value;
        
        let filteredCategories = allCategories.filter(card => {
            const name = card.dataset.name.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();
            const eventsCount = parseInt(card.dataset.events);
            
            // Search filter
            const matchesSearch = query === '' || name.includes(query) || description.includes(query);
            
            // Category filter
            let matchesFilter = true;
            if (currentFilter === 'popular') {
                matchesFilter = eventsCount > 0;
            } else if (currentFilter === 'new') {
                // You can implement logic for new categories here
                matchesFilter = true;
            }
            
            return matchesSearch && matchesFilter;
        });
        
        // Sort categories
        filteredCategories.sort((a, b) => {
            switch (sortBy) {
                case 'events':
                    return parseInt(b.dataset.events) - parseInt(a.dataset.events);
                case 'popular':
                    return parseInt(b.dataset.events) - parseInt(a.dataset.events);
                case 'name':
                default:
                    return a.dataset.name.localeCompare(b.dataset.name);
            }
        });
        
        // Hide all categories
        allCategories.forEach(card => card.style.display = 'none');
        
        // Show filtered categories
        if (filteredCategories.length > 0) {
            filteredCategories.forEach(card => card.style.display = 'block');
            emptyState.classList.add('hidden');
            categoriesGrid.classList.remove('hidden');
        } else {
            emptyState.classList.remove('hidden');
            categoriesGrid.classList.add('hidden');
        }
    }
});
</script>
@endpush
@endsection
