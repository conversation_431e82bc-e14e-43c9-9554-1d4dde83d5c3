@props([
    'href' => null,
    'icon',
    'label',
    'iconColor' => 'blue',
    'onclick' => null,
    'aos' => null,
    'aosDelay' => null
])

@php
    $iconColors = [
        'blue' => 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/40',
        'green' => 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 group-hover:bg-green-200 dark:group-hover:bg-green-900/40',
        'yellow' => 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 group-hover:bg-yellow-200 dark:group-hover:bg-yellow-900/40',
        'red' => 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 group-hover:bg-red-200 dark:group-hover:bg-red-900/40',
        'purple' => 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 group-hover:bg-purple-200 dark:group-hover:bg-purple-900/40',
        'orange' => 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 group-hover:bg-orange-200 dark:group-hover:bg-orange-900/40',
        'pink' => 'bg-pink-100 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400 group-hover:bg-pink-200 dark:group-hover:bg-pink-900/40',
        'indigo' => 'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-900/40',
        'gray' => 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 group-hover:bg-gray-200 dark:group-hover:bg-gray-600',
    ];
    
    $hoverColors = [
        'blue' => 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
        'green' => 'group-hover:text-green-600 dark:group-hover:text-green-400',
        'yellow' => 'group-hover:text-yellow-600 dark:group-hover:text-yellow-400',
        'red' => 'group-hover:text-red-600 dark:group-hover:text-red-400',
        'purple' => 'group-hover:text-purple-600 dark:group-hover:text-purple-400',
        'orange' => 'group-hover:text-orange-600 dark:group-hover:text-orange-400',
        'pink' => 'group-hover:text-pink-600 dark:group-hover:text-pink-400',
        'indigo' => 'group-hover:text-indigo-600 dark:group-hover:text-indigo-400',
        'gray' => 'group-hover:text-gray-700 dark:group-hover:text-gray-300',
    ];
    
    $baseClasses = 'group flex flex-col items-center p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200';
    
    $aosAttributes = '';
    if ($aos) {
        $aosAttributes .= ' data-aos="' . $aos . '"';
        if ($aosDelay) {
            $aosAttributes .= ' data-aos-delay="' . $aosDelay . '"';
        }
    }
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $baseClasses]) }}{!! $aosAttributes !!}>
        <div class="w-12 h-12 {{ $iconColors[$iconColor] ?? $iconColors['blue'] }} rounded-lg flex items-center justify-center transition-colors duration-200">
            <i data-lucide="{{ $icon }}" class="w-6 h-6"></i>
        </div>
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mt-2 {{ $hoverColors[$iconColor] ?? $hoverColors['blue'] }} transition-colors">{{ $label }}</span>
    </a>
@else
    <button {{ $attributes->merge(['class' => $baseClasses]) }} @if($onclick) onclick="{{ $onclick }}" @endif{!! $aosAttributes !!}>
        <div class="w-12 h-12 {{ $iconColors[$iconColor] ?? $iconColors['blue'] }} rounded-lg flex items-center justify-center transition-colors duration-200">
            <i data-lucide="{{ $icon }}" class="w-6 h-6"></i>
        </div>
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mt-2 {{ $hoverColors[$iconColor] ?? $hoverColors['blue'] }} transition-colors">{{ $label }}</span>
    </button>
@endif
