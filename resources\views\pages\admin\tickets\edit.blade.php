@extends('layouts.admin')

@section('title', 'Edit Ticket - Admin Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Ticket</h1>
                    <p class="mt-2 text-gray-600">Update ticket information</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('admin.tickets.show', $ticket->id ?? 1) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                        View Ticket
                    </a>
                    <a href="{{ route('admin.tickets.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to Tickets
                    </a>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="p-6">
                <form action="{{ route('admin.tickets.update', $ticket->id ?? 1) }}" method="POST" class="space-y-6">
                    @csrf
                    @method('PUT')
                    
                    <!-- Event Selection -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="event_id" class="block text-sm font-medium text-gray-700 mb-2">
                                Event <span class="text-red-500">*</span>
                            </label>
                            <select name="event_id" id="event_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select Event</option>
                                @if(isset($events))
                                    @foreach($events as $event)
                                        <option value="{{ $event->id }}" 
                                                {{ (old('event_id', $ticket->event_id ?? '') == $event->id) ? 'selected' : '' }}>
                                            {{ $event->title }} - {{ $event->date }}
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            @error('event_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">
                                User <span class="text-red-500">*</span>
                            </label>
                            <select name="user_id" id="user_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select User</option>
                                @if(isset($users))
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" 
                                                {{ (old('user_id', $ticket->user_id ?? '') == $user->id) ? 'selected' : '' }}>
                                            {{ $user->name }} ({{ $user->email }})
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            @error('user_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Ticket Details -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="ticket_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Ticket Number <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="ticket_number" id="ticket_number" 
                                   value="{{ old('ticket_number', $ticket->ticket_number ?? 'TIK-' . strtoupper(uniqid())) }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            @error('ticket_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                                Price <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-2 text-gray-500">Rp</span>
                                <input type="number" name="price" id="price" 
                                       value="{{ old('price', $ticket->price ?? '') }}" required min="0" step="1000"
                                       class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="status" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="active" {{ old('status', $ticket->status ?? 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="used" {{ old('status', $ticket->status ?? '') == 'used' ? 'selected' : '' }}>Used</option>
                                <option value="expired" {{ old('status', $ticket->status ?? '') == 'expired' ? 'selected' : '' }}>Expired</option>
                                <option value="refunded" {{ old('status', $ticket->status ?? '') == 'refunded' ? 'selected' : '' }}>Refunded</option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="seat_number" class="block text-sm font-medium text-gray-700 mb-2">
                                Seat Number
                            </label>
                            <input type="text" name="seat_number" id="seat_number" 
                                   value="{{ old('seat_number', $ticket->seat_number ?? '') }}" placeholder="e.g., A-15, VIP-01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            @error('seat_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                Category
                            </label>
                            <select name="category" id="category"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select Category</option>
                                <option value="regular" {{ old('category', $ticket->category ?? '') == 'regular' ? 'selected' : '' }}>Regular</option>
                                <option value="vip" {{ old('category', $ticket->category ?? '') == 'vip' ? 'selected' : '' }}>VIP</option>
                                <option value="vvip" {{ old('category', $ticket->category ?? '') == 'vvip' ? 'selected' : '' }}>VVIP</option>
                                <option value="early_bird" {{ old('category', $ticket->category ?? '') == 'early_bird' ? 'selected' : '' }}>Early Bird</option>
                            </select>
                            @error('category')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Notes
                        </label>
                        <textarea name="notes" id="notes" rows="3" 
                                  placeholder="Additional notes about this ticket..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">{{ old('notes', $ticket->notes ?? '') }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Audit Information -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-gray-900 mb-3">Audit Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Created:</span>
                                <span class="text-gray-900 ml-2">
                                    {{ isset($ticket->created_at) ? $ticket->created_at->format('M d, Y H:i') : now()->format('M d, Y H:i') }}
                                </span>
                            </div>
                            <div>
                                <span class="text-gray-600">Last Updated:</span>
                                <span class="text-gray-900 ml-2">
                                    {{ isset($ticket->updated_at) ? $ticket->updated_at->format('M d, Y H:i') : now()->format('M d, Y H:i') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ route('admin.tickets.show', $ticket->id ?? 1) }}" 
                           class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors duration-200">
                            <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                            Update Ticket
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Format price input
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('input', function() {
        // Remove non-numeric characters except for the decimal point
        this.value = this.value.replace(/[^0-9]/g, '');
    });
    
    // Status change warning
    const statusSelect = document.getElementById('status');
    const originalStatus = statusSelect.value;
    
    statusSelect.addEventListener('change', function() {
        if (originalStatus === 'active' && this.value === 'refunded') {
            if (!confirm('Are you sure you want to refund this ticket? This action may trigger a refund process.')) {
                this.value = originalStatus;
            }
        }
        
        if (originalStatus === 'used' && this.value === 'active') {
            if (!confirm('Are you sure you want to reactivate this used ticket?')) {
                this.value = originalStatus;
            }
        }
    });
});
</script>
@endpush
@endsection
