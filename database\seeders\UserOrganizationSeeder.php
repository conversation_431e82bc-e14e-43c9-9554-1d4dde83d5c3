<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;

class UserOrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample organization data for penjual users
        $organizationData = [
            [
                'name' => 'Event Organizer Pro',
                'bio' => 'Profesional event organizer dengan pengalaman lebih dari 10 tahun dalam menyelenggarakan berbagai jenis acara mulai dari konser musik, seminar bisnis, hingga festival budaya.',
                'social_media' => [
                    'instagram' => 'https://instagram.com/eventorganizerpro',
                    'facebook' => 'https://facebook.com/eventorganizerpro',
                    'twitter' => 'https://twitter.com/eventorganizerpro',
                    'website' => 'https://eventorganizerpro.com',
                ],
            ],
            [
                'name' => 'Creative Events Indonesia',
                'bio' => 'Spesialis dalam menyelenggarakan acara kreatif dan inovatif. Kami menghadirkan pengalaman tak terlupakan melalui konsep acara yang unik dan berkesan.',
                'social_media' => [
                    'instagram' => 'https://instagram.com/creativeeventsidn',
                    'facebook' => 'https://facebook.com/creativeeventsidn',
                    'linkedin' => 'https://linkedin.com/company/creative-events-indonesia',
                    'youtube' => 'https://youtube.com/@creativeeventsidn',
                ],
            ],
            [
                'name' => 'Jakarta Music Festival',
                'bio' => 'Penyelenggara festival musik terbesar di Jakarta. Menghadirkan artis lokal dan internasional dalam satu panggung yang spektakuler.',
                'social_media' => [
                    'instagram' => 'https://instagram.com/jakartamusicfest',
                    'facebook' => 'https://facebook.com/jakartamusicfest',
                    'twitter' => 'https://twitter.com/jakartamusicfest',
                    'tiktok' => 'https://tiktok.com/@jakartamusicfest',
                    'youtube' => 'https://youtube.com/@jakartamusicfest',
                ],
            ],
            [
                'name' => 'Tech Conference Asia',
                'bio' => 'Menyelenggarakan konferensi teknologi terdepan di Asia. Menghadirkan pembicara expert dari berbagai perusahaan teknologi global.',
                'social_media' => [
                    'linkedin' => 'https://linkedin.com/company/tech-conference-asia',
                    'twitter' => 'https://twitter.com/techconfasia',
                    'website' => 'https://techconference.asia',
                    'youtube' => 'https://youtube.com/@techconfasia',
                ],
            ],
            [
                'name' => 'Bali Cultural Events',
                'bio' => 'Melestarikan dan mempromosikan budaya Bali melalui berbagai acara seni dan budaya yang autentik dan berkualitas tinggi.',
                'social_media' => [
                    'instagram' => 'https://instagram.com/baliculturalev',
                    'facebook' => 'https://facebook.com/baliculturalev',
                    'website' => 'https://balicultural.events',
                ],
            ],
            [
                'name' => 'Startup Meetup Jakarta',
                'bio' => 'Komunitas startup terbesar di Jakarta. Menyelenggarakan meetup, workshop, dan networking event untuk para entrepreneur.',
                'social_media' => [
                    'instagram' => 'https://instagram.com/startupmeetupjkt',
                    'linkedin' => 'https://linkedin.com/company/startup-meetup-jakarta',
                    'twitter' => 'https://twitter.com/startupmeetupjkt',
                    'website' => 'https://startupmeetup.jakarta.id',
                ],
            ],
        ];

        // Get all penjual users
        $penjualUsers = User::where('role', 'penjual')->get();

        if ($penjualUsers->count() === 0) {
            $this->command->warn('No penjual users found. Creating sample penjual users...');
            
            // Create sample penjual users if none exist
            $samplePenjualUsers = [
                [
                    'name' => 'Ahmad Rizki',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '081234567890',
                    'role' => 'penjual',
                    'is_active' => true,
                ],
                [
                    'name' => 'Sari Dewi',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '081234567891',
                    'role' => 'penjual',
                    'is_active' => true,
                ],
                [
                    'name' => 'Budi Santoso',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '081234567892',
                    'role' => 'penjual',
                    'is_active' => true,
                ],
                [
                    'name' => 'Maya Putri',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '081234567893',
                    'role' => 'penjual',
                    'is_active' => true,
                ],
                [
                    'name' => 'Kadek Wayan',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '081234567894',
                    'role' => 'penjual',
                    'is_active' => true,
                ],
                [
                    'name' => 'Rina Sari',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'phone' => '081234567895',
                    'role' => 'penjual',
                    'is_active' => true,
                ],
            ];

            foreach ($samplePenjualUsers as $userData) {
                User::create($userData);
            }

            $penjualUsers = User::where('role', 'penjual')->get();
        }

        // Update penjual users with organization data
        foreach ($penjualUsers as $index => $user) {
            if (isset($organizationData[$index])) {
                $orgData = $organizationData[$index];
                
                $user->update([
                    'organization' => $orgData['name'],
                    'bio' => $orgData['bio'],
                    'social_media' => $orgData['social_media'],
                ]);

                $this->command->info("✅ Updated {$user->name} with organization: {$orgData['name']}");
            } else {
                // For additional users, create generic organization data
                $genericOrgData = [
                    'organization' => $user->name . ' Events',
                    'bio' => 'Event organizer profesional yang berpengalaman dalam menyelenggarakan berbagai jenis acara berkualitas tinggi.',
                    'social_media' => [
                        'instagram' => 'https://instagram.com/' . strtolower(str_replace(' ', '', $user->name)) . 'events',
                        'website' => 'https://' . strtolower(str_replace(' ', '', $user->name)) . 'events.com',
                    ],
                ];

                $user->update($genericOrgData);
                $this->command->info("✅ Updated {$user->name} with generic organization data");
            }
        }

        $this->command->info('🎉 User organization seeder completed successfully!');
        $this->command->info("📊 Updated {$penjualUsers->count()} penjual users with organization and social media data");
        
        // Display summary
        $this->command->info('📋 Organization Summary:');
        foreach ($penjualUsers as $user) {
            $socialCount = count($user->active_social_media);
            $this->command->info("   • {$user->name} ({$user->organization}) - {$socialCount} social media links");
        }
    }
}
