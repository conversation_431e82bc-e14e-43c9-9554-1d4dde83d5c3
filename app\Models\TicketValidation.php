<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TicketValidation extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_id',
        'validated_by',
        'status',
        'validated_at',
        'notes',
    ];

    protected $casts = [
        'validated_at' => 'datetime',
    ];

    /**
     * Status constants
     */
    public const STATUS_VALID = 'valid';
    public const STATUS_INVALID = 'invalid';

    /**
     * Ticket relationship
     */
    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    /**
     * Validator relationship
     */
    public function validator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    /**
     * Scope for valid validations
     */
    public function scopeValid($query)
    {
        return $query->where('status', self::STATUS_VALID);
    }

    /**
     * Scope for invalid validations
     */
    public function scopeInvalid($query)
    {
        return $query->where('status', self::STATUS_INVALID);
    }

    /**
     * Scope for today's validations
     */
    public function scopeToday($query)
    {
        return $query->whereDate('validated_at', today());
    }

    /**
     * Scope for specific validator
     */
    public function scopeByValidator($query, $validatorId)
    {
        return $query->where('validated_by', $validatorId);
    }

    /**
     * Check if validation is valid
     */
    public function isValid(): bool
    {
        return $this->status === self::STATUS_VALID;
    }

    /**
     * Check if validation is invalid
     */
    public function isInvalid(): bool
    {
        return $this->status === self::STATUS_INVALID;
    }
}
