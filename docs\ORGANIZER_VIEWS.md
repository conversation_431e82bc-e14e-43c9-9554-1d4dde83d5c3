# Organizer Views Documentation

## Overview

This document describes the organizer views for event management in the TiXara application.

## Views Structure

```
resources/views/organizer/tickets/
├── index.blade.php    # Event listing dashboard
├── create.blade.php   # Create new event form
├── show.blade.php     # Event details view
└── edit.blade.php     # Edit event form
```

## 1. Event Listing (index.blade.php)

**Route**: `/organizer/tickets`  
**Controller**: `OrganizerEventController@index`

### Features:
- **Statistics Cards**: Total, Published, Draft, Completed events
- **Advanced Filters**: Search by title/venue, status filter
- **Data Table**: Event listing with:
  - Event poster thumbnail
  - Category badges
  - Status indicators
  - Capacity tracking
  - Price display
  - Action buttons (View/Edit/Publish)
- **Pagination**: Built-in pagination support
- **Empty State**: Helpful empty state with call-to-action
- **Responsive Design**: Mobile-friendly layout

### Usage:
```php
// Controller method
public function index(Request $request)
{
    $query = auth()->user()->organizedEvents()->with('category');
    
    // Apply filters
    if ($request->filled('status')) {
        $query->where('status', $request->status);
    }
    
    // Search functionality
    if ($request->filled('search')) {
        $search = $request->search;
        $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('venue_name', 'like', "%{$search}%");
        });
    }
    
    $tickets = $query->orderBy('created_at', 'desc')
        ->paginate(10)
        ->withQueryString();
    
    return view('organizer.tickets.index', compact('tickets', 'stats'));
}
```

## 2. Event Details (show.blade.php)

**Route**: `/organizer/tickets/{event}`  
**Controller**: `OrganizerEventController@show`

### Features:
- **Event Overview**: Full event information with poster
- **Event Details**: Comprehensive event information display
- **Quick Stats Sidebar**: Views, tickets sold, revenue, creation date
- **Quick Actions**: View public page, copy link, share, analytics, QR code
- **Event Status**: Real-time status indicators
- **Gallery Display**: Image gallery with modal view
- **Interactive Elements**: Copy to clipboard, share functionality

### Key Sections:
1. **Header**: Title, status, action buttons
2. **Event Overview**: Poster, description, category, price
3. **Event Details**: Date/time, venue, capacity, settings
4. **Gallery**: Image gallery (if available)
5. **Sidebar**: Stats, quick actions, status information

### JavaScript Features:
```javascript
// Copy event link to clipboard
function copyEventLink() {
    const eventUrl = '{{ route("events.show", $event->slug) }}';
    navigator.clipboard.writeText(eventUrl);
}

// Share event using Web Share API
function shareEvent() {
    const eventData = {
        title: '{{ $event->title }}',
        text: '{{ Str::limit($event->description, 100) }}',
        url: '{{ route("events.show", $event->slug) }}'
    };
    
    if (navigator.share) {
        navigator.share(eventData);
    } else {
        copyEventLink(); // Fallback
    }
}

// Image modal functionality
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
}
```

## 3. Create Event (create.blade.php)

**Route**: `/organizer/tickets/create`  
**Controller**: `OrganizerEventController@create`

### Features:
- **Multi-section Form**: Organized into logical sections
- **Real-time Validation**: Client-side and server-side validation
- **File Upload**: Poster (required) and gallery (optional)
- **Interactive Elements**: Free event toggle, date validation
- **Responsive Design**: Mobile-friendly form layout

### Form Sections:
1. **Basic Information**: Title, category, price, description
2. **Venue Information**: Venue details, address, capacity
3. **Date & Time**: Event dates, sale periods
4. **Media**: Poster and gallery uploads
5. **Settings**: Approval requirements

### Validation Rules:
```php
$validator = Validator::make($request->all(), [
    'title' => 'required|string|max:255',
    'description' => 'required|string',
    'category_id' => 'required|exists:categories,id',
    'venue_name' => 'required|string|max:255',
    'venue_address' => 'required|string',
    'city' => 'required|string|max:100',
    'province' => 'required|string|max:100',
    'start_date' => 'required|date|after:now',
    'end_date' => 'required|date|after:start_date',
    'price' => 'required|numeric|min:0',
    'total_capacity' => 'required|integer|min:1',
    'poster' => 'required|image|mimes:jpeg,png,jpg|max:2048',
    'gallery.*' => 'image|mimes:jpeg,png,jpg|max:2048',
]);
```

## 4. Edit Event (edit.blade.php)

**Route**: `/organizer/tickets/{event}/edit`  
**Controller**: `OrganizerEventController@edit`

### Features:
- **Pre-filled Form**: All fields populated with current values
- **Current Media Display**: Shows current poster and gallery
- **Conditional Updates**: Optional file uploads
- **Capacity Warnings**: Shows sold tickets warning
- **Gallery Management**: Remove existing gallery images
- **Action Buttons**: Save as draft or update

### Special Considerations:
- **Capacity Changes**: Warns if tickets already sold
- **Media Updates**: Optional poster/gallery updates
- **Date Validation**: Flexible validation for existing events
- **Gallery Management**: Remove individual gallery images

### JavaScript Features:
```javascript
// Auto-disable price field when free is checked
document.querySelector('input[name="is_free"]').addEventListener('change', function() {
    const priceField = document.querySelector('input[name="price"]');
    if (this.checked) {
        priceField.value = '0';
        priceField.disabled = true;
    } else {
        priceField.disabled = false;
    }
});

// Remove gallery image
function removeGalleryImage(index) {
    if (confirm('Are you sure you want to remove this image?')) {
        // Hide the image and add hidden input to track removal
        event.target.closest('.relative').style.display = 'none';
        
        const form = document.querySelector('form');
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'remove_gallery[]';
        hiddenInput.value = index;
        form.appendChild(hiddenInput);
    }
}
```

## Authorization

All views use Laravel's authorization system through the `EventPolicy`:

```php
// In controller methods
$this->authorize('view', $event);    // For show
$this->authorize('update', $event);  // For edit/update
$this->authorize('delete', $event);  // For delete
```

### Policy Rules:
- **Organizers**: Can only manage their own events
- **Admins**: Can manage all events
- **Restrictions**: Cannot edit/delete events with sold tickets or that have started

## Error Handling

All views include comprehensive error handling:

```php
try {
    // Event operations
    $event->update($data);
    return redirect()->route('organizer.tickets.show', $event)
        ->with('success', 'Event updated successfully!');
} catch (\Exception $e) {
    return back()->with('error', 'Error: ' . $e->getMessage())
        ->withInput();
}
```

## Responsive Design

All views are fully responsive with:
- **Mobile-first approach**
- **Flexible grid layouts**
- **Touch-friendly buttons**
- **Collapsible sections**
- **Horizontal scroll for tables**

## Accessibility

Views include accessibility features:
- **Semantic HTML**
- **ARIA labels**
- **Keyboard navigation**
- **Screen reader support**
- **High contrast support**

## Performance

Optimizations included:
- **Lazy loading for images**
- **Pagination for large datasets**
- **Efficient database queries**
- **Compressed images**
- **Minimal JavaScript**

## Browser Support

Compatible with:
- **Chrome 80+**
- **Firefox 75+**
- **Safari 13+**
- **Edge 80+**
- **Mobile browsers**

## Future Enhancements

Planned improvements:
- **Real-time analytics**
- **Bulk operations**
- **Advanced filtering**
- **Export functionality**
- **Event templates**
