<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UserLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing users with default level data
        $users = User::whereIn('role', ['admin', 'penjual'])->get();

        foreach ($users as $user) {
            // Calculate basic statistics
            $totalEvents = $user->organizedEvents()->count();
            $totalTicketsSold = $user->organizedEvents()
                ->withCount(['tickets' => function($query) {
                    $query->where('status', '!=', 'cancelled');
                }])
                ->get()
                ->sum('tickets_count');
            
            $totalRevenue = $user->organizedEvents()
                ->with(['tickets' => function($query) {
                    $query->where('status', '!=', 'cancelled');
                }])
                ->get()
                ->sum(function($event) {
                    return $event->tickets->sum('price');
                });

            // Determine appropriate level based on performance
            $level = $this->calculateUserLevel($totalEvents, $totalTicketsSold, $totalRevenue);
            
            // Calculate level score
            $levelScore = $this->calculateLevelScore($totalEvents, $totalTicketsSold, $totalRevenue);

            // Update user
            $user->update([
                'user_level' => $level,
                'total_events_created' => $totalEvents,
                'total_tickets_sold' => $totalTicketsSold,
                'total_revenue' => $totalRevenue,
                'level_score' => $levelScore,
                'level_upgraded_at' => now(),
            ]);

            $this->command->info("Updated user {$user->name} to level {$level}");
        }

        // Update pembeli users to star level
        User::where('role', 'pembeli')
            ->update([
                'user_level' => 'star',
                'total_events_created' => 0,
                'total_tickets_sold' => 0,
                'total_revenue' => 0,
                'level_score' => 0,
                'level_upgraded_at' => now(),
            ]);

        $this->command->info('Updated all pembeli users to star level');
    }

    /**
     * Calculate appropriate user level based on performance
     */
    private function calculateUserLevel(int $totalEvents, int $totalTicketsSold, float $totalRevenue): string
    {
        $levels = config('user_levels.levels');
        
        // Start from highest level and work down
        $levelKeys = array_reverse(array_keys($levels));
        
        foreach ($levelKeys as $levelKey) {
            $requirements = $levels[$levelKey]['requirements'] ?? [];
            
            $qualifies = true;
            
            if (isset($requirements['events_created']) && $totalEvents < $requirements['events_created']) {
                $qualifies = false;
            }
            
            if (isset($requirements['tickets_sold']) && $totalTicketsSold < $requirements['tickets_sold']) {
                $qualifies = false;
            }
            
            if (isset($requirements['revenue']) && $totalRevenue < $requirements['revenue']) {
                $qualifies = false;
            }
            
            if ($qualifies) {
                return $levelKey;
            }
        }
        
        // Default to star if no level qualifies
        return 'star';
    }

    /**
     * Calculate level score based on performance
     */
    private function calculateLevelScore(int $totalEvents, int $totalTicketsSold, float $totalRevenue): float
    {
        $score = 0;

        // Events created (20% weight)
        $score += ($totalEvents * 10) * 0.2;

        // Tickets sold (30% weight)
        $score += ($totalTicketsSold * 2) * 0.3;

        // Revenue (40% weight) - convert to millions for scoring
        $score += ($totalRevenue / 1000000) * 0.4;

        // Base score (10% weight)
        $score += 10 * 0.1;

        return round($score, 2);
    }
}
