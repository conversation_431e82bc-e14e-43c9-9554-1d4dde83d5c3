{{--
/**
 * My Reports Page
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: <PERSON><PERSON><PERSON> Nazula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */
--}}

@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><PERSON><PERSON><PERSON></h1>
                    <p class="text-gray-600 dark:text-gray-400">Pantau status laporan yang telah Anda kirimkan</p>
                </div>
                <a href="{{ route('reports.index') }}" class="inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                    Buat Laporan Baru
                </a>
            </div>
        </div>

        @if($reports->count() > 0)
            <!-- Reports List -->
            <div class="space-y-6">
                @foreach($reports as $report)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-300">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div class="flex-1">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                        {{ $report->title }}
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ $report->report_number }} • {{ $report->created_at->format('d M Y, H:i') }}
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $report->type_color }}-100 text-{{ $report->type_color }}-800 dark:bg-{{ $report->type_color }}-900/20 dark:text-{{ $report->type_color }}-400">
                                        {{ $report->type_label }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $report->priority_color }}-100 text-{{ $report->priority_color }}-800 dark:bg-{{ $report->priority_color }}-900/20 dark:text-{{ $report->priority_color }}-400">
                                        {{ $report->priority_label }}
                                    </span>
                                </div>
                            </div>
                            
                            <p class="text-gray-700 dark:text-gray-300 mb-4 line-clamp-2">
                                {{ Str::limit($report->description, 150) }}
                            </p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-{{ $report->status_color }}-100 text-{{ $report->status_color }}-800 dark:bg-{{ $report->status_color }}-900/20 dark:text-{{ $report->status_color }}-400">
                                        <div class="w-2 h-2 bg-{{ $report->status_color }}-400 rounded-full mr-2"></div>
                                        {{ $report->status_label }}
                                    </span>
                                    
                                    @if($report->resolved_at)
                                        <span class="text-sm text-gray-500 dark:text-gray-400">
                                            Diselesaikan: {{ $report->resolved_at->format('d M Y') }}
                                        </span>
                                    @endif
                                </div>
                                
                                <a href="{{ route('reports.show', $report) }}" class="inline-flex items-center text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 font-medium">
                                    Lihat Detail
                                    <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $reports->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="flag" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Belum Ada Laporan
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                    Anda belum pernah mengirimkan laporan. Jika menemukan masalah atau pelanggaran, jangan ragu untuk melaporkannya.
                </p>
                <a href="{{ route('reports.index') }}" class="inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                    Buat Laporan Pertama
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
@endpush
