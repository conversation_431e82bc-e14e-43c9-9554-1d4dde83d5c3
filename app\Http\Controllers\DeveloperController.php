<?php

/**
 * Developer Controller
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: <PERSON>ha<PERSON>zula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DeveloperController extends Controller
{
    /**
     * Show developer page
     */
    public function index()
    {
        $developer = [
            'name' => 'Dhafa Nazula P',
            'title' => 'Full Stack Developer',
            'bio' => 'Passionate developer dengan pengalaman dalam membangun aplikasi web modern menggunakan Laravel, Vue.js, dan teknologi terkini lainnya.',
            'experience_years' => 5,
            'projects_completed' => 50,
            'technologies' => [
                'frontend' => [
                    'Laravel Blade',
                    'Vue.js',
                    'React.js',
                    'Tailwind CSS',
                    'Alpine.js',
                    'JavaScript ES6+',
                    'TypeScript',
                    'SASS/SCSS'
                ],
                'backend' => [
                    'Laravel',
                    'PHP',
                    'Node.js',
                    'Express.js',
                    'RESTful API',
                    'GraphQL',
                    'Laravel Sanctum',
                    'JWT Authentication'
                ],
                'database' => [
                    'MySQL',
                    'PostgreSQL',
                    'MongoDB',
                    'Redis',
                    'Elasticsearch',
                    'Database Design',
                    'Query Optimization',
                    'Data Migration'
                ],
                'tools' => [
                    'Git & GitHub',
                    'Docker',
                    'AWS',
                    'DigitalOcean',
                    'Nginx',
                    'Apache',
                    'CI/CD',
                    'Testing (PHPUnit, Jest)'
                ]
            ],
            'social_links' => [
                'instagram' => 'https://instagram.com/seehai.dhafa',
                'github' => 'https://github.com/dhafanazula',
                'linkedin' => 'https://linkedin.com/in/dhafanazula',
                'email' => '<EMAIL>'
            ]
        ];

        $company = [
            'name' => 'BintangCode',
            'parent_company' => 'CV Bintang Gumilang Group',
            'description' => 'BintangCode adalah sub holding dari CV Bintang Gumilang Group yang fokus pada pengembangan solusi teknologi inovatif untuk berbagai industri.',
            'established' => 2019,
            'projects_completed' => 100,
            'happy_clients' => 150,
            'services' => [
                'Web Development',
                'Mobile App Development',
                'E-commerce Solutions',
                'Custom Software Development',
                'UI/UX Design',
                'Digital Marketing',
                'Cloud Solutions',
                'Technical Consulting'
            ]
        ];

        $timeline = [
            [
                'period' => 'Q1 2024',
                'title' => 'Project Initiation',
                'description' => 'Memulai pengembangan TiXara dengan konsep platform ticketing yang user-friendly dan modern.',
                'achievements' => [
                    'Project planning dan requirement analysis',
                    'Technology stack selection',
                    'Database design dan architecture',
                    'Initial prototype development'
                ]
            ],
            [
                'period' => 'Q2 2024',
                'title' => 'Core Features Development',
                'description' => 'Mengembangkan fitur inti seperti user management, event creation, ticket booking, dan payment system.',
                'achievements' => [
                    'User authentication dan authorization',
                    'Event management system',
                    'Ticket booking workflow',
                    'Payment gateway integration',
                    'Email notification system'
                ]
            ],
            [
                'period' => 'Q3 2024',
                'title' => 'Advanced Features',
                'description' => 'Menambahkan fitur lanjutan seperti analytics dashboard, notification system, dan multi-role management.',
                'achievements' => [
                    'Analytics dan reporting dashboard',
                    'Real-time notifications',
                    'Multi-role user management',
                    'Advanced search dan filtering',
                    'Mobile responsive optimization'
                ]
            ],
            [
                'period' => 'Q4 2024',
                'title' => 'ArtPosure Marketing Suite',
                'description' => 'Meluncurkan ArtPosure - layanan marketing terintegrasi untuk event organizer dengan berbagai paket dan add-on.',
                'achievements' => [
                    'Social media marketing tools',
                    'Website placement system',
                    'Marketing package management',
                    'Add-on services integration',
                    'Analytics dan performance tracking'
                ]
            ]
        ];

        return view('pages.developer', compact('developer', 'company', 'timeline'));
    }

    /**
     * Get developer information API
     */
    public function getDeveloperInfo()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'developer' => [
                    'name' => 'Dhafa Nazula P',
                    'title' => 'Full Stack Developer',
                    'bio' => 'Passionate developer dengan pengalaman dalam membangun aplikasi web modern.',
                    'experience_years' => 5,
                    'location' => 'Jakarta, Indonesia',
                    'specialization' => [
                        'Laravel Development',
                        'Vue.js Applications',
                        'API Development',
                        'Database Design',
                        'UI/UX Implementation'
                    ]
                ],
                'company' => [
                    'name' => 'BintangCode',
                    'parent' => 'CV Bintang Gumilang Group',
                    'type' => 'Software Development Company',
                    'established' => 2019,
                    'location' => 'Jakarta, Indonesia'
                ],
                'contact' => [
                    'email' => '<EMAIL>',
                    'instagram' => '@seehai.dhafa',
                    'github' => 'dhafanazula',
                    'linkedin' => 'dhafanazula'
                ]
            ]
        ]);
    }

    /**
     * Get technology stack information
     */
    public function getTechStack()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'frontend' => [
                    'framework' => 'Laravel Blade',
                    'styling' => 'Tailwind CSS',
                    'javascript' => 'Alpine.js',
                    'icons' => 'Lucide Icons',
                    'build_tools' => 'Vite'
                ],
                'backend' => [
                    'framework' => 'Laravel 10',
                    'language' => 'PHP 8.2',
                    'api' => 'RESTful API',
                    'authentication' => 'Laravel Sanctum',
                    'validation' => 'Form Requests'
                ],
                'database' => [
                    'primary' => 'MySQL 8.0',
                    'cache' => 'Redis',
                    'orm' => 'Eloquent ORM',
                    'migrations' => 'Laravel Migrations',
                    'seeding' => 'Database Seeders'
                ],
                'tools' => [
                    'version_control' => 'Git & GitHub',
                    'containerization' => 'Docker',
                    'package_manager' => 'Composer & NPM',
                    'testing' => 'PHPUnit',
                    'deployment' => 'CI/CD Pipeline'
                ],
                'architecture' => [
                    'pattern' => 'MVC (Model-View-Controller)',
                    'design' => 'Repository Pattern',
                    'api' => 'RESTful Architecture',
                    'security' => 'CSRF Protection, XSS Prevention',
                    'performance' => 'Query Optimization, Caching'
                ]
            ]
        ]);
    }

    /**
     * Get project statistics
     */
    public function getProjectStats()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'development' => [
                    'total_files' => 450,
                    'lines_of_code' => 25000,
                    'commits' => 380,
                    'features_implemented' => 85,
                    'bugs_fixed' => 120
                ],
                'performance' => [
                    'page_load_time' => '< 2 seconds',
                    'mobile_score' => 95,
                    'desktop_score' => 98,
                    'accessibility_score' => 92,
                    'seo_score' => 96
                ],
                'testing' => [
                    'unit_tests' => 150,
                    'feature_tests' => 80,
                    'test_coverage' => '85%',
                    'browser_compatibility' => '98%'
                ],
                'security' => [
                    'vulnerability_scans' => 'Weekly',
                    'security_score' => 'A+',
                    'ssl_rating' => 'A+',
                    'data_encryption' => 'AES-256'
                ]
            ]
        ]);
    }

    /**
     * Submit message to developer
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'type' => 'required|string|in:collaboration,technical,feedback,other'
        ]);

        try {
            // Log developer message
            \Log::info('Developer message received', [
                'name' => $request->name,
                'email' => $request->email,
                'subject' => $request->subject,
                'type' => $request->type,
                'message' => $request->message,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // In real implementation, send email to developer
            // Mail::to('<EMAIL>')->send(new DeveloperMessage($request->all()));

            return response()->json([
                'success' => true,
                'message' => 'Terima kasih! Pesan Anda telah terkirim ke developer. Kami akan merespons dalam 24 jam.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to send developer message', [
                'error' => $e->getMessage(),
                'email' => $request->email,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengirim pesan. Silakan coba lagi atau hubungi langsung melalui email.'
            ], 500);
        }
    }
}
