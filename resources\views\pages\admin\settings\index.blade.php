@extends('layouts.admin')

@section('title', 'System Settings - Admin TiXara')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">System Settings</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Manage application configuration and system maintenance</p>
                </div>
                <div class="flex space-x-3">
                    <form action="{{ route('admin.settings.optimize') }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" 
                                class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                                onclick="return confirm('This will optimize the application. Continue?')">
                            <i data-lucide="zap" class="w-4 h-4 inline mr-2"></i>
                            Optimize App
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Settings -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Application Settings -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Application Settings</h2>
                    
                    <form action="{{ route('admin.settings.update-app') }}" method="POST">
                        @csrf
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="app_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Application Name</label>
                                <input type="text" 
                                       id="app_name" 
                                       name="app_name" 
                                       value="{{ $settings['app_name'] }}"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="app_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Application URL</label>
                                <input type="url" 
                                       id="app_url" 
                                       name="app_url" 
                                       value="{{ $settings['app_url'] }}"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="app_timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Timezone</label>
                                <select id="app_timezone" 
                                        name="app_timezone"
                                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="Asia/Jakarta" {{ $settings['app_timezone'] == 'Asia/Jakarta' ? 'selected' : '' }}>Asia/Jakarta (WIB)</option>
                                    <option value="Asia/Makassar" {{ $settings['app_timezone'] == 'Asia/Makassar' ? 'selected' : '' }}>Asia/Makassar (WITA)</option>
                                    <option value="Asia/Jayapura" {{ $settings['app_timezone'] == 'Asia/Jayapura' ? 'selected' : '' }}>Asia/Jayapura (WIT)</option>
                                    <option value="UTC" {{ $settings['app_timezone'] == 'UTC' ? 'selected' : '' }}>UTC</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                                <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                                Save Application Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Mail Settings -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Mail Settings</h2>
                    
                    <form action="{{ route('admin.settings.update-mail') }}" method="POST">
                        @csrf
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="mail_driver" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Mail Driver</label>
                                <select id="mail_driver" 
                                        name="mail_driver"
                                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="smtp" {{ $settings['mail_driver'] == 'smtp' ? 'selected' : '' }}>SMTP</option>
                                    <option value="sendmail" {{ $settings['mail_driver'] == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                                    <option value="mailgun" {{ $settings['mail_driver'] == 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                                    <option value="ses" {{ $settings['mail_driver'] == 'ses' ? 'selected' : '' }}>Amazon SES</option>
                                    <option value="log" {{ $settings['mail_driver'] == 'log' ? 'selected' : '' }}>Log (Testing)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="mail_from_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Address</label>
                                <input type="email" 
                                       id="mail_from_address" 
                                       name="mail_from_address" 
                                       value="{{ $settings['mail_from_address'] }}"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_from_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Name</label>
                                <input type="text" 
                                       id="mail_from_name" 
                                       name="mail_from_name" 
                                       value="{{ $settings['mail_from_name'] }}"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_host" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Host</label>
                                <input type="text" 
                                       id="mail_host" 
                                       name="mail_host" 
                                       placeholder="smtp.gmail.com"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Port</label>
                                <input type="number" 
                                       id="mail_port" 
                                       name="mail_port" 
                                       placeholder="587"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Username</label>
                                <input type="text" 
                                       id="mail_username" 
                                       name="mail_username" 
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Password</label>
                                <input type="password" 
                                       id="mail_password" 
                                       name="mail_password" 
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_encryption" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Encryption</label>
                                <select id="mail_encryption" 
                                        name="mail_encryption"
                                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="">None</option>
                                    <option value="tls">TLS</option>
                                    <option value="ssl">SSL</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                                <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                                Save Mail Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Cache Management -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Cache Management</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <form action="{{ route('admin.settings.clear-cache') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="config">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center">
                                <i data-lucide="settings" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear Config</div>
                                <div class="text-xs opacity-75">{{ $cacheInfo['config_cached'] ? 'Cached' : 'Not Cached' }}</div>
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.settings.clear-cache') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="route">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-center">
                                <i data-lucide="route" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear Routes</div>
                                <div class="text-xs opacity-75">{{ $cacheInfo['routes_cached'] ? 'Cached' : 'Not Cached' }}</div>
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.settings.clear-cache') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="view">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 text-center">
                                <i data-lucide="eye" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear Views</div>
                                <div class="text-xs opacity-75">{{ $cacheInfo['views_cached'] ? 'Cached' : 'Not Cached' }}</div>
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.settings.clear-cache') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="all">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 text-center">
                                <i data-lucide="trash-2" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear All</div>
                                <div class="text-xs opacity-75">All Caches</div>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Storage Management -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Storage Management</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <form action="{{ route('admin.settings.clean-storage') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="logs">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete all log files. Continue?')">
                                <i data-lucide="file-text" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Logs</div>
                                <div class="text-xs opacity-75">{{ $storageInfo['logs_size'] }}</div>
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.settings.clean-storage') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="cache">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete cache files. Continue?')">
                                <i data-lucide="database" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Cache</div>
                                <div class="text-xs opacity-75">{{ $storageInfo['cache_size'] }}</div>
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.settings.clean-storage') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="sessions">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete session files. Continue?')">
                                <i data-lucide="users" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Sessions</div>
                                <div class="text-xs opacity-75">{{ $storageInfo['sessions_size'] }}</div>
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.settings.clean-storage') }}" method="POST" class="inline">
                            @csrf
                            <input type="hidden" name="type" value="temp">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete temporary files. Continue?')">
                                <i data-lucide="folder-x" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Temp</div>
                                <div class="text-xs opacity-75">Temp Files</div>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- System Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Information</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">PHP Version</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $systemInfo['php_version'] }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Laravel Version</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $systemInfo['laravel_version'] }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Environment</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ ucfirst($systemInfo['environment']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Debug Mode</span>
                            <span class="text-sm font-medium {{ $systemInfo['debug_mode'] ? 'text-red-600' : 'text-green-600' }}">
                                {{ $systemInfo['debug_mode'] ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Database</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ ucfirst($systemInfo['database_connection']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Storage</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ ucfirst($systemInfo['storage_disk']) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Storage Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Storage Information</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Space</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $storageInfo['total_space'] }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Free Space</span>
                            <span class="text-sm font-medium text-green-600">{{ $storageInfo['free_space'] }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Used Space</span>
                            <span class="text-sm font-medium text-orange-600">{{ $storageInfo['used_space'] }}</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <form action="{{ route('admin.settings.clear-cache') }}" method="POST">
                            @csrf
                            <button type="submit" 
                                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                                Clear All Cache
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.settings.optimize') }}" method="POST">
                            @csrf
                            <button type="submit" 
                                    class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                    onclick="return confirm('This will optimize the application. Continue?')">
                                <i data-lucide="zap" class="w-4 h-4 inline mr-2"></i>
                                Optimize Application
                            </button>
                        </form>
                        
                        <a href="{{ route('admin.dashboard') }}" 
                           class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors inline-block text-center">
                            <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
