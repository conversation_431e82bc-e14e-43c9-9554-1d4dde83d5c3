@extends('layouts.admin')

@section('title', $title)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $title }}</h1>
            <p class="mt-1 text-sm text-gray-600">Kelola kategori event yang tersedia di platform</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.categories.create') }}" 
               class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                Tambah Kategori
            </a>
        </div>
    </div>

    <!-- Filters & Search -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
        <form method="GET" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
            <!-- Search -->
            <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari Kategori</label>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="{{ $filters['search'] ?? '' }}"
                       placeholder="Nama atau deskripsi kategori..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>

            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" id="status" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="">Semua Status</option>
                    <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Aktif</option>
                    <option value="inactive" {{ ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' }}>Tidak Aktif</option>
                </select>
            </div>

            <!-- Sort -->
            <div>
                <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Urutkan</label>
                <select name="sort" id="sort" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="sort_order" {{ ($filters['sort'] ?? '') === 'sort_order' ? 'selected' : '' }}>Urutan</option>
                    <option value="name" {{ ($filters['sort'] ?? '') === 'name' ? 'selected' : '' }}>Nama</option>
                    <option value="created_at" {{ ($filters['sort'] ?? '') === 'created_at' ? 'selected' : '' }}>Tanggal Dibuat</option>
                </select>
            </div>

            <!-- Direction -->
            <div>
                <label for="direction" class="block text-sm font-medium text-gray-700 mb-1">Arah</label>
                <select name="direction" id="direction" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="asc" {{ ($filters['direction'] ?? '') === 'asc' ? 'selected' : '' }}>A-Z</option>
                    <option value="desc" {{ ($filters['direction'] ?? '') === 'desc' ? 'selected' : '' }}>Z-A</option>
                </select>
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                    <i data-lucide="search" class="w-4 h-4"></i>
                </button>
                <a href="{{ route('admin.categories.index') }}" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Bulk Actions -->
    <div id="bulkActions" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <span class="text-sm font-medium text-blue-900">
                    <span id="selectedCount">0</span> kategori dipilih
                </span>
            </div>
            <div class="flex items-center space-x-2">
                <form method="POST" action="{{ route('admin.categories.bulk-action') }}" class="inline">
                    @csrf
                    <input type="hidden" name="action" value="activate">
                    <input type="hidden" name="categories" id="bulkCategoriesActivate">
                    <button type="submit" class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors">
                        Aktifkan
                    </button>
                </form>
                <form method="POST" action="{{ route('admin.categories.bulk-action') }}" class="inline">
                    @csrf
                    <input type="hidden" name="action" value="deactivate">
                    <input type="hidden" name="categories" id="bulkCategoriesDeactivate">
                    <button type="submit" class="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 transition-colors">
                        Nonaktifkan
                    </button>
                </form>
                <form method="POST" action="{{ route('admin.categories.bulk-action') }}" class="inline" 
                      onsubmit="return confirm('Yakin ingin menghapus kategori terpilih?')">
                    @csrf
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="categories" id="bulkCategoriesDelete">
                    <button type="submit" class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors">
                        Hapus
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary focus:ring-primary">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Kategori
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Deskripsi
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Event
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Urutan
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Aksi
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="categoriesTableBody">
                    @forelse($categories as $category)
                    <tr class="hover:bg-gray-50" data-category-id="{{ $category->id }}">
                        <td class="px-6 py-4">
                            <input type="checkbox" name="selected_categories[]" value="{{ $category->id }}" 
                                   class="category-checkbox rounded border-gray-300 text-primary focus:ring-primary">
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3" 
                                     style="background-color: {{ $category->color }}20;">
                                    <i data-lucide="{{ $category->icon }}" class="w-5 h-5" style="color: {{ $category->color }}"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $category->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $category->slug }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 max-w-xs truncate" title="{{ $category->description }}">
                                {{ $category->description }}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">
                                <span class="font-medium">{{ $category->events_count }}</span> event
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <button onclick="toggleStatus({{ $category->id }})" 
                                    class="status-toggle inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors
                                           {{ $category->is_active ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-red-100 text-red-800 hover:bg-red-200' }}">
                                <span class="w-2 h-2 rounded-full mr-1 {{ $category->is_active ? 'bg-green-400' : 'bg-red-400' }}"></span>
                                {{ $category->is_active ? 'Aktif' : 'Tidak Aktif' }}
                            </button>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">{{ $category->sort_order }}</div>
                        </td>
                        <td class="px-6 py-4 text-right">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{{ route('admin.categories.show', $category) }}" 
                                   class="text-blue-600 hover:text-blue-900 transition-colors" title="Lihat Detail">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <a href="{{ route('admin.categories.edit', $category) }}" 
                                   class="text-yellow-600 hover:text-yellow-900 transition-colors" title="Edit">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </a>
                                @if($category->events_count == 0)
                                <form method="POST" action="{{ route('admin.categories.destroy', $category) }}" 
                                      class="inline" onsubmit="return confirm('Yakin ingin menghapus kategori ini?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900 transition-colors" title="Hapus">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </form>
                                @else
                                <span class="text-gray-400" title="Tidak dapat dihapus karena memiliki event">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </span>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <i data-lucide="folder-x" class="w-12 h-12 text-gray-400 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada kategori</h3>
                                <p class="text-gray-600 mb-4">Belum ada kategori yang dibuat.</p>
                                <a href="{{ route('admin.categories.create') }}" 
                                   class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    Tambah Kategori Pertama
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($categories->hasPages())
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $categories->links() }}
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        categoryCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Individual checkbox functionality
    categoryCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAll();
            updateBulkActions();
        });
    });

    function updateSelectAll() {
        const checkedCount = document.querySelectorAll('.category-checkbox:checked').length;
        const totalCount = categoryCheckboxes.length;
        
        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0) {
            bulkActions.classList.remove('hidden');
            selectedCount.textContent = count;
            
            // Update hidden inputs for bulk actions
            const categoryIds = Array.from(checkedBoxes).map(cb => cb.value);
            document.getElementById('bulkCategoriesActivate').value = JSON.stringify(categoryIds);
            document.getElementById('bulkCategoriesDeactivate').value = JSON.stringify(categoryIds);
            document.getElementById('bulkCategoriesDelete').value = JSON.stringify(categoryIds);
        } else {
            bulkActions.classList.add('hidden');
        }
    }
});

// Toggle status function
function toggleStatus(categoryId) {
    fetch(`/admin/categories/${categoryId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the status badge
            const row = document.querySelector(`tr[data-category-id="${categoryId}"]`);
            const statusButton = row.querySelector('.status-toggle');
            const statusDot = statusButton.querySelector('span');
            
            if (data.is_active) {
                statusButton.className = 'status-toggle inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors bg-green-100 text-green-800 hover:bg-green-200';
                statusButton.innerHTML = '<span class="w-2 h-2 rounded-full mr-1 bg-green-400"></span>Aktif';
            } else {
                statusButton.className = 'status-toggle inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors bg-red-100 text-red-800 hover:bg-red-200';
                statusButton.innerHTML = '<span class="w-2 h-2 rounded-full mr-1 bg-red-400"></span>Tidak Aktif';
            }
            
            // Show success message
            showNotification(data.message, 'success');
        } else {
            showNotification('Gagal mengubah status kategori', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Terjadi kesalahan', 'error');
    });
}

// Show notification function
function showNotification(message, type) {
    // You can implement your notification system here
    alert(message);
}
</script>
@endpush
@endsection
