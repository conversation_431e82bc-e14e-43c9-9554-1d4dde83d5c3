@echo off
echo Fixing Notifications Table Issue...

echo.
echo [1/6] Checking current migration status...
php artisan migrate:status

echo.
echo [2/6] Running the fix migration...
php artisan migrate --path=database/migrations/2024_01_01_000008_fix_notifications_table.php

echo.
echo [3/6] Verifying table structure...
php artisan tinker --execute="
try {
    \$exists = \Illuminate\Support\Facades\Schema::hasTable('notifications');
    echo 'Notifications table exists: ' . (\$exists ? 'YES' : 'NO') . PHP_EOL;
    
    if (\$exists) {
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('notifications');
        echo 'Columns: ' . implode(', ', \$columns) . PHP_EOL;
        
        \$hasUserId = \Illuminate\Support\Facades\Schema::hasColumn('notifications', 'user_id');
        echo 'Has user_id column: ' . (\$hasUserId ? 'YES' : 'NO') . PHP_EOL;
        
        if (\$hasUserId) {
            echo 'SUCCESS: user_id column found!' . PHP_EOL;
        } else {
            echo 'ERROR: user_id column still missing!' . PHP_EOL;
        }
    }
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [4/6] Testing notification creation...
php artisan tinker --execute="
try {
    \$user = \App\Models\User::first();
    if (\$user) {
        \$notification = \App\Models\Notification::create([
            'user_id' => \$user->id,
            'title' => 'Test Notification',
            'message' => 'This is a test notification to verify the fix.',
            'type' => 'system',
            'priority' => 'normal'
        ]);
        echo 'SUCCESS: Test notification created with ID: ' . \$notification->id . PHP_EOL;
        
        // Clean up test notification
        \$notification->delete();
        echo 'Test notification cleaned up.' . PHP_EOL;
    } else {
        echo 'No users found to test with.' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error creating test notification: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/6] Testing notification queries...
php artisan tinker --execute="
try {
    \$user = \App\Models\User::first();
    if (\$user) {
        \$notifications = \$user->notifications()->count();
        echo 'User notifications count: ' . \$notifications . PHP_EOL;
        
        \$unread = \$user->unreadNotifications()->count();
        echo 'Unread notifications count: ' . \$unread . PHP_EOL;
        
        echo 'SUCCESS: Notification queries working!' . PHP_EOL;
    } else {
        echo 'No users found to test with.' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error testing notification queries: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [6/6] Clearing cache...
php artisan cache:clear
php artisan config:clear
php artisan route:clear

echo.
echo ========================================
echo Notifications table fix completed!
echo ========================================
echo.
echo The notifications table should now have the correct structure.
echo You can test the notifications feature in your application.
echo.
pause
