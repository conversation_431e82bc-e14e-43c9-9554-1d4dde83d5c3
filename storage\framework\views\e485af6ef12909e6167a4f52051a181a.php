<?php $__env->startSection('title', 'Pengaturan UangTix'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-cog text-primary me-2"></i>
                Pengaturan UangTix
            </h1>
            <p class="text-muted"><PERSON><PERSON><PERSON> kurs, biaya, dan pengaturan sistem UangTix</p>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('admin.uangtix.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <!-- Exchange Rate Settings -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exchange-alt me-2"></i>
                        Pengaturan Kurs
                    </h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.uangtix.update-settings')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="rate_idr_to_uangtix" class="form-label">Kurs IDR ke UangTix</label>
                                <div class="input-group">
                                    <span class="input-group-text">1 IDR =</span>
                                    <input type="number" class="form-control" id="rate_idr_to_uangtix" 
                                           name="rate_idr_to_uangtix" value="<?php echo e($exchangeRate->rate_idr_to_uangtix); ?>" 
                                           step="0.0001" min="0.0001" max="999999" required>
                                    <span class="input-group-text">UTX</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="rate_uangtix_to_idr" class="form-label">Kurs UangTix ke IDR</label>
                                <div class="input-group">
                                    <span class="input-group-text">1 UTX =</span>
                                    <input type="number" class="form-control" id="rate_uangtix_to_idr" 
                                           name="rate_uangtix_to_idr" value="<?php echo e($exchangeRate->rate_uangtix_to_idr); ?>" 
                                           step="0.0001" min="0.0001" max="999999" required>
                                    <span class="input-group-text">IDR</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="min_deposit_idr" class="form-label">Minimum Deposit (IDR)</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="number" class="form-control" id="min_deposit_idr" 
                                           name="min_deposit_idr" value="<?php echo e($exchangeRate->min_deposit_idr); ?>" 
                                           min="1000" required>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="max_deposit_idr" class="form-label">Maksimum Deposit (IDR)</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp</span>
                                    <input type="number" class="form-control" id="max_deposit_idr" 
                                           name="max_deposit_idr" value="<?php echo e($exchangeRate->max_deposit_idr); ?>" 
                                           min="10000" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="min_withdrawal_uangtix" class="form-label">Minimum Penarikan (UTX)</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="min_withdrawal_uangtix" 
                                           name="min_withdrawal_uangtix" value="<?php echo e($exchangeRate->min_withdrawal_uangtix); ?>" 
                                           min="1" required>
                                    <span class="input-group-text">UTX</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="deposit_fee_percentage" class="form-label">Biaya Deposit (%)</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="deposit_fee_percentage" 
                                           name="deposit_fee_percentage" value="<?php echo e($exchangeRate->deposit_fee_percentage); ?>" 
                                           step="0.01" min="0" max="100" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="withdrawal_fee_percentage" class="form-label">Biaya Penarikan (%)</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="withdrawal_fee_percentage" 
                                           name="withdrawal_fee_percentage" value="<?php echo e($exchangeRate->withdrawal_fee_percentage); ?>" 
                                           step="0.01" min="0" max="100" required>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="deposits_enabled" 
                                           name="deposits_enabled" value="1" <?php echo e($exchangeRate->deposits_enabled ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="deposits_enabled">
                                        Aktifkan Deposit
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="withdrawals_enabled" 
                                           name="withdrawals_enabled" value="1" <?php echo e($exchangeRate->withdrawals_enabled ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="withdrawals_enabled">
                                        Aktifkan Penarikan
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="transfers_enabled" 
                                           name="transfers_enabled" value="1" <?php echo e($exchangeRate->transfers_enabled ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="transfers_enabled">
                                        Aktifkan Transfer
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="terms_and_conditions" class="form-label">Syarat dan Ketentuan</label>
                            <textarea class="form-control" id="terms_and_conditions" name="terms_and_conditions" 
                                      rows="8" placeholder="Masukkan syarat dan ketentuan UangTix..."><?php echo e($exchangeRate->terms_and_conditions); ?></textarea>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Simpan Pengaturan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Settings Preview -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-eye me-2"></i>
                        Preview Pengaturan Saat Ini
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-primary font-weight-bold">Kurs</h6>
                            <div class="small">
                                <div><?php echo e($exchangeRate->formatted_rates['idr_to_uangtix']); ?></div>
                                <div><?php echo e($exchangeRate->formatted_rates['uangtix_to_idr']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-success font-weight-bold">Batas Transaksi</h6>
                            <div class="small">
                                <div>Min Deposit: <?php echo e($exchangeRate->formatted_limits['min_deposit']); ?></div>
                                <div>Max Deposit: <?php echo e($exchangeRate->formatted_limits['max_deposit']); ?></div>
                                <div>Min Penarikan: <?php echo e($exchangeRate->formatted_limits['min_withdrawal']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-warning font-weight-bold">Biaya</h6>
                            <div class="small">
                                <div>Deposit: <?php echo e($exchangeRate->formatted_fees['deposit_fee']); ?></div>
                                <div>Penarikan: <?php echo e($exchangeRate->formatted_fees['withdrawal_fee']); ?></div>
                                <div>Transfer: 0% (Gratis)</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-info font-weight-bold">Status Layanan</h6>
                            <div class="small">
                                <div>
                                    <span class="badge badge-<?php echo e($exchangeRate->deposits_enabled ? 'success' : 'danger'); ?>">
                                        Deposit <?php echo e($exchangeRate->deposits_enabled ? 'Aktif' : 'Nonaktif'); ?>

                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-<?php echo e($exchangeRate->withdrawals_enabled ? 'success' : 'danger'); ?>">
                                        Penarikan <?php echo e($exchangeRate->withdrawals_enabled ? 'Aktif' : 'Nonaktif'); ?>

                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-<?php echo e($exchangeRate->transfers_enabled ? 'success' : 'danger'); ?>">
                                        Transfer <?php echo e($exchangeRate->transfers_enabled ? 'Aktif' : 'Nonaktif'); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Auto-calculate reverse rate
document.getElementById('rate_idr_to_uangtix').addEventListener('input', function() {
    const idrToUtx = parseFloat(this.value);
    if (idrToUtx > 0) {
        const utxToIdr = (1 / idrToUtx).toFixed(4);
        document.getElementById('rate_uangtix_to_idr').value = utxToIdr;
    }
});

document.getElementById('rate_uangtix_to_idr').addEventListener('input', function() {
    const utxToIdr = parseFloat(this.value);
    if (utxToIdr > 0) {
        const idrToUtx = (1 / utxToIdr).toFixed(4);
        document.getElementById('rate_idr_to_uangtix').value = idrToUtx;
    }
});

// Validate max deposit is greater than min deposit
document.getElementById('min_deposit_idr').addEventListener('input', function() {
    const minDeposit = parseInt(this.value);
    const maxDepositField = document.getElementById('max_deposit_idr');
    const maxDeposit = parseInt(maxDepositField.value);
    
    if (maxDeposit <= minDeposit) {
        maxDepositField.value = minDeposit * 10;
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/uangtix/settings.blade.php ENDPATH**/ ?>