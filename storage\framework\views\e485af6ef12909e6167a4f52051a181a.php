<?php $__env->startSection('title', 'Pengaturan UangTix'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="settings" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Pengaturan UangTix</h1>
                            <p class="text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON>, biaya, dan pengaturan sistem UangTix</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="<?php echo e(route('admin.uangtix.index')); ?>"
                       class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Exchange Rate Settings -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <i data-lucide="arrow-right-left" class="w-5 h-5 text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Pengaturan Kurs & Sistem</h2>
                </div>
            </div>

            <div class="p-6">
                <form action="<?php echo e(route('admin.uangtix.update-settings')); ?>" method="POST" class="space-y-8" x-data="settingsForm()">
                    <?php echo csrf_field(); ?>

                    <!-- Exchange Rates Section -->
                    <div class="space-y-6">
                        <div class="flex items-center gap-3 pb-3 border-b border-gray-200 dark:border-gray-700">
                            <div class="w-6 h-6 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="trending-up" class="w-4 h-4 text-yellow-600 dark:text-yellow-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Kurs Mata Uang</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- IDR to UangTix Rate -->
                            <div class="space-y-2">
                                <label for="rate_idr_to_uangtix" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Kurs IDR ke UangTix
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">1 IDR =</span>
                                    </div>
                                    <input type="number"
                                           id="rate_idr_to_uangtix"
                                           name="rate_idr_to_uangtix"
                                           value="<?php echo e($exchangeRate->rate_idr_to_uangtix); ?>"
                                           step="0.0001"
                                           min="0.0001"
                                           max="999999"
                                           required
                                           x-model="rates.idrToUtx"
                                           @input="updateReverseRate('idr')"
                                           class="block w-full pl-16 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">UTX</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Berapa UangTix yang didapat dari 1 IDR</p>
                            </div>

                            <!-- UangTix to IDR Rate -->
                            <div class="space-y-2">
                                <label for="rate_uangtix_to_idr" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Kurs UangTix ke IDR
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">1 UTX =</span>
                                    </div>
                                    <input type="number"
                                           id="rate_uangtix_to_idr"
                                           name="rate_uangtix_to_idr"
                                           value="<?php echo e($exchangeRate->rate_uangtix_to_idr); ?>"
                                           step="0.0001"
                                           min="0.0001"
                                           max="999999"
                                           required
                                           x-model="rates.utxToIdr"
                                           @input="updateReverseRate('utx')"
                                           class="block w-full pl-16 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">IDR</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Berapa IDR yang didapat dari 1 UangTix</p>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction Limits Section -->
                    <div class="space-y-6">
                        <div class="flex items-center gap-3 pb-3 border-b border-gray-200 dark:border-gray-700">
                            <div class="w-6 h-6 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="shield-check" class="w-4 h-4 text-green-600 dark:text-green-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Batas Transaksi</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Min Deposit -->
                            <div class="space-y-2">
                                <label for="min_deposit_idr" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Minimum Deposit
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">Rp</span>
                                    </div>
                                    <input type="number"
                                           id="min_deposit_idr"
                                           name="min_deposit_idr"
                                           value="<?php echo e($exchangeRate->min_deposit_idr); ?>"
                                           min="1000"
                                           required
                                           x-model="limits.minDeposit"
                                           @input="validateLimits"
                                           class="block w-full pl-8 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Minimal Rp 1,000</p>
                            </div>

                            <!-- Max Deposit -->
                            <div class="space-y-2">
                                <label for="max_deposit_idr" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Maksimum Deposit
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">Rp</span>
                                    </div>
                                    <input type="number"
                                           id="max_deposit_idr"
                                           name="max_deposit_idr"
                                           value="<?php echo e($exchangeRate->max_deposit_idr); ?>"
                                           min="10000"
                                           required
                                           x-model="limits.maxDeposit"
                                           class="block w-full pl-8 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Harus lebih besar dari minimum</p>
                            </div>

                            <!-- Min Withdrawal -->
                            <div class="space-y-2">
                                <label for="min_withdrawal_uangtix" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Minimum Penarikan
                                </label>
                                <div class="relative">
                                    <input type="number"
                                           id="min_withdrawal_uangtix"
                                           name="min_withdrawal_uangtix"
                                           value="<?php echo e($exchangeRate->min_withdrawal_uangtix); ?>"
                                           min="1"
                                           required
                                           class="block w-full pl-3 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">UTX</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Minimal 1 UangTix</p>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction Fees Section -->
                    <div class="space-y-6">
                        <div class="flex items-center gap-3 pb-3 border-b border-gray-200 dark:border-gray-700">
                            <div class="w-6 h-6 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="percent" class="w-4 h-4 text-orange-600 dark:text-orange-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Biaya Transaksi</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Deposit Fee -->
                            <div class="space-y-2">
                                <label for="deposit_fee_percentage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Biaya Deposit
                                </label>
                                <div class="relative">
                                    <input type="number"
                                           id="deposit_fee_percentage"
                                           name="deposit_fee_percentage"
                                           value="<?php echo e($exchangeRate->deposit_fee_percentage); ?>"
                                           step="0.01"
                                           min="0"
                                           max="100"
                                           required
                                           class="block w-full pl-3 pr-8 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">%</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Persentase biaya dari jumlah deposit</p>
                            </div>

                            <!-- Withdrawal Fee -->
                            <div class="space-y-2">
                                <label for="withdrawal_fee_percentage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Biaya Penarikan
                                </label>
                                <div class="relative">
                                    <input type="number"
                                           id="withdrawal_fee_percentage"
                                           name="withdrawal_fee_percentage"
                                           value="<?php echo e($exchangeRate->withdrawal_fee_percentage); ?>"
                                           step="0.01"
                                           min="0"
                                           max="100"
                                           required
                                           class="block w-full pl-3 pr-8 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">%</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Persentase biaya dari jumlah penarikan</p>
                            </div>
                        </div>
                    </div>

                    <!-- Service Status Section -->
                    <div class="space-y-6">
                        <div class="flex items-center gap-3 pb-3 border-b border-gray-200 dark:border-gray-700">
                            <div class="w-6 h-6 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="toggle-left" class="w-4 h-4 text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Status Layanan</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Deposits Toggle -->
                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Deposit</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Izinkan pengguna melakukan deposit</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox"
                                               id="deposits_enabled"
                                               name="deposits_enabled"
                                               value="1"
                                               <?php echo e($exchangeRate->deposits_enabled ? 'checked' : ''); ?>

                                               class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>

                            <!-- Withdrawals Toggle -->
                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Penarikan</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Izinkan pengguna melakukan penarikan</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox"
                                               id="withdrawals_enabled"
                                               name="withdrawals_enabled"
                                               value="1"
                                               <?php echo e($exchangeRate->withdrawals_enabled ? 'checked' : ''); ?>

                                               class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-yellow-600"></div>
                                    </label>
                                </div>
                            </div>

                            <!-- Transfers Toggle -->
                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Transfer</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Izinkan transfer antar pengguna</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox"
                                               id="transfers_enabled"
                                               name="transfers_enabled"
                                               value="1"
                                               <?php echo e($exchangeRate->transfers_enabled ? 'checked' : ''); ?>

                                               class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions Section -->
                    <div class="space-y-6">
                        <div class="flex items-center gap-3 pb-3 border-b border-gray-200 dark:border-gray-700">
                            <div class="w-6 h-6 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="file-text" class="w-4 h-4 text-red-600 dark:text-red-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Syarat dan Ketentuan</h3>
                        </div>

                        <div class="space-y-2">
                            <label for="terms_and_conditions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Syarat dan Ketentuan UangTix
                            </label>
                            <textarea id="terms_and_conditions"
                                      name="terms_and_conditions"
                                      rows="8"
                                      placeholder="Masukkan syarat dan ketentuan UangTix..."
                                      class="block w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400 resize-none"><?php echo e($exchangeRate->terms_and_conditions); ?></textarea>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Syarat dan ketentuan yang akan ditampilkan kepada pengguna</p>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                        <button type="submit"
                                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                            <i data-lucide="save" class="w-5 h-5 mr-2"></i>
                            Simpan Pengaturan
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Current Settings Preview -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-cyan-100 dark:bg-cyan-900/20 rounded-lg flex items-center justify-center">
                        <i data-lucide="eye" class="w-5 h-5 text-cyan-600 dark:text-cyan-400"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Preview Pengaturan Saat Ini</h2>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Exchange Rates Preview -->
                    <div class="space-y-4">
                        <div class="flex items-center gap-2">
                            <div class="w-5 h-5 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="trending-up" class="w-3 h-3 text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <h3 class="text-sm font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wide">Kurs</h3>
                        </div>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">IDR → UTX:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_rates['idr_to_uangtix']); ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">UTX → IDR:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_rates['uangtix_to_idr']); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction Limits Preview -->
                    <div class="space-y-4">
                        <div class="flex items-center gap-2">
                            <div class="w-5 h-5 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="shield-check" class="w-3 h-3 text-green-600 dark:text-green-400"></i>
                            </div>
                            <h3 class="text-sm font-semibold text-green-600 dark:text-green-400 uppercase tracking-wide">Batas Transaksi</h3>
                        </div>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">Min Deposit:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_limits['min_deposit']); ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">Max Deposit:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_limits['max_deposit']); ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">Min Penarikan:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_limits['min_withdrawal']); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction Fees Preview -->
                    <div class="space-y-4">
                        <div class="flex items-center gap-2">
                            <div class="w-5 h-5 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="percent" class="w-3 h-3 text-orange-600 dark:text-orange-400"></i>
                            </div>
                            <h3 class="text-sm font-semibold text-orange-600 dark:text-orange-400 uppercase tracking-wide">Biaya</h3>
                        </div>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">Deposit:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_fees['deposit_fee']); ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">Penarikan:</span>
                                <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_fees['withdrawal_fee']); ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">Transfer:</span>
                                <span class="font-medium text-green-600 dark:text-green-400">0% (Gratis)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Service Status Preview -->
                    <div class="space-y-4">
                        <div class="flex items-center gap-2">
                            <div class="w-5 h-5 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="toggle-left" class="w-3 h-3 text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <h3 class="text-sm font-semibold text-purple-600 dark:text-purple-400 uppercase tracking-wide">Status Layanan</h3>
                        </div>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Deposit:</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($exchangeRate->deposits_enabled ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'); ?>">
                                    <div class="w-1.5 h-1.5 rounded-full <?php echo e($exchangeRate->deposits_enabled ? 'bg-green-400' : 'bg-red-400'); ?> mr-1"></div>
                                    <?php echo e($exchangeRate->deposits_enabled ? 'Aktif' : 'Nonaktif'); ?>

                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Penarikan:</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($exchangeRate->withdrawals_enabled ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'); ?>">
                                    <div class="w-1.5 h-1.5 rounded-full <?php echo e($exchangeRate->withdrawals_enabled ? 'bg-green-400' : 'bg-red-400'); ?> mr-1"></div>
                                    <?php echo e($exchangeRate->withdrawals_enabled ? 'Aktif' : 'Nonaktif'); ?>

                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Transfer:</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($exchangeRate->transfers_enabled ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'); ?>">
                                    <div class="w-1.5 h-1.5 rounded-full <?php echo e($exchangeRate->transfers_enabled ? 'bg-green-400' : 'bg-red-400'); ?> mr-1"></div>
                                    <?php echo e($exchangeRate->transfers_enabled ? 'Aktif' : 'Nonaktif'); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Alpine.js component for settings form
function settingsForm() {
    return {
        rates: {
            idrToUtx: <?php echo e($exchangeRate->rate_idr_to_uangtix); ?>,
            utxToIdr: <?php echo e($exchangeRate->rate_uangtix_to_idr); ?>

        },
        limits: {
            minDeposit: <?php echo e($exchangeRate->min_deposit_idr); ?>,
            maxDeposit: <?php echo e($exchangeRate->max_deposit_idr); ?>

        },
        isSubmitting: false,

        // Auto-calculate reverse exchange rate
        updateReverseRate(type) {
            if (type === 'idr') {
                const idrToUtx = parseFloat(this.rates.idrToUtx);
                if (idrToUtx > 0) {
                    this.rates.utxToIdr = (1 / idrToUtx).toFixed(4);
                }
            } else if (type === 'utx') {
                const utxToIdr = parseFloat(this.rates.utxToIdr);
                if (utxToIdr > 0) {
                    this.rates.idrToUtx = (1 / utxToIdr).toFixed(4);
                }
            }
        },

        // Validate deposit limits
        validateLimits() {
            const minDeposit = parseInt(this.limits.minDeposit);
            const maxDeposit = parseInt(this.limits.maxDeposit);

            if (minDeposit >= maxDeposit) {
                this.limits.maxDeposit = minDeposit + 10000;
            }
        }
    }
}

// Initialize interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Add input validation feedback
    const inputs = document.querySelectorAll('input[type="number"]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && this.checkValidity()) {
                this.classList.add('border-green-500');
                this.classList.remove('border-red-500');
            } else if (this.value && !this.checkValidity()) {
                this.classList.add('border-red-500');
                this.classList.remove('border-green-500');
            }
        });

        input.addEventListener('input', function() {
            this.classList.remove('border-green-500', 'border-red-500');
        });
    });

    // Form submission with loading state
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalContent = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="flex items-center"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>Menyimpan...</div>';

            // Re-enable after timeout as fallback
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalContent;
            }, 5000);
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/uangtix/settings.blade.php ENDPATH**/ ?>