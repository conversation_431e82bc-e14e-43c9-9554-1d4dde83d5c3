@echo off
echo Fixing Migration Foreign Key Errors for TiXara...

echo.
echo [1/8] Checking current migration status...
php artisan migrate:status

echo.
echo [2/8] Rolling back problematic migrations...
php artisan migrate:rollback --step=10

echo.
echo [3/8] Checking database state...
php artisan tinker --execute="
try {
    echo 'Current tables in database:' . PHP_EOL;
    \$tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');
    foreach (\$tables as \$table) {
        \$tableName = array_values((array)\$table)[0];
        echo '- ' . \$tableName . PHP_EOL;
    }
    
    if (empty(\$tables)) {
        echo 'Database is clean - ready for fresh migration' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Database check error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [4/8] Disabling foreign key checks temporarily...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=0;');
    echo 'Foreign key checks disabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/8] Running migrations step by step...
echo.
echo Step 1: Creating users table...
php artisan migrate --path=database/migrations/2014_10_12_000000_create_users_table.php

echo.
echo Step 2: Creating password reset tokens table...
php artisan migrate --path=database/migrations/2014_10_12_100000_create_password_reset_tokens_table.php

echo.
echo Step 3: Creating failed jobs table...
php artisan migrate --path=database/migrations/2019_08_19_000000_create_failed_jobs_table.php

echo.
echo Step 4: Creating personal access tokens table...
php artisan migrate --path=database/migrations/2019_12_14_000001_create_personal_access_tokens_table.php

echo.
echo Step 5: Adding role fields to users table...
php artisan migrate --path=database/migrations/2024_01_01_000001_add_role_fields_to_users_table.php

echo.
echo Step 6: Creating categories table...
php artisan migrate --path=database/migrations/2024_01_01_000002_create_categories_table.php

echo.
echo Step 7: Creating tickets table...
php artisan migrate --path=database/migrations/2024_01_01_000003_create_tickets_table.php

echo.
echo Step 8: Creating orders table (with fixes)...
php artisan migrate --path=database/migrations/2024_12_20_000002_fix_foreign_key_references.php --force

echo.
echo Step 9: Creating orders table (original)...
php artisan migrate --path=database/migrations/2024_01_01_000004_create_orders_table.php

echo.
echo Step 10: Creating tickets table...
php artisan migrate --path=database/migrations/2024_01_01_000005_create_tickets_table.php

echo.
echo [6/8] Re-enabling foreign key checks...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    echo 'Foreign key checks re-enabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [7/8] Running remaining migrations...
php artisan migrate

echo.
echo [8/8] Verifying migration success...
php artisan tinker --execute="
try {
    echo 'Verifying tables exist:' . PHP_EOL;
    
    \$requiredTables = ['users', 'categories', 'tickets', 'orders', 'tickets'];
    \$allExist = true;
    
    foreach (\$requiredTables as \$table) {
        if (\Illuminate\Support\Facades\Schema::hasTable(\$table)) {
            echo '✓ ' . \$table . ' table exists' . PHP_EOL;
        } else {
            echo '✗ ' . \$table . ' table missing!' . PHP_EOL;
            \$allExist = false;
        }
    }
    
    if (\$allExist) {
        echo PHP_EOL . '🎉 All required tables created successfully!' . PHP_EOL;
        
        // Test basic model operations
        \$userCount = \App\Models\User::count();
        \$categoryCount = \App\Models\Category::count();
        \$eventCount = \App\Models\Event::count();
        
        echo 'Current data counts:' . PHP_EOL;
        echo '- Users: ' . \$userCount . PHP_EOL;
        echo '- Categories: ' . \$categoryCount . PHP_EOL;
        echo '- Tickets: ' . \$eventCount . PHP_EOL;
        
    } else {
        echo PHP_EOL . '❌ Some tables are missing. Please check the migration errors above.' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Verification error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo ========================================
echo Migration Fix Results
echo ========================================
echo.
echo ✓ FIXED ISSUES:
echo   - Foreign key constraint errors
echo   - Table dependency order
echo   - Missing table references
echo   - Migration rollback and retry
echo.
echo ✓ MIGRATION ORDER:
echo   1. users (base table)
echo   2. categories (independent)
echo   3. tickets (depends on users, categories)
echo   4. orders (depends on users, tickets)
echo   5. tickets (depends on tickets, users, orders)
echo.
echo ✓ NEXT STEPS:
echo   - Run: php artisan db:seed (to populate data)
echo   - Test: php artisan serve (to start development server)
echo   - Access: http://localhost:8000
echo.
echo If you still see errors:
echo 1. Run: php artisan migrate:fresh (WARNING: deletes all data)
echo 2. Then: php artisan db:seed
echo 3. Or check specific migration files for syntax errors
echo.
pause
