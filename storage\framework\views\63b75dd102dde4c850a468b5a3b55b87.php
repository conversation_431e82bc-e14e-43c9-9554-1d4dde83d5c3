<?php $__env->startSection('title', 'Kritik & Saran - TiXara'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Feedback Styles */
.feedback-hero {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    position: relative;
    overflow: hidden;
}

.feedback-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="feedback" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23feedback)"/></svg>');
    opacity: 0.3;
}

.feedback-card {
    background: white;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.feedback-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feedback-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.feedback-card:hover::before {
    transform: scaleX(1);
}

.rating-stars {
    display: flex;
    gap: 4px;
    margin-bottom: 16px;
}

.rating-star {
    width: 32px;
    height: 32px;
    cursor: pointer;
    color: #d1d5db;
    transition: all 0.2s ease;
}

.rating-star:hover,
.rating-star.active {
    color: #fbbf24;
    transform: scale(1.1);
}

.btn-feedback {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 16px 24px;
    border: none;
    border-radius: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn-feedback:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.3);
    color: white;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-badge.suggestion {
    background: #dbeafe;
    color: #1d4ed8;
}

.type-badge.complaint {
    background: #fef3c7;
    color: #d97706;
}

.type-badge.compliment {
    background: #dcfce7;
    color: #166534;
}

.type-badge.feature_request {
    background: #e0e7ff;
    color: #4338ca;
}

.type-badge.bug_report {
    background: #fee2e2;
    color: #991b1b;
}

@media (max-width: 768px) {
    .feedback-card {
        padding: 24px;
    }
    
    .rating-star {
        width: 28px;
        height: 28px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="feedback-hero text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 backdrop-blur-sm">
                <i data-lucide="message-square" class="w-10 h-10 text-white"></i>
            </div>
            <h1 class="text-5xl md:text-6xl font-bold mb-6">
                Kritik & Saran
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                Suara Anda sangat berharga bagi kami
            </p>
            <p class="text-lg mb-12 max-w-2xl mx-auto opacity-80">
                Bantu kami meningkatkan layanan TiXara dengan memberikan kritik, saran, atau masukan Anda.
            </p>
        </div>
    </div>
</div>

<!-- Feedback Form -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="feedback-card">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Berikan Feedback Anda
                </h2>
                <p class="text-gray-600 dark:text-gray-300">
                    Setiap masukan dari Anda membantu kami menjadi lebih baik
                </p>
            </div>

            <form id="feedbackForm">
                <?php echo csrf_field(); ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="feedbackType" class="form-label">Jenis Feedback</label>
                        <select class="form-control" id="feedbackType" name="type" required>
                            <option value="">Pilih jenis feedback</option>
                            <option value="suggestion">Saran</option>
                            <option value="complaint">Keluhan</option>
                            <option value="compliment">Pujian</option>
                            <option value="feature_request">Permintaan Fitur</option>
                            <option value="bug_report">Laporan Bug</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div>
                        <label for="feedbackCategory" class="form-label">Kategori</label>
                        <select class="form-control" id="feedbackCategory" name="category" required>
                            <option value="">Pilih kategori</option>
                            <option value="ui_ux">UI/UX</option>
                            <option value="performance">Performa</option>
                            <option value="feature">Fitur</option>
                            <option value="content">Konten</option>
                            <option value="service">Layanan</option>
                            <option value="technical">Teknis</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="feedbackTitle" class="form-label">Judul Feedback</label>
                    <input type="text" class="form-control" id="feedbackTitle" name="title" 
                           placeholder="Ringkasan singkat feedback Anda" required>
                </div>

                <div class="mb-6">
                    <label for="feedbackMessage" class="form-label">Pesan</label>
                    <textarea class="form-control" id="feedbackMessage" name="message" 
                              rows="6" placeholder="Jelaskan feedback Anda secara detail..." required></textarea>
                </div>

                <div class="mb-6">
                    <label class="form-label">Rating Kepuasan (Opsional)</label>
                    <div class="rating-stars">
                        <i data-lucide="star" class="rating-star" data-rating="1"></i>
                        <i data-lucide="star" class="rating-star" data-rating="2"></i>
                        <i data-lucide="star" class="rating-star" data-rating="3"></i>
                        <i data-lucide="star" class="rating-star" data-rating="4"></i>
                        <i data-lucide="star" class="rating-star" data-rating="5"></i>
                    </div>
                    <input type="hidden" id="feedbackRating" name="rating" value="">
                    <div class="form-text">Berikan rating 1-5 bintang untuk kepuasan Anda terhadap TiXara</div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="contactEmail" class="form-label">Email Kontak</label>
                        <input type="email" class="form-control" id="contactEmail" name="contact_email" 
                               value="<?php echo e(auth()->check() ? auth()->user()->email : ''); ?>"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="contactPhone" class="form-label">Nomor Telepon (Opsional)</label>
                        <input type="tel" class="form-control" id="contactPhone" name="contact_phone" 
                               placeholder="+62 812-3456-7890">
                    </div>
                </div>

                <div class="mb-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isAnonymous" name="is_anonymous" value="1">
                        <label class="form-check-label" for="isAnonymous">
                            Kirim sebagai anonim
                        </label>
                        <div class="form-text">Jika dicentang, identitas Anda tidak akan disimpan</div>
                    </div>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <i data-lucide="info" class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">Informasi Penting:</h4>
                            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                <li>• Feedback akan ditinjau oleh tim kami</li>
                                <li>• Kami akan merespons dalam 3-5 hari kerja</li>
                                <li>• Saran yang konstruktif sangat kami hargai</li>
                                <li>• Feedback membantu kami mengembangkan platform yang lebih baik</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4">
                    <button type="button" class="btn btn-secondary flex-1" onclick="history.back()">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                        Kembali
                    </button>
                    <button type="submit" class="btn-feedback flex-1">
                        <i data-lucide="send" class="w-5 h-5"></i>
                        Kirim Feedback
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Feedback Types Info -->
<div class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Jenis Feedback
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Pilih jenis feedback yang sesuai dengan tujuan Anda
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="feedback-card text-center">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="lightbulb" class="w-8 h-8 text-blue-600 dark:text-blue-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Saran</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Berikan ide atau saran untuk meningkatkan layanan kami
                </p>
            </div>

            <div class="feedback-card text-center">
                <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="frown" class="w-8 h-8 text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Keluhan</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Sampaikan keluhan atau ketidakpuasan terhadap layanan
                </p>
            </div>

            <div class="feedback-card text-center">
                <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="smile" class="w-8 h-8 text-green-600 dark:text-green-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Pujian</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Berikan apresiasi untuk layanan yang memuaskan
                </p>
            </div>

            <div class="feedback-card text-center">
                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="plus-square" class="w-8 h-8 text-purple-600 dark:text-purple-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Permintaan Fitur</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Usulkan fitur baru yang ingin Anda lihat di TiXara
                </p>
            </div>

            <div class="feedback-card text-center">
                <div class="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="bug" class="w-8 h-8 text-red-600 dark:text-red-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Laporan Bug</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Laporkan bug atau error yang Anda temukan
                </p>
            </div>

            <div class="feedback-card text-center">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="more-horizontal" class="w-8 h-8 text-gray-600 dark:text-gray-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Lainnya</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Feedback lain yang tidak masuk kategori di atas
                </p>
            </div>
        </div>
    </div>
</div>

<?php if(auth()->check()): ?>
<!-- My Feedback Link -->
<div class="py-12 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Lihat Feedback Anda
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-8">
            Pantau status feedback yang telah Anda kirimkan
        </p>
        <a href="<?php echo e(route('feedback.my-feedback')); ?>" class="btn-feedback inline-flex">
            <i data-lucide="list" class="w-5 h-5"></i>
            Feedback Saya
        </a>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
/*
 * Feedback JavaScript
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Rating stars functionality
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('feedbackRating');
    
    ratingStars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            ratingInput.value = rating;
            
            // Update star display
            ratingStars.forEach((s, i) => {
                if (i < rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });
        
        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            
            ratingStars.forEach((s, i) => {
                if (i < rating) {
                    s.style.color = '#fbbf24';
                } else {
                    s.style.color = '#d1d5db';
                }
            });
        });
    });
    
    // Reset star colors on mouse leave
    document.querySelector('.rating-stars').addEventListener('mouseleave', function() {
        const currentRating = parseInt(ratingInput.value) || 0;
        
        ratingStars.forEach((s, i) => {
            if (i < currentRating) {
                s.style.color = '#fbbf24';
            } else {
                s.style.color = '#d1d5db';
            }
        });
    });

    // Feedback form handling
    document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Mengirim...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("feedback.store")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                this.reset();
                ratingInput.value = '';
                ratingStars.forEach(s => {
                    s.classList.remove('active');
                    s.style.color = '#d1d5db';
                });
                
                // Redirect to my feedback if user is logged in
                <?php if(auth()->check()): ?>
                setTimeout(() => {
                    window.location.href = '<?php echo e(route("feedback.my-feedback")); ?>';
                }, 2000);
                <?php endif; ?>
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat mengirim feedback');
                
                if (data.errors) {
                    Object.keys(data.errors).forEach(key => {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field) {
                            field.classList.add('is-invalid');
                            
                            // Remove existing error message
                            const existingError = field.parentNode.querySelector('.invalid-feedback');
                            if (existingError) {
                                existingError.remove();
                            }
                            
                            // Add new error message
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'invalid-feedback';
                            errorDiv.textContent = data.errors[key][0];
                            field.parentNode.appendChild(errorDiv);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Feedback submission error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    });

    // Clear validation errors on input
    document.querySelectorAll('.form-control').forEach(field => {
        field.addEventListener('input', function() {
            this.classList.remove('is-invalid');
            const errorDiv = this.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        });
    });

    // Anonymous checkbox handling
    document.getElementById('isAnonymous').addEventListener('change', function() {
        const emailField = document.getElementById('contactEmail');
        const phoneField = document.getElementById('contactPhone');
        
        if (this.checked) {
            emailField.value = '';
            phoneField.value = '';
            emailField.disabled = true;
            phoneField.disabled = true;
        } else {
            emailField.disabled = false;
            phoneField.disabled = false;
            <?php if(auth()->check()): ?>
            emailField.value = '<?php echo e(auth()->user()->email); ?>';
            <?php endif; ?>
        }
    });
});

function showToast(type, message) {
    // Remove existing toasts
    document.querySelectorAll('.toast-notification').forEach(toast => toast.remove());
    
    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/feedback/index.blade.php ENDPATH**/ ?>