@props([
    'name',
    'title' => null,
    'subtitle' => null,
    'maxWidth' => 'md', // sm, md, lg, xl, 2xl, full
    'closeable' => true,
    'backdrop' => true,
    'blur' => true,
    'persistent' => false
])

@php
    $maxWidthClasses = [
        'sm' => 'max-w-sm',
        'md' => 'max-w-md',
        'lg' => 'max-w-lg',
        'xl' => 'max-w-xl',
        '2xl' => 'max-w-2xl',
        'full' => 'max-w-full'
    ];
    
    $maxWidthClass = $maxWidthClasses[$maxWidth] ?? $maxWidthClasses['md'];
@endphp

<div x-data="{ show: false, name: '{{ $name }}' }"
     x-show="show"
     x-on:open-modal.window="show = ($event.detail.name === name)"
     x-on:close-modal.window="show = false"
     x-on:keydown.escape.window="!{{ $persistent ? 'true' : 'false' }} && (show = false)"
     style="display: none;"
     class="fixed inset-0 z-50 overflow-y-auto">
    
    <!-- Backdrop -->
    <div x-show="show"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 {{ $backdrop ? 'bg-gray-500 bg-opacity-75' : '' }} {{ $blur ? 'backdrop-blur-sm' : '' }}"
         @if(!$persistent)
             @click="show = false"
         @endif>
    </div>

    <!-- Modal -->
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div x-show="show"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             class="inline-block w-full {{ $maxWidthClass }} my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl">
            
            @if($title || $subtitle || $closeable)
                <!-- Header -->
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div>
                            @if($title)
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $title }}</h3>
                            @endif
                            @if($subtitle)
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $subtitle }}</p>
                            @endif
                        </div>
                        
                        @if($closeable)
                            <button @click="show = false"
                                    class="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200">
                                <i data-lucide="x" class="w-5 h-5"></i>
                            </button>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Body -->
            <div class="px-6 py-4">
                {{ $slot }}
            </div>

            @isset($footer)
                <!-- Footer -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
                    {{ $footer }}
                </div>
            @endisset
        </div>
    </div>
</div>

@once
    @push('scripts')
    <script>
        // Modal helper functions
        window.openModal = function(name) {
            window.dispatchEvent(new CustomEvent('open-modal', {
                detail: { name: name }
            }));
        };
        
        window.closeModal = function() {
            window.dispatchEvent(new CustomEvent('close-modal'));
        };
    </script>
    @endpush
@endonce
