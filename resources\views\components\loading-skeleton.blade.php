@props([
    'type' => 'card', // card, list, table, text, avatar, button
    'rows' => 3,
    'height' => null,
    'width' => null,
    'rounded' => 'rounded',
    'animate' => true
])

@php
    $baseClasses = 'bg-gray-200 dark:bg-gray-700';
    $animateClasses = $animate ? 'animate-pulse' : '';
    
    $skeletonClasses = collect([$baseClasses, $rounded, $animateClasses])->filter()->implode(' ');
@endphp

<div {{ $attributes->merge(['class' => 'space-y-4']) }}>
    @if($type === 'card')
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <!-- Header -->
            <div class="flex items-center space-x-4 mb-4">
                <div class="{{ $skeletonClasses }} w-12 h-12 rounded-lg"></div>
                <div class="flex-1 space-y-2">
                    <div class="{{ $skeletonClasses }} h-4 w-3/4 rounded"></div>
                    <div class="{{ $skeletonClasses }} h-3 w-1/2 rounded"></div>
                </div>
            </div>
            
            <!-- Body -->
            <div class="space-y-3">
                @for($i = 0; $i < $rows; $i++)
                    <div class="{{ $skeletonClasses }} h-4 rounded" style="width: {{ rand(60, 100) }}%"></div>
                @endfor
            </div>
            
            <!-- Footer -->
            <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="{{ $skeletonClasses }} h-8 w-20 rounded-lg"></div>
                <div class="{{ $skeletonClasses }} h-8 w-16 rounded-lg"></div>
            </div>
        </div>
        
    @elseif($type === 'list')
        @for($i = 0; $i < $rows; $i++)
            <div class="flex items-center space-x-4 p-4">
                <div class="{{ $skeletonClasses }} w-10 h-10 rounded-full"></div>
                <div class="flex-1 space-y-2">
                    <div class="{{ $skeletonClasses }} h-4 w-3/4 rounded"></div>
                    <div class="{{ $skeletonClasses }} h-3 w-1/2 rounded"></div>
                </div>
                <div class="{{ $skeletonClasses }} h-6 w-16 rounded"></div>
            </div>
        @endfor
        
    @elseif($type === 'table')
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex space-x-4">
                    @for($i = 0; $i < 4; $i++)
                        <div class="{{ $skeletonClasses }} h-4 flex-1 rounded"></div>
                    @endfor
                </div>
            </div>
            
            <!-- Rows -->
            @for($i = 0; $i < $rows; $i++)
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                    <div class="flex space-x-4">
                        @for($j = 0; $j < 4; $j++)
                            <div class="{{ $skeletonClasses }} h-4 flex-1 rounded"></div>
                        @endfor
                    </div>
                </div>
            @endfor
        </div>
        
    @elseif($type === 'text')
        <div class="space-y-2">
            @for($i = 0; $i < $rows; $i++)
                <div class="{{ $skeletonClasses }} h-4 rounded" style="width: {{ $i === $rows - 1 ? rand(40, 70) : rand(80, 100) }}%"></div>
            @endfor
        </div>
        
    @elseif($type === 'avatar')
        <div class="{{ $skeletonClasses }} {{ $width ?? 'w-12' }} {{ $height ?? 'h-12' }} rounded-full"></div>
        
    @elseif($type === 'button')
        <div class="{{ $skeletonClasses }} {{ $width ?? 'w-24' }} {{ $height ?? 'h-10' }} rounded-lg"></div>
        
    @else
        <!-- Custom skeleton -->
        <div class="{{ $skeletonClasses }} {{ $width ?? 'w-full' }} {{ $height ?? 'h-4' }} {{ $rounded }}"></div>
    @endif
</div>
