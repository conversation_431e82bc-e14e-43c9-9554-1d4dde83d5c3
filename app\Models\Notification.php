<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'data',
        'read_at',
        'action_url',
        'priority'
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Notification types
     */
    const TYPE_SYSTEM = 'system';
    const TYPE_USER = 'user';
    const TYPE_EVENT = 'event';
    const TYPE_PAYMENT = 'payment';
    const TYPE_ORDER = 'order';
    const TYPE_TICKET = 'ticket';

    /**
     * Priority levels
     */
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Get the user that owns the notification
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(): void
    {
        $this->update(['read_at' => now()]);
    }

    /**
     * Check if notification is read
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * Check if notification is unread
     */
    public function isUnread(): bool
    {
        return is_null($this->read_at);
    }

    /**
     * Get notification icon based on type
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            self::TYPE_SYSTEM => 'cog',
            self::TYPE_USER => 'user',
            self::TYPE_EVENT => 'calendar',
            self::TYPE_PAYMENT => 'credit-card',
            self::TYPE_ORDER => 'shopping-bag',
            self::TYPE_TICKET => 'ticket',
            default => 'bell'
        };
    }

    /**
     * Get notification color based on priority
     */
    public function getColorAttribute(): string
    {
        return match($this->priority ?? self::PRIORITY_NORMAL) {
            self::PRIORITY_LOW => 'gray',
            self::PRIORITY_NORMAL => 'blue',
            self::PRIORITY_HIGH => 'orange',
            self::PRIORITY_URGENT => 'red',
            default => 'blue'
        };
    }

    /**
     * Get formatted time ago
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope for unread notifications
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope for read notifications
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope for specific type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for specific priority
     */
    public function scopeOfPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Create system notification
     */
    public static function createSystem(int $userId, string $title, string $message, array $data = []): self
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => self::TYPE_SYSTEM,
            'data' => $data,
            'priority' => self::PRIORITY_NORMAL,
        ]);
    }

    /**
     * Create event notification
     */
    public static function createEvent(int $userId, string $title, string $message, array $data = []): self
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => self::TYPE_EVENT,
            'data' => $data,
            'priority' => self::PRIORITY_NORMAL,
        ]);
    }

    /**
     * Create payment notification
     */
    public static function createPayment(int $userId, string $title, string $message, array $data = []): self
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => self::TYPE_PAYMENT,
            'data' => $data,
            'priority' => self::PRIORITY_HIGH,
        ]);
    }

    /**
     * Create order notification
     */
    public static function createOrder(int $userId, string $title, string $message, array $data = []): self
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => self::TYPE_ORDER,
            'data' => $data,
            'priority' => self::PRIORITY_NORMAL,
        ]);
    }

    /**
     * Broadcast notification to multiple users
     */
    public static function broadcast(array $userIds, string $title, string $message, string $type = self::TYPE_SYSTEM, array $data = []): void
    {
        foreach ($userIds as $userId) {
            self::create([
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'data' => $data,
                'priority' => self::PRIORITY_NORMAL,
            ]);
        }
    }

    /**
     * Get all notification types
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_SYSTEM => 'System',
            self::TYPE_USER => 'User',
            self::TYPE_EVENT => 'Event',
            self::TYPE_PAYMENT => 'Payment',
            self::TYPE_ORDER => 'Order',
            self::TYPE_TICKET => 'Ticket',
        ];
    }

    /**
     * Get all priority levels
     */
    public static function getPriorities(): array
    {
        return [
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_NORMAL => 'Normal',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_URGENT => 'Urgent',
        ];
    }
}
