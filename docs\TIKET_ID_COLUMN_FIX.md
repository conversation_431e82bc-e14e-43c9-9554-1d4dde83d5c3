# Tiket_ID Column Error Fix Documentation

## Overview

This document describes the comprehensive fix for SQLSTATE[42S22] Column not found errors related to 'tiket_id' column references in the TiXara application.

## Problem Description

**Error**: `SQLSTATE[42S22]: Column not found: 1054 Unknown column 'tiket_id' in 'where clause'`

**Location**: Multiple files, specifically triggered at:
- `/organizer/dashboard` - `OrganizerDashboardController.php:375`
- Various other controllers and seeders

**Root Cause**: 
- Legacy code still using `tiket_id` column name
- Database migration renamed column to `event_id` but code not fully updated
- Inconsistent column naming across different files

## Files Fixed

### 1. ✅ OrganizerDashboardController.php (Line 375)

**Problem**: Conversion rate calculation using wrong column name

**Before (Incorrect)**:
```php
$totalOrders = Order::whereIn('tiket_id', $tickets->pluck('id'))
    ->where('payment_status', 'paid')
    ->count();
```

**After (Correct)**:
```php
$totalOrders = Order::whereIn('event_id', $tickets->pluck('id'))
    ->where('payment_status', 'paid')
    ->count();
```

### 2. ✅ TicketController.php (Line 36)

**Problem**: Checking existing tickets using wrong column

**Before (Incorrect)**:
```php
$existingTickets = auth()->user()->tickets()
    ->where('tiket_id', $event->id)
    ->where('status', '!=', 'cancelled')
    ->count();
```

**After (Correct)**:
```php
$existingTickets = auth()->user()->tickets()
    ->where('event_id', $event->id)
    ->where('status', '!=', 'cancelled')
    ->count();
```

### 3. ✅ AdminTicketController.php (Line 287)

**Problem**: Bulk actions validation using wrong field names

**Before (Incorrect)**:
```php
$validated = $request->validate([
    'action' => 'required|in:delete,feature,unfeature,publish,unpublish',
    'tiket_ids' => 'required|array',
    'tiket_ids.*' => 'exists:tickets,id',
]);

$tickets = Event::whereIn('id', $validated['tiket_ids']);
```

**After (Correct)**:
```php
$validated = $request->validate([
    'action' => 'required|in:delete,feature,unfeature,publish,unpublish',
    'event_ids' => 'required|array',
    'event_ids.*' => 'exists:events,id',
]);

$tickets = Event::whereIn('id', $validated['event_ids']);
```

### 4. ✅ Livewire TicketPurchase.php (Line 56)

**Problem**: Event emission using wrong field name

**Before (Incorrect)**:
```php
$this->emit('ticketAddedToCart', [
    'tiket_id' => $this->event->id,
    'quantity' => $this->quantity,
    'total_price' => $this->totalPrice
]);
```

**After (Correct)**:
```php
$this->emit('ticketAddedToCart', [
    'event_id' => $this->event->id,
    'quantity' => $this->quantity,
    'total_price' => $this->totalPrice
]);
```

### 5. ✅ TicketSeeder.php

**Problem**: Creating orders and tickets with wrong column

**Before (Incorrect)**:
```php
// Create order
$order = Order::create([
    'order_number' => $this->generateOrderNumber(),
    'user_id' => $buyer->id,
    'tiket_id' => $event->id,
    // ...
]);

// Create ticket
$ticket = Ticket::create([
    'ticket_number' => $this->generateTicketNumber(),
    'qr_code' => $this->generateQRCode(),
    'tiket_id' => $event->id,
    // ...
]);
```

**After (Correct)**:
```php
// Create order
$order = Order::create([
    'order_number' => $this->generateOrderNumber(),
    'user_id' => $buyer->id,
    'event_id' => $event->id,
    // ...
]);

// Create ticket
$ticket = Ticket::create([
    'ticket_number' => $this->generateTicketNumber(),
    'qr_code' => $this->generateQRCode(),
    'event_id' => $event->id,
    // ...
]);
```

### 6. ✅ NotificationSeeder.php

**Problem**: Notification data using wrong field names

**Before (Incorrect)**:
```php
'data' => [
    'tiket_id' => 1,
    'event_name' => 'Jakarta Music Festival',
    'discount' => '20%'
],
```

**After (Correct)**:
```php
'data' => [
    'event_id' => 1,
    'event_name' => 'Jakarta Music Festival',
    'discount' => '20%'
],
```

### 7. ✅ TicketController.php (QR Code Generation)

**Problem**: QR code data using wrong field name

**Before (Incorrect)**:
```php
$qrCodeData = json_encode([
    'ticket_id' => $ticket->id,
    'ticket_number' => $ticket->ticket_number,
    'qr_code' => $ticket->qr_code,
    'tiket_id' => $ticket->tiket_id,
    'attendee_name' => $ticket->attendee_name,
    'generated_at' => now()->toISOString(),
]);
```

**After (Correct)**:
```php
$qrCodeData = json_encode([
    'ticket_id' => $ticket->id,
    'ticket_number' => $ticket->ticket_number,
    'qr_code' => $ticket->qr_code,
    'event_id' => $ticket->event_id,
    'attendee_name' => $ticket->attendee_name,
    'generated_at' => now()->toISOString(),
]);
```

## Database Schema Consistency

### Current Correct Schema
```sql
-- orders table
CREATE TABLE orders (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT UNSIGNED NOT NULL,  -- ✅ Correct column name
    user_id BIGINT UNSIGNED NOT NULL,
    -- other columns...
    FOREIGN KEY (event_id) REFERENCES events(id)
);

-- tickets table  
CREATE TABLE tickets (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    event_id BIGINT UNSIGNED NOT NULL,  -- ✅ Correct column name
    user_id BIGINT UNSIGNED NOT NULL,
    -- other columns...
    FOREIGN KEY (event_id) REFERENCES events(id)
);
```

### Model Relationships
```php
// Event Model
public function orders()
{
    return $this->hasMany(Order::class, 'event_id');  // ✅ Correct
}

public function tickets()
{
    return $this->hasMany(Ticket::class, 'event_id');  // ✅ Correct
}

// Order Model
public function event()
{
    return $this->belongsTo(Event::class, 'event_id');  // ✅ Correct
}

// Ticket Model
public function event()
{
    return $this->belongsTo(Event::class, 'event_id');  // ✅ Correct
}
```

## Testing Results

### Before Fix
```
❌ SQLSTATE[42S22]: Column not found: 1054 Unknown column 'tiket_id'
❌ Organizer dashboard crashes at line 375
❌ Ticket purchase functionality broken
❌ Admin bulk actions not working
❌ Seeder fails to create test data
❌ Inconsistent database references
```

### After Fix
```
✅ All SQL queries using correct 'event_id' column
✅ Organizer dashboard loads successfully
✅ Ticket purchase functionality working
✅ Admin bulk actions working
✅ Seeder creates test data successfully
✅ Consistent database references throughout
✅ No more 'tiket_id' references found
```

## Verification Commands

### Check for Remaining References
```bash
# Search for any remaining tiket_id references
grep -r "tiket_id" app/ database/ --exclude-dir=vendor --exclude="*.md" | grep -v "migration"

# Should return empty (no results)
```

### Test Database Queries
```bash
php artisan tinker --execute="
// Test organizer dashboard queries
\$organizer = \App\Models\User::where('role', 'penjual')->first();
if (\$organizer) {
    \$startDate = \Carbon\Carbon::now()->subDays(30);
    \$tickets = \App\Models\Event::where('organizer_id', \$organizer->id)
        ->where('created_at', '>=', \$startDate)
        ->get();
    
    \$totalViews = \$tickets->sum('view_count');
    \$totalOrders = \App\Models\Order::whereIn('event_id', \$tickets->pluck('id'))
        ->where('payment_status', 'paid')
        ->count();
    
    echo '✓ Conversion rate query works! Views: ' . \$totalViews . ', Orders: ' . \$totalOrders;
}
"
```

### Test Application
```bash
# Test main application
curl -X GET "http://127.0.0.1:8000" -H "Accept: text/html" -I

# Test organizer dashboard (requires login)
curl -X GET "http://127.0.0.1:8000/organizer/dashboard" -H "Accept: text/html" -I
```

## Impact Analysis

### Affected Functionality
- ✅ **Organizer Dashboard**: Conversion rate calculations now work
- ✅ **Ticket Purchase**: Duplicate ticket checking works
- ✅ **Admin Panel**: Bulk actions for events work
- ✅ **Notifications**: Event-related notifications have correct data
- ✅ **QR Codes**: Generated with correct event references
- ✅ **Database Seeding**: Test data creation works

### Performance Impact
- **Positive**: Queries now use proper indexes on `event_id`
- **Positive**: No more failed queries causing errors
- **Positive**: Consistent foreign key relationships

## Best Practices Applied

### 1. Consistent Naming
- All foreign key columns follow Laravel convention: `{model}_id`
- Consistent across all models and relationships
- Clear and descriptive column names

### 2. Database Integrity
- Proper foreign key constraints
- Consistent data types
- Indexed foreign key columns

### 3. Code Quality
- Eliminated legacy column references
- Consistent model relationships
- Proper validation rules

## Future Prevention

### 1. Code Review
- Always check column names in new code
- Verify foreign key relationships
- Test database queries before deployment

### 2. Testing
- Include database query tests
- Test all CRUD operations
- Verify seeder functionality

### 3. Documentation
- Document all database schema changes
- Update model relationship documentation
- Maintain migration history

## Related Files

### Controllers
- `app/Http/Controllers/Organizer/DashboardController.php`
- `app/Http/Controllers/TicketController.php`
- `app/Http/Controllers/Admin/TicketController.php`

### Livewire Components
- `app/Http/Livewire/TicketPurchase.php`

### Seeders
- `database/seeders/TicketSeeder.php`
- `database/seeders/NotificationSeeder.php`

### Models
- `app/Models/Event.php`
- `app/Models/Order.php`
- `app/Models/Ticket.php`

## Related Documentation
- [QR Code and SQL Fixes](QR_CODE_AND_SQL_FIXES.md)
- [Error Fixes](ERROR_FIXES.md)
- [Storage 404 Fix](STORAGE_404_FIX.md)
