<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // UangTix Balance for all users
        Schema::create('uangtix_balances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('balance', 15, 2)->default(0);
            $table->decimal('total_earned', 15, 2)->default(0);
            $table->decimal('total_spent', 15, 2)->default(0);
            $table->decimal('total_deposited', 15, 2)->default(0);
            $table->decimal('total_withdrawn', 15, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['user_id']);
            $table->index(['is_active']);
        });

        // UangTix Transaction History
        Schema::create('uangtix_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_number')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['deposit', 'withdrawal', 'purchase', 'refund', 'admin_add', 'admin_deduct', 'transfer_in', 'transfer_out']);
            $table->decimal('amount', 15, 2);
            $table->decimal('balance_before', 15, 2);
            $table->decimal('balance_after', 15, 2);
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('completed');
            $table->string('description');
            $table->json('metadata')->nullable();

            // Related records
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('event_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('admin_id')->nullable()->constrained('users')->onDelete('set null');

            // Transfer related
            $table->foreignId('from_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('to_user_id')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();

            $table->index(['user_id', 'type']);
            $table->index(['status']);
            $table->index(['created_at']);
            $table->index(['transaction_number']);
        });

        // UangTix Exchange Rates (for conversion from IDR)
        Schema::create('uangtix_exchange_rates', function (Blueprint $table) {
            $table->id();
            $table->decimal('rate_idr_to_uangtix', 10, 4)->default(1.0000); // 1 IDR = X UangTix
            $table->decimal('rate_uangtix_to_idr', 10, 4)->default(1.0000); // 1 UangTix = X IDR
            $table->decimal('min_deposit_idr', 10, 2)->default(10000); // Minimum deposit in IDR
            $table->decimal('max_deposit_idr', 10, 2)->default(10000000); // Maximum deposit in IDR
            $table->decimal('min_withdrawal_uangtix', 10, 2)->default(10); // Minimum withdrawal in UangTix
            $table->decimal('deposit_fee_percentage', 5, 2)->default(0); // Fee for deposits
            $table->decimal('withdrawal_fee_percentage', 5, 2)->default(2.5); // Fee for withdrawals
            $table->boolean('deposits_enabled')->default(true);
            $table->boolean('withdrawals_enabled')->default(true);
            $table->boolean('transfers_enabled')->default(true);
            $table->text('terms_and_conditions')->nullable();
            $table->timestamps();
        });

        // UangTix Deposit/Withdrawal Requests
        Schema::create('uangtix_requests', function (Blueprint $table) {
            $table->id();
            $table->string('request_number')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['deposit', 'withdrawal']);
            $table->decimal('amount_idr', 15, 2)->nullable(); // For deposits
            $table->decimal('amount_uangtix', 15, 2); // UangTix amount
            $table->decimal('fee_amount', 15, 2)->default(0);
            $table->decimal('final_amount', 15, 2); // Final amount after fees
            $table->enum('status', ['pending', 'approved', 'rejected', 'completed', 'failed'])->default('pending');

            // Payment information
            $table->string('payment_method')->nullable();
            $table->json('payment_data')->nullable();
            $table->string('payment_reference')->nullable();

            // Bank information for withdrawals
            $table->string('bank_name')->nullable();
            $table->string('bank_account_number')->nullable();
            $table->string('bank_account_name')->nullable();

            // Admin handling
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('processed_at')->nullable();
            $table->text('admin_notes')->nullable();
            $table->text('rejection_reason')->nullable();

            $table->timestamps();

            $table->index(['user_id', 'type']);
            $table->index(['status']);
            $table->index(['created_at']);
        });

        // UangTix Promotions/Bonuses
        Schema::create('uangtix_promotions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description');
            $table->enum('type', ['deposit_bonus', 'cashback', 'referral_bonus', 'event_discount']);
            $table->decimal('bonus_percentage', 5, 2)->default(0); // Percentage bonus
            $table->decimal('bonus_amount', 10, 2)->default(0); // Fixed bonus amount
            $table->decimal('min_amount', 10, 2)->default(0); // Minimum amount to qualify
            $table->decimal('max_bonus', 10, 2)->default(0); // Maximum bonus amount
            $table->integer('usage_limit')->nullable(); // Total usage limit
            $table->integer('usage_per_user')->default(1); // Usage limit per user
            $table->integer('used_count')->default(0); // Current usage count
            $table->boolean('is_active')->default(true);
            $table->date('start_date');
            $table->date('end_date');
            $table->timestamps();

            $table->index(['code']);
            $table->index(['is_active']);
            $table->index(['start_date', 'end_date']);
        });

        // UangTix Promotion Usage
        Schema::create('uangtix_promotion_usage', function (Blueprint $table) {
            $table->id();
            $table->foreignId('promotion_id')->constrained('uangtix_promotions')->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('transaction_id')->nullable()->constrained('uangtix_transactions')->onDelete('set null');
            $table->decimal('bonus_amount', 10, 2);
            $table->timestamps();

            $table->unique(['promotion_id', 'user_id', 'transaction_id'], 'utx_promo_usage_unique');
            $table->index(['user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uangtix_promotion_usage');
        Schema::dropIfExists('uangtix_promotions');
        Schema::dropIfExists('uangtix_requests');
        Schema::dropIfExists('uangtix_exchange_rates');
        Schema::dropIfExists('uangtix_transactions');
        Schema::dropIfExists('uangtix_balances');
    }
};
