<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Image Driver
    |--------------------------------------------------------------------------
    |
    | Intervention Image supports "GD Library" and "Imagick" to process images
    | internally. You may choose one of them according to your PHP
    | configuration. By default PHP's "GD Library" implementation is used.
    |
    | Supported: "gd", "imagick"
    |
    */

    'driver' => env('IMAGE_DRIVER', 'gd'),

    /*
    |--------------------------------------------------------------------------
    | Image Cache
    |--------------------------------------------------------------------------
    |
    | Define if processed images should be cached. This can improve
    | performance when the same image is processed multiple times.
    |
    */

    'cache' => [
        'enabled' => env('IMAGE_CACHE_ENABLED', true),
        'lifetime' => env('IMAGE_CACHE_LIFETIME', 60), // minutes
        'path' => storage_path('app/cache/images'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Quality Settings
    |--------------------------------------------------------------------------
    |
    | Define default quality settings for different image formats.
    |
    */

    'quality' => [
        'jpeg' => env('IMAGE_JPEG_QUALITY', 85),
        'png' => env('IMAGE_PNG_QUALITY', 9),
        'webp' => env('IMAGE_WEBP_QUALITY', 85),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Size Limits
    |--------------------------------------------------------------------------
    |
    | Define maximum dimensions and file sizes for image processing.
    |
    */

    'limits' => [
        'max_width' => env('IMAGE_MAX_WIDTH', 2048),
        'max_height' => env('IMAGE_MAX_HEIGHT', 2048),
        'max_file_size' => env('IMAGE_MAX_FILE_SIZE', 5120), // KB
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Image Sizes
    |--------------------------------------------------------------------------
    |
    | Define default sizes for different image types in your application.
    |
    */

    'sizes' => [
        'thumbnail' => [
            'width' => 150,
            'height' => 150,
        ],
        'small' => [
            'width' => 300,
            'height' => 300,
        ],
        'medium' => [
            'width' => 600,
            'height' => 600,
        ],
        'large' => [
            'width' => 1200,
            'height' => 1200,
        ],
        'poster' => [
            'width' => 800,
            'height' => 600,
        ],
        'gallery' => [
            'width' => 1200,
            'height' => 800,
        ],
        'avatar' => [
            'width' => 200,
            'height' => 200,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Watermark Settings
    |--------------------------------------------------------------------------
    |
    | Define default watermark settings.
    |
    */

    'watermark' => [
        'enabled' => env('IMAGE_WATERMARK_ENABLED', false),
        'path' => public_path('images/watermark.png'),
        'position' => 'bottom-right',
        'opacity' => 50,
        'margin' => 10,
    ],

];
