<?php
/**
 * <PERSON><PERSON>t to fix Imagick PHP startup warning
 * This script will comment out the imagick extension in php.ini
 */

echo "🔧 Fixing Imagick PHP Startup Warning...\n";
echo "=====================================\n\n";

// Get php.ini file path
$phpIniPath = php_ini_loaded_file();

if (!$phpIniPath) {
    echo "❌ Error: Could not find php.ini file\n";
    exit(1);
}

echo "📁 PHP.ini file: {$phpIniPath}\n";

// Check if file exists and is readable
if (!file_exists($phpIniPath)) {
    echo "❌ Error: php.ini file does not exist\n";
    exit(1);
}

if (!is_readable($phpIniPath)) {
    echo "❌ Error: php.ini file is not readable\n";
    exit(1);
}

if (!is_writable($phpIniPath)) {
    echo "❌ Error: php.ini file is not writable\n";
    echo "💡 Try running as administrator or change file permissions\n";
    exit(1);
}

// Read php.ini content
$content = file_get_contents($phpIniPath);
if ($content === false) {
    echo "❌ Error: Could not read php.ini file\n";
    exit(1);
}

echo "📖 Reading php.ini content...\n";

// Create backup
$backupPath = $phpIniPath . '.backup.' . date('Y-m-d-H-i-s');
if (file_put_contents($backupPath, $content) === false) {
    echo "❌ Error: Could not create backup file\n";
    exit(1);
}

echo "💾 Backup created: {$backupPath}\n";

// Find and comment out imagick extensions
$lines = explode("\n", $content);
$modified = false;
$foundExtensions = [];

foreach ($lines as $index => &$line) {
    $trimmedLine = trim($line);
    
    // Check for imagick extension lines
    if (preg_match('/^extension\s*=\s*imagick/i', $trimmedLine) || 
        preg_match('/^extension\s*=\s*php_imagick/i', $trimmedLine)) {
        
        if (!str_starts_with($trimmedLine, ';')) {
            $line = ';' . $line . ' ; Commented out by fix-imagick-warning script';
            $foundExtensions[] = $trimmedLine;
            $modified = true;
            echo "🔧 Commented out: {$trimmedLine}\n";
        } else {
            echo "ℹ️  Already commented: {$trimmedLine}\n";
        }
    }
}

if (!$modified) {
    echo "ℹ️  No active imagick extensions found in php.ini\n";
    echo "🔍 Checking for other possible configurations...\n";
    
    // Check for other possible imagick references
    foreach ($lines as $line) {
        if (stripos($line, 'imagick') !== false && !str_starts_with(trim($line), ';')) {
            echo "🔍 Found reference: " . trim($line) . "\n";
        }
    }
} else {
    // Write modified content back to php.ini
    $newContent = implode("\n", $lines);
    
    if (file_put_contents($phpIniPath, $newContent) === false) {
        echo "❌ Error: Could not write to php.ini file\n";
        exit(1);
    }
    
    echo "✅ Successfully modified php.ini\n";
    echo "📝 Extensions commented out:\n";
    foreach ($foundExtensions as $ext) {
        echo "   • {$ext}\n";
    }
}

echo "\n🔄 Next steps:\n";
echo "1. Restart your web server (Apache/Nginx)\n";
echo "2. Restart PHP-FPM if using it\n";
echo "3. Test with: php -v (should not show imagick warning)\n";

echo "\n💡 Alternative solutions:\n";
echo "1. Install proper Imagick extension for Windows\n";
echo "2. Use GD driver (already configured in your app)\n";
echo "3. Remove imagick from php.ini completely\n";

echo "\n📚 Your application is configured to use GD driver, so this should not affect functionality.\n";

echo "\n✅ Script completed successfully!\n";
?>
