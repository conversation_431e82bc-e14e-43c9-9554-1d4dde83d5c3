@extends('layouts.admin')

@section('title', $title)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $title }}</h1>
            <p class="mt-1 text-sm text-gray-600">Buat kategori event baru untuk platform</p>
        </div>
        <a href="{{ route('admin.categories.index') }}" 
           class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
            <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
            Kembali
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border">
        <form method="POST" action="{{ route('admin.categories.store') }}" class="p-6 space-y-6">
            @csrf

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Nama Kategori <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           name="name" 
                           id="name"
                           value="{{ old('name') }}"
                           placeholder="Contoh: Musik & Konser"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @error('name') border-red-500 @enderror"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Slug -->
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                        Slug <span class="text-gray-400">(Opsional)</span>
                    </label>
                    <input type="text" 
                           name="slug" 
                           id="slug"
                           value="{{ old('slug') }}"
                           placeholder="musik-konser (otomatis dibuat jika kosong)"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @error('slug') border-red-500 @enderror">
                    @error('slug')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">URL-friendly version dari nama kategori</p>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Deskripsi <span class="text-red-500">*</span>
                </label>
                <textarea name="description" 
                          id="description"
                          rows="3"
                          placeholder="Deskripsi singkat tentang kategori ini..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @error('description') border-red-500 @enderror"
                          required>{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Visual Settings -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Icon -->
                <div>
                    <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                        Icon <span class="text-red-500">*</span>
                    </label>
                    <select name="icon" 
                            id="icon"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @error('icon') border-red-500 @enderror"
                            required>
                        <option value="">Pilih Icon</option>
                        <option value="musical-note" {{ old('icon') === 'musical-note' ? 'selected' : '' }}>🎵 Musical Note</option>
                        <option value="academic-cap" {{ old('icon') === 'academic-cap' ? 'selected' : '' }}>🎓 Academic Cap</option>
                        <option value="flag" {{ old('icon') === 'flag' ? 'selected' : '' }}>🏁 Flag</option>
                        <option value="sparkles" {{ old('icon') === 'sparkles' ? 'selected' : '' }}>✨ Sparkles</option>
                        <option value="cake" {{ old('icon') === 'cake' ? 'selected' : '' }}>🍰 Cake</option>
                        <option value="device-gamepad-2" {{ old('icon') === 'device-gamepad-2' ? 'selected' : '' }}>🎮 Gamepad</option>
                        <option value="briefcase" {{ old('icon') === 'briefcase' ? 'selected' : '' }}>💼 Briefcase</option>
                        <option value="heart" {{ old('icon') === 'heart' ? 'selected' : '' }}>❤️ Heart</option>
                        <option value="book-open" {{ old('icon') === 'book-open' ? 'selected' : '' }}>📖 Book</option>
                        <option value="car" {{ old('icon') === 'car' ? 'selected' : '' }}>🚗 Car</option>
                        <option value="shirt" {{ old('icon') === 'shirt' ? 'selected' : '' }}>👕 Shirt</option>
                        <option value="users" {{ old('icon') === 'users' ? 'selected' : '' }}>👥 Users</option>
                        <option value="moon" {{ old('icon') === 'moon' ? 'selected' : '' }}>🌙 Moon</option>
                        <option value="camera" {{ old('icon') === 'camera' ? 'selected' : '' }}>📷 Camera</option>
                        <option value="palette" {{ old('icon') === 'palette' ? 'selected' : '' }}>🎨 Palette</option>
                        <option value="trophy" {{ old('icon') === 'trophy' ? 'selected' : '' }}>🏆 Trophy</option>
                        <option value="plane" {{ old('icon') === 'plane' ? 'selected' : '' }}>✈️ Plane</option>
                        <option value="coffee" {{ old('icon') === 'coffee' ? 'selected' : '' }}>☕ Coffee</option>
                    </select>
                    @error('icon')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Color -->
                <div>
                    <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                        Warna <span class="text-red-500">*</span>
                    </label>
                    <div class="flex items-center space-x-2">
                        <input type="color" 
                               name="color" 
                               id="color"
                               value="{{ old('color', '#A8D5BA') }}"
                               class="w-12 h-10 border border-gray-300 rounded cursor-pointer @error('color') border-red-500 @enderror">
                        <input type="text" 
                               id="colorText"
                               value="{{ old('color', '#A8D5BA') }}"
                               placeholder="#A8D5BA"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               readonly>
                    </div>
                    @error('color')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                        Urutan <span class="text-gray-400">(Opsional)</span>
                    </label>
                    <input type="number" 
                           name="sort_order" 
                           id="sort_order"
                           value="{{ old('sort_order') }}"
                           min="0"
                           placeholder="Otomatis jika kosong"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @error('sort_order') border-red-500 @enderror">
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Status -->
            <div>
                <label class="flex items-center">
                    <input type="checkbox" 
                           name="is_active" 
                           value="1"
                           {{ old('is_active', true) ? 'checked' : '' }}
                           class="rounded border-gray-300 text-primary focus:ring-primary">
                    <span class="ml-2 text-sm text-gray-700">Aktifkan kategori</span>
                </label>
                <p class="mt-1 text-xs text-gray-500">Kategori yang tidak aktif tidak akan ditampilkan di frontend</p>
            </div>

            <!-- Preview -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                <div id="categoryPreview" class="max-w-sm">
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                        <!-- Category Header -->
                        <div class="relative h-32 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                            <div id="previewGradient" class="absolute inset-0 bg-gradient-to-br opacity-80"></div>
                            
                            <!-- Icon -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="w-16 h-16 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center shadow-lg">
                                    <i id="previewIcon" data-lucide="help-circle" class="w-8 h-8 text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Category Content -->
                        <div class="p-6">
                            <h3 id="previewName" class="text-xl font-bold text-gray-900 mb-2">
                                Nama Kategori
                            </h3>
                            <p id="previewDescription" class="text-gray-600 text-sm mb-4 line-clamp-2">
                                Deskripsi kategori akan muncul di sini...
                            </p>
                            
                            <!-- Stats -->
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-4 text-sm text-gray-500">
                                    <span class="flex items-center gap-1">
                                        <i data-lucide="calendar" class="w-4 h-4"></i>
                                        0 event
                                    </span>
                                </div>
                            </div>
                            
                            <!-- Action Button -->
                            <div class="block w-full bg-gradient-to-r from-primary to-secondary text-white text-center py-3 rounded-xl font-semibold">
                                Jelajahi Event
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t">
                <a href="{{ route('admin.categories.index') }}" 
                   class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                    Simpan Kategori
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    const descriptionInput = document.getElementById('description');
    const iconSelect = document.getElementById('icon');
    const colorInput = document.getElementById('color');
    const colorText = document.getElementById('colorText');
    
    // Preview elements
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewIcon = document.getElementById('previewIcon');
    const previewGradient = document.getElementById('previewGradient');
    
    // Auto-generate slug from name
    nameInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.value === slugify(nameInput.dataset.oldValue || '')) {
            slugInput.value = slugify(this.value);
        }
        nameInput.dataset.oldValue = this.value;
        updatePreview();
    });
    
    // Update preview on input changes
    [nameInput, descriptionInput, iconSelect, colorInput].forEach(input => {
        input.addEventListener('input', updatePreview);
    });
    
    // Color input sync
    colorInput.addEventListener('input', function() {
        colorText.value = this.value;
        updatePreview();
    });
    
    function slugify(text) {
        return text
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
    }
    
    function updatePreview() {
        // Update name
        previewName.textContent = nameInput.value || 'Nama Kategori';
        
        // Update description
        previewDescription.textContent = descriptionInput.value || 'Deskripsi kategori akan muncul di sini...';
        
        // Update icon
        if (iconSelect.value) {
            previewIcon.setAttribute('data-lucide', iconSelect.value);
            previewIcon.style.color = colorInput.value;
            // Re-initialize lucide icons
            if (window.lucide) {
                window.lucide.createIcons();
            }
        }
        
        // Update gradient
        const color = colorInput.value;
        previewGradient.style.background = `linear-gradient(135deg, ${color}20, ${color}40)`;
    }
    
    // Initial preview update
    updatePreview();
});
</script>
@endpush
@endsection
