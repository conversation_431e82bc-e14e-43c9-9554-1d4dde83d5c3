{{--
/**
 * My Feedback Page
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: <PERSON><PERSON><PERSON> Nazula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */
--}}

@extends('layouts.app')

@section('title', 'Feedback Saya - TiXara')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Feedback Saya</h1>
                    <p class="text-gray-600 dark:text-gray-400">Pantau status feedback yang telah Anda kirimkan</p>
                </div>
                <a href="{{ route('feedback.index') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                    Kirim Feedback Baru
                </a>
            </div>
        </div>

        @if($feedbacks->count() > 0)
            <!-- Feedback List -->
            <div class="space-y-6">
                @foreach($feedbacks as $feedback)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-300">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div class="flex-1">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                                        {{ $feedback->title }}
                                    </h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ $feedback->feedback_number }} • {{ $feedback->created_at->format('d M Y, H:i') }}
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $feedback->type_color }}-100 text-{{ $feedback->type_color }}-800 dark:bg-{{ $feedback->type_color }}-900/20 dark:text-{{ $feedback->type_color }}-400">
                                        {{ $feedback->type_label }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        {{ $feedback->category_label }}
                                    </span>
                                </div>
                            </div>
                            
                            @if($feedback->rating)
                                <div class="flex items-center mb-3">
                                    <span class="text-sm text-gray-600 dark:text-gray-400 mr-2">Rating:</span>
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= $feedback->rating)
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                            @else
                                                <i data-lucide="star" class="w-4 h-4 text-gray-300"></i>
                                            @endif
                                        @endfor
                                        <span class="text-sm text-gray-600 dark:text-gray-400 ml-2">({{ $feedback->rating }}/5)</span>
                                    </div>
                                </div>
                            @endif
                            
                            <p class="text-gray-700 dark:text-gray-300 mb-4 line-clamp-2">
                                {{ Str::limit($feedback->message, 150) }}
                            </p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-{{ $feedback->status_color }}-100 text-{{ $feedback->status_color }}-800 dark:bg-{{ $feedback->status_color }}-900/20 dark:text-{{ $feedback->status_color }}-400">
                                        <div class="w-2 h-2 bg-{{ $feedback->status_color }}-400 rounded-full mr-2"></div>
                                        {{ $feedback->status_label }}
                                    </span>
                                    
                                    @if($feedback->hasResponse())
                                        <span class="inline-flex items-center text-sm text-green-600 dark:text-green-400">
                                            <i data-lucide="message-circle" class="w-4 h-4 mr-1"></i>
                                            Ada Respon
                                        </span>
                                    @endif
                                    
                                    @if($feedback->responded_at)
                                        <span class="text-sm text-gray-500 dark:text-gray-400">
                                            Direspon: {{ $feedback->responded_at->format('d M Y') }}
                                        </span>
                                    @endif
                                </div>
                                
                                <a href="{{ route('feedback.show', $feedback) }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                                    Lihat Detail
                                    <i data-lucide="arrow-right" class="w-4 h-4 ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $feedbacks->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="message-square" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Belum Ada Feedback
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                    Anda belum pernah mengirimkan feedback. Suara Anda sangat berharga untuk membantu kami meningkatkan layanan.
                </p>
                <a href="{{ route('feedback.index') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                    Kirim Feedback Pertama
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
@endpush
