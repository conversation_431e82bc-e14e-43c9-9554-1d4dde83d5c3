@props(['categories', 'title' => 'Kategori Event', 'subtitle' => 'Jelajahi berbagai kategori event menarik', 'showAll' => true])

<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                {{ $title }}
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                {{ $subtitle }}
            </p>
        </div>

        <!-- Categories Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-6 mb-8">
            @foreach($categories as $index => $category)
            <div class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden transform hover:-translate-y-2" 
                 data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                
                <!-- Category Header -->
                <div class="relative h-32 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br opacity-80" 
                         style="background: linear-gradient(135deg, {{ $category->color }}20, {{ $category->color }}40)"></div>
                    
                    <!-- Decorative Elements -->
                    <div class="absolute top-4 right-4 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm"></div>
                    <div class="absolute bottom-4 left-4 w-6 h-6 rounded-full bg-white/30 backdrop-blur-sm"></div>
                    
                    <!-- Icon -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-16 h-16 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <i data-lucide="{{ $category->icon }}" class="w-8 h-8" style="color: {{ $category->color }}"></i>
                        </div>
                    </div>
                </div>

                <!-- Category Content -->
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary transition-colors">
                        {{ $category->name }}
                    </h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                        {{ $category->description }}
                    </p>
                    
                    <!-- Stats -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="text-sm text-gray-500">
                            <span class="flex items-center gap-1">
                                <i data-lucide="calendar" class="w-4 h-4"></i>
                                {{ $category->events_count ?? 0 }} event
                            </span>
                        </div>
                        @if($category->events_count > 0)
                        <div class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                            Tersedia
                        </div>
                        @endif
                    </div>
                    
                    <!-- Action Button -->
                    <a href="{{ route('categories.show', $category->slug) }}" 
                       class="block w-full bg-gradient-to-r from-primary to-secondary text-white text-center py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 group-hover:from-secondary group-hover:to-primary">
                        Jelajahi
                    </a>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Show All Button -->
        @if($showAll && $categories->count() >= 8)
        <div class="text-center" data-aos="fade-up" data-aos-delay="400">
            <a href="{{ route('categories.index') }}" 
               class="inline-flex items-center px-8 py-3 bg-white border-2 border-primary text-primary rounded-full font-semibold hover:bg-primary hover:text-white transition-all duration-300 shadow-lg hover:shadow-xl">
                <span>Lihat Semua Kategori</span>
                <i data-lucide="arrow-right" class="w-5 h-5 ml-2"></i>
            </a>
        </div>
        @endif
    </div>
</section>
