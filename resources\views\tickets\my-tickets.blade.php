@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">

    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                <div>
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                        Tiket Saya
                    </h1>
                    <p class="text-lg text-gray-600" data-aos="fade-up" data-aos-delay="100">
                        Kelola dan lihat semua tiket yang Anda miliki
                    </p>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 md:mt-0" data-aos="fade-left">
                    <div class="bg-white rounded-xl p-4 text-center shadow-lg">
                        <div class="text-2xl font-bold text-primary">{{ $stats['total'] }}</div>
                        <div class="text-sm text-gray-600">Total</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-lg">
                        <div class="text-2xl font-bold text-green-600">{{ $stats['active'] }}</div>
                        <div class="text-sm text-gray-600">Aktif</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-lg">
                        <div class="text-2xl font-bold text-blue-600">{{ $stats['used'] }}</div>
                        <div class="text-sm text-gray-600">Digunakan</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-lg">
                        <div class="text-2xl font-bold text-orange-600">{{ $stats['upcoming'] }}</div>
                        <div class="text-sm text-gray-600">Mendatang</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters & Search -->
    <section class="pb-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                <form method="GET" action="{{ route('tickets.my-tickets') }}" class="flex flex-col md:flex-row gap-4">
                    <!-- Search -->
                    <div class="flex-1">
                        <input type="text"
                               name="event"
                               value="{{ request('event') }}"
                               placeholder="Cari berdasarkan nama event..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <select name="status" class="px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                            <option value="">Semua Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                            <option value="used" {{ request('status') == 'used' ? 'selected' : '' }}>Digunakan</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                        </select>
                    </div>

                    <!-- Sort -->
                    <div>
                        <select name="sort" class="px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                            <option value="created_at" {{ request('sort') == 'created_at' ? 'selected' : '' }}>Terbaru</option>
                            <option value="event_date" {{ request('sort') == 'event_date' ? 'selected' : '' }}>Tanggal Event</option>
                        </select>
                    </div>

                    <!-- Submit -->
                    <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">
                        Filter
                    </button>

                    @if(request()->hasAny(['event', 'status', 'sort']))
                        <a href="{{ route('tickets.my-tickets') }}" class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200">
                            Reset
                        </a>
                    @endif
                </form>
            </div>
        </div>
    </section>

    <!-- Tickets List -->
    <section class="pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($tickets->count() > 0)
                <div class="space-y-6">
                    @foreach($tickets as $ticket)
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                            <div class="md:flex">
                                <!-- Event Image -->
                                <div class="md:w-48 h-48 md:h-auto">
                                    <img src="{{ $ticket->event->poster_url }}"
                                         alt="{{ $ticket->event->title }}"
                                         class="w-full h-full object-cover">
                                </div>

                                <!-- Ticket Content -->
                                <div class="flex-1 p-6">
                                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                                        <div>
                                            <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $ticket->event->title }}</h3>
                                            <div class="space-y-1 text-sm text-gray-600">
                                                <div class="flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                                    </svg>
                                                    {{ $ticket->event->start_date->format('d M Y, H:i') }} WIB
                                                </div>
                                                <div class="flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                    </svg>
                                                    {{ $ticket->event->venue_name }}, {{ $ticket->event->city }}
                                                </div>
                                                <div class="flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                                    </svg>
                                                    {{ $ticket->ticket_number }}
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Status Badge -->
                                        <div class="mt-4 md:mt-0">
                                            @if($ticket->status === 'active')
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-green-100 text-green-800">
                                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                                    Aktif
                                                </span>
                                            @elseif($ticket->status === 'used')
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-blue-100 text-blue-800">
                                                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                                    Digunakan
                                                </span>
                                            @elseif($ticket->status === 'cancelled')
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-red-100 text-red-800">
                                                    <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                                    Dibatalkan
                                                </span>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Attendee Info -->
                                    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                                        <h4 class="font-semibold text-gray-900 mb-2">Informasi Peserta</h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-600">Nama:</span>
                                                <span class="font-semibold ml-2">{{ $ticket->attendee_name }}</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Email:</span>
                                                <span class="font-semibold ml-2">{{ $ticket->attendee_email }}</span>
                                            </div>
                                            @if($ticket->attendee_phone)
                                                <div>
                                                    <span class="text-gray-600">Telepon:</span>
                                                    <span class="font-semibold ml-2">{{ $ticket->attendee_phone }}</span>
                                                </div>
                                            @endif
                                            <div>
                                                <span class="text-gray-600">Harga:</span>
                                                <span class="font-semibold ml-2">Rp {{ number_format($ticket->total_paid, 0, ',', '.') }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex flex-wrap gap-3">
                                        <a href="{{ route('tickets.show', $ticket) }}"
                                           class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            Lihat Detail
                                        </a>

                                        @if($ticket->status === 'active')
                                            <button onclick="showQRCode('{{ $ticket->qr_code }}', '{{ $ticket->ticket_number }}')"
                                                    class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h4M4 4h4m0 0V4m0 0h4m0 0v4M4 16h4m0 0v4m0 0h4m0 0v-4"/>
                                                </svg>
                                                QR Code
                                            </button>

                                            <a href="{{ route('tickets.download', $ticket) }}"
                                               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                </svg>
                                                Download
                                            </a>

                                            @if($ticket->event->start_date > now()->addHours(24))
                                                <button onclick="cancelTicket('{{ $ticket->id }}')"
                                                        class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                    </svg>
                                                    Batalkan
                                                </button>
                                            @endif
                                        @endif

                                        @if($ticket->status === 'used')
                                            <span class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                Digunakan pada {{ $ticket->used_at->format('d M Y H:i') }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12" data-aos="fade-up">
                    {{ $tickets->links('pagination.custom') }}
                </div>
            @else
                <!-- No Tickets -->
                <div class="text-center py-16" data-aos="fade-up">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum ada tiket</h3>
                    <p class="text-gray-600 mb-6">Anda belum memiliki tiket apapun. Mulai jelajahi event menarik!</p>
                    <a href="{{ route('tickets.index') }}"
                       class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        Jelajahi Event
                    </a>
                </div>
            @endif
        </div>
    </section>
</div>

<!-- QR Code Modal -->
<div id="qrModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50" onclick="closeQRModal()">
    <div class="bg-white rounded-2xl p-8 max-w-sm mx-4" onclick="event.stopPropagation()">
        <div class="text-center">
            <h3 class="text-xl font-bold text-gray-900 mb-4">QR Code Tiket</h3>
            <div id="qrCodeContainer" class="mb-4">
                <!-- QR Code will be inserted here -->
            </div>
            <p id="ticketNumber" class="text-sm text-gray-600 mb-6"></p>
            <button onclick="closeQRModal()"
                    class="w-full bg-primary text-white py-3 rounded-lg hover:bg-primary/90 transition-colors duration-200">
                Tutup
            </button>
        </div>
    </div>
</div>

<!-- Cancel Ticket Modal -->
<div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-8 max-w-md mx-4">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Batalkan Tiket</h3>
        <p class="text-gray-600 mb-6">Apakah Anda yakin ingin membatalkan tiket ini? Tindakan ini tidak dapat dibatalkan.</p>

        <form id="cancelForm" method="POST">
            @csrf
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Alasan pembatalan</label>
                <textarea name="reason"
                          required
                          rows="3"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none"
                          placeholder="Jelaskan alasan pembatalan..."></textarea>
            </div>

            <div class="flex space-x-4">
                <button type="button"
                        onclick="closeCancelModal()"
                        class="flex-1 bg-gray-300 text-gray-700 py-3 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                    Batal
                </button>
                <button type="submit"
                        class="flex-1 bg-red-600 text-white py-3 rounded-lg hover:bg-red-700 transition-colors duration-200">
                    Batalkan Tiket
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
function showQRCode(qrData, ticketNumber) {
    const modal = document.getElementById('qrModal');
    const container = document.getElementById('qrCodeContainer');
    const ticketNumberElement = document.getElementById('ticketNumber');

    // Clear previous QR code
    container.innerHTML = '';

    // Generate QR code
    QRCode.toCanvas(qrData, { width: 200, margin: 2 }, function (error, canvas) {
        if (error) {
            console.error(error);
            container.innerHTML = '<p class="text-red-500">Gagal membuat QR Code</p>';
        } else {
            container.appendChild(canvas);
        }
    });

    ticketNumberElement.textContent = `Tiket: ${ticketNumber}`;
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

function closeQRModal() {
    const modal = document.getElementById('qrModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
}

function cancelTicket(ticketId) {
    const modal = document.getElementById('cancelModal');
    const form = document.getElementById('cancelForm');

    form.action = `/tickets/${ticketId}/cancel`;
    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

function closeCancelModal() {
    const modal = document.getElementById('cancelModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const filterInputs = filterForm.querySelectorAll('select');

    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            filterForm.submit();
        });
    });
});
</script>
@endpush
