<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver as GdDriver;
use App\Services\ImageService;

class ImageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('image', function () {
            // Override any configuration that might try to use Imagick
            $configDriver = config('image.driver', 'gd');
            if ($configDriver === 'imagick') {
                \Log::info('Imagick driver requested but forcing GD for compatibility');
            }

            // Ensure GD is available
            if (!extension_loaded('gd')) {
                throw new \Exception('GD extension is required for image processing but not available');
            }

            // Always use GD driver for maximum compatibility
            return new ImageManager(new GdDriver());
        });

        // Register alias for the Image facade
        $this->app->alias('image', ImageManager::class);

        // Register ImageService
        $this->app->singleton(ImageService::class, fn() => new ImageService());
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/image.php' => config_path('image.php'),
        ], 'image-config');

        // Create cache directory if it doesn't exist
        if (config('image.cache.enabled')) {
            $cachePath = config('image.cache.path');
            if (!file_exists($cachePath)) {
                mkdir($cachePath, 0755, true);
            }
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return ['image', ImageManager::class];
    }
}
