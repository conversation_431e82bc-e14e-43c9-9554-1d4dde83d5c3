/**
 * Live Notification System
 * Handles real-time notifications with sound alerts
 */

class LiveNotificationSystem {
    constructor() {
        this.lastCheck = null;
        this.pollInterval = 30000; // 30 seconds
        this.isPolling = false;
        this.audioContext = null;
        this.soundEnabled = true;
        this.notificationPermission = 'default';
        
        this.init();
    }

    async init() {
        // Request notification permission
        await this.requestNotificationPermission();
        
        // Initialize audio context
        this.initAudioContext();
        
        // Start polling for notifications
        this.startPolling();
        
        // Listen for visibility changes to adjust polling
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPolling();
            } else {
                this.startPolling();
            }
        });

        // Listen for user interactions to enable audio
        document.addEventListener('click', () => this.enableAudio(), { once: true });
        document.addEventListener('keydown', () => this.enableAudio(), { once: true });
    }

    async requestNotificationPermission() {
        if ('Notification' in window) {
            this.notificationPermission = await Notification.requestPermission();
        }
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Audio context not supported:', error);
            this.soundEnabled = false;
        }
    }

    enableAudio() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    startPolling() {
        if (this.isPolling) return;
        
        this.isPolling = true;
        this.pollForNotifications();
        
        this.pollTimer = setInterval(() => {
            this.pollForNotifications();
        }, this.pollInterval);
    }

    stopPolling() {
        this.isPolling = false;
        if (this.pollTimer) {
            clearInterval(this.pollTimer);
            this.pollTimer = null;
        }
    }

    async pollForNotifications() {
        try {
            const params = new URLSearchParams();
            if (this.lastCheck) {
                params.append('last_check', this.lastCheck);
            }

            const response = await fetch(`/notifications/latest?${params}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                }
            });

            if (!response.ok) return;

            const data = await response.json();
            
            // Update last check timestamp
            this.lastCheck = new Date().toISOString();
            
            // Process new notifications
            if (data.has_new && data.notifications.length > 0) {
                this.handleNewNotifications(data.notifications);
            }
            
            // Update notification count in header
            this.updateNotificationCount(data.unread_count);
            
        } catch (error) {
            console.error('Error polling notifications:', error);
        }
    }

    handleNewNotifications(notifications) {
        notifications.forEach(notification => {
            // Play sound for new notification
            this.playNotificationSound(notification.priority);
            
            // Show browser notification
            this.showBrowserNotification(notification);
            
            // Show in-app notification
            this.showInAppNotification(notification);
            
            // Update header dropdown
            this.addToHeaderDropdown(notification);
        });
    }

    playNotificationSound(priority = 'normal') {
        if (!this.soundEnabled || !this.audioContext) return;

        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            // Different sounds for different priorities
            const soundConfig = {
                'low': { frequency: 400, duration: 0.2 },
                'normal': { frequency: 600, duration: 0.3 },
                'high': { frequency: 800, duration: 0.4 },
                'urgent': { frequency: 1000, duration: 0.6 }
            };
            
            const config = soundConfig[priority] || soundConfig.normal;
            
            oscillator.frequency.setValueAtTime(config.frequency, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(
                config.frequency * 0.8, 
                this.audioContext.currentTime + config.duration
            );
            
            gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + config.duration);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + config.duration);
            
            // For urgent notifications, play multiple beeps
            if (priority === 'urgent') {
                setTimeout(() => this.playNotificationSound('normal'), 300);
                setTimeout(() => this.playNotificationSound('normal'), 600);
            }
            
        } catch (error) {
            console.error('Error playing notification sound:', error);
        }
    }

    showBrowserNotification(notification) {
        if (this.notificationPermission !== 'granted') return;
        
        const browserNotification = new Notification(notification.title, {
            body: notification.message,
            icon: '/icons/icon-192x192.png',
            badge: '/icons/icon-96x96.png',
            tag: `notification-${notification.id}`,
            requireInteraction: notification.priority === 'urgent',
            silent: false
        });
        
        browserNotification.onclick = () => {
            window.focus();
            if (notification.action_url) {
                window.location.href = notification.action_url;
            }
            browserNotification.close();
        };
        
        // Auto close after 5 seconds (except urgent)
        if (notification.priority !== 'urgent') {
            setTimeout(() => browserNotification.close(), 5000);
        }
    }

    showInAppNotification(notification) {
        // Use existing notification system
        window.dispatchEvent(new CustomEvent('show-notification', {
            detail: {
                type: this.getNotificationType(notification.type),
                title: notification.title,
                message: notification.message,
                duration: this.getNotificationDuration(notification.priority),
                action: notification.action_url ? {
                    text: 'Lihat',
                    callback: () => window.location.href = notification.action_url
                } : null
            }
        }));
    }

    getNotificationType(type) {
        const typeMap = {
            'system': 'info',
            'event': 'info',
            'payment': 'success',
            'order': 'success',
            'ticket': 'success'
        };
        return typeMap[type] || 'info';
    }

    getNotificationDuration(priority) {
        const durationMap = {
            'low': 3000,
            'normal': 5000,
            'high': 7000,
            'urgent': 10000
        };
        return durationMap[priority] || 5000;
    }

    updateNotificationCount(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'flex' : 'none';
            
            // Add animation for new notifications
            if (count > 0) {
                badge.classList.add('animate-pulse');
                setTimeout(() => badge.classList.remove('animate-pulse'), 2000);
            }
        }
    }

    addToHeaderDropdown(notification) {
        const dropdown = document.querySelector('.notifications-dropdown-content');
        if (!dropdown) return;
        
        const notificationElement = this.createNotificationElement(notification);
        dropdown.insertBefore(notificationElement, dropdown.firstChild);
        
        // Remove old notifications if too many
        const notifications = dropdown.querySelectorAll('.notification-item');
        if (notifications.length > 10) {
            notifications[notifications.length - 1].remove();
        }
    }

    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = 'notification-item px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100';
        element.dataset.notificationId = notification.id;
        
        const colorClass = this.getColorClass(notification.type);
        
        element.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-2 h-2 ${colorClass} rounded-full mt-2 ${notification.is_read ? 'opacity-50' : ''}"></div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                    <p class="text-sm text-gray-600">${notification.message}</p>
                    <p class="text-xs text-gray-500 mt-1">${notification.created_at}</p>
                </div>
                <button class="text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        `;
        
        // Add click handler
        element.addEventListener('click', (e) => {
            if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'SVG' && e.target.tagName !== 'PATH') {
                this.markAsRead(notification.id);
                if (notification.action_url) {
                    window.location.href = notification.action_url;
                }
            }
        });
        
        return element;
    }

    getColorClass(type) {
        const colorMap = {
            'system': 'bg-blue-500',
            'event': 'bg-purple-500',
            'payment': 'bg-green-500',
            'order': 'bg-orange-500',
            'ticket': 'bg-indigo-500'
        };
        return colorMap[type] || 'bg-gray-500';
    }

    async markAsRead(notificationId) {
        try {
            await fetch(`/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                }
            });
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('meta[name="user-authenticated"]')) {
        window.liveNotifications = new LiveNotificationSystem();
    }
});

// Export for global access
window.LiveNotificationSystem = LiveNotificationSystem;
