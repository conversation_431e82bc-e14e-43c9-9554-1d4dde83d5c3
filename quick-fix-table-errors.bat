@echo off
echo Quick Fix for Table Errors - TiXara

echo.
echo [1/5] Disabling foreign key checks...
php artisan tinker --execute="
\Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=0;');
echo 'Foreign key checks disabled';
"

echo.
echo [2/5] Dropping problematic tables...
php artisan tinker --execute="
try {
    // Drop tickets table if it has wrong structure
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets')) {
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
        \$eventColumns = ['title', 'description', 'venue_name'];
        \$hasEventColumns = count(array_intersect(\$eventColumns, \$columns)) > 0;
        
        if (\$hasEventColumns) {
            echo 'Dropping tickets table with wrong structure...' . PHP_EOL;
            \Illuminate\Support\Facades\Schema::dropIfExists('tickets');
        }
    }
    
    // Drop events table if it exists
    if (\Illuminate\Support\Facades\Schema::hasTable('events')) {
        echo 'Dropping existing events table...' . PHP_EOL;
        \Illuminate\Support\Facades\Schema::dropIfExists('events');
    }
    
    echo 'Problematic tables dropped' . PHP_EOL;
    
} catch (Exception \$e) {
    echo 'Drop error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [3/5] Creating events table...
php artisan migrate --path=database/migrations/2024_01_01_000003_create_events_table.php --force

echo.
echo [4/5] Creating tickets table...
php artisan migrate --path=database/migrations/2024_01_01_000005_create_tickets_table.php --force

echo.
echo [5/5] Re-enabling foreign key checks...
php artisan tinker --execute="
\Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=1;');
echo 'Foreign key checks re-enabled';
"

echo.
echo Quick fix completed! 
echo.
echo Next steps:
echo 1. Run: php artisan migrate (for remaining migrations)
echo 2. Run: php artisan db:seed (for sample data)
echo.
pause
