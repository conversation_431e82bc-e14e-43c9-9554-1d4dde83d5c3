<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration is a safety net to fix any foreign key issues
     * that might occur during the initial migration process.
     */
    public function up(): void
    {
        // Check if we need to fix any foreign key constraints
        $this->fixForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Nothing to rollback as this is a fix migration
    }

    /**
     * Fix foreign key constraints if needed
     */
    private function fixForeignKeyConstraints(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            // Check and fix tickets table foreign keys
            if (Schema::hasTable('tickets') && Schema::hasTable('categories') && Schema::hasTable('users')) {
                if (!$this->foreignKeyExists('tickets', 'tickets_category_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
                    });
                }

                if (!$this->foreignKeyExists('tickets', 'tickets_organizer_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('organizer_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }
            }

            // Check and fix orders table foreign keys
            if (Schema::hasTable('orders') && Schema::hasTable('users') && Schema::hasTable('tickets')) {
                if (!$this->foreignKeyExists('orders', 'orders_user_id_foreign')) {
                    Schema::table('orders', function (Blueprint $table) {
                        $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }

                if (!$this->foreignKeyExists('orders', 'orders_event_id_foreign')) {
                    Schema::table('orders', function (Blueprint $table) {
                        $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
                    });
                }
            }

            // Check and fix tickets table foreign keys
            if (Schema::hasTable('tickets') && Schema::hasTable('users') && Schema::hasTable('events') && Schema::hasTable('orders')) {
                if (!$this->foreignKeyExists('tickets', 'tickets_event_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
                    });
                }

                if (!$this->foreignKeyExists('tickets', 'tickets_buyer_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('buyer_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }

                if (!$this->foreignKeyExists('tickets', 'tickets_order_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
                    });
                }

                if (!$this->foreignKeyExists('tickets', 'tickets_validated_by_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('validated_by')->references('id')->on('users');
                    });
                }
            }

        } catch (Exception $e) {
            // Log the error but don't fail the migration
            \Log::error('Foreign key fix migration error: ' . $e->getMessage());
        } finally {
            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }

    /**
     * Check if foreign key constraint exists
     */
    private function foreignKeyExists(string $table, string $constraintName): bool
    {
        $result = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.TABLE_CONSTRAINTS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = ?
            AND CONSTRAINT_NAME = ?
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'
        ", [$table, $constraintName]);

        return count($result) > 0;
    }
};
