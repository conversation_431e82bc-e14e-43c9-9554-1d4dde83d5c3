<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Header -->
        <div class="mb-8" data-aos="fade-up">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent mb-2">
                        <i data-lucide="target" class="inline-block w-10 h-10 mr-3 text-primary-600"></i>
                        Dashboard Organizer
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300 text-lg">
                        Kelola event <span class="font-semibold text-primary-700">TikPro</span> dan pantau performa penjualan Anda
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Selamat datang kembali, <span class="font-medium text-primary-600"><?php echo e(auth()->user()->name); ?></span> • <?php echo e(now()->format('l, d F Y')); ?>

                    </p>
                </div>
                <div class="mt-6 md:mt-0 flex flex-wrap gap-3">
                    <a href="<?php echo e(route('organizer.tickets.create')); ?>"
                       class="group px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <i data-lucide="plus" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                        Buat Event Baru
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Tickets -->
            <div class="bg-white dark:bg-dark-700 rounded-2xl shadow-xl p-6 border border-gray-100 dark:border-dark-600 transform hover:scale-105 transition-all duration-300" data-aos="fade-up" data-aos-delay="100">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-4 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-700 rounded-xl">
                        <i data-lucide="calendar" class="w-7 h-7 text-primary-600 dark:text-primary-300"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">Total Event</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($stats['total_tickets'] ?? $stats['total'] ?? 0); ?></p>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-primary-600 font-semibold"><?php echo e($stats['published_tickets']); ?> Published</span>
                    <span class="text-gray-400 mx-2">•</span>
                    <span class="text-warning font-semibold"><?php echo e($stats['draft_tickets']); ?> Draft</span>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white dark:bg-dark-700 rounded-2xl shadow-xl p-6 border border-gray-100 dark:border-dark-600 transform hover:scale-105 transition-all duration-300" data-aos="fade-up" data-aos-delay="200">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-4 bg-gradient-to-br from-accent-100 to-accent-200 dark:from-accent-800 dark:to-accent-700 rounded-xl">
                        <i data-lucide="dollar-sign" class="w-7 h-7 text-accent-600 dark:text-accent-300"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">Total Revenue</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">Rp <?php echo e(number_format($stats['total_revenue'], 0, ',', '.')); ?></p>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-accent-600 font-semibold">
                        +Rp <?php echo e(number_format($stats['period_revenue'], 0, ',', '.')); ?>

                    </span>
                    <span class="text-gray-400 mx-2">•</span>
                    <span class="text-gray-600 dark:text-gray-400"><?php echo e($dateRange); ?> hari terakhir</span>
                </div>
            </div>

            <!-- Tickets Sold -->
            <div class="bg-white dark:bg-dark-700 rounded-2xl shadow-xl p-6 border border-gray-100 dark:border-dark-600 transform hover:scale-105 transition-all duration-300" data-aos="fade-up" data-aos-delay="300">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-4 bg-gradient-to-br from-pasta-sage/20 to-pasta-sage/30 dark:from-pasta-sage/40 dark:to-pasta-sage/30 rounded-xl">
                        <i data-lucide="ticket" class="w-7 h-7 text-pasta-sage"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">Tiket Terjual</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e(number_format($stats['total_tickets_sold'], 0, ',', '.')); ?></p>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-pasta-sage font-semibold">
                        +<?php echo e(number_format($stats['period_tickets_sold'], 0, ',', '.')); ?>

                    </span>
                    <span class="text-gray-400 mx-2">•</span>
                    <span class="text-gray-600 dark:text-gray-400"><?php echo e($dateRange); ?> hari terakhir</span>
                </div>
            </div>

            <!-- Total Customers -->
            <div class="bg-white dark:bg-dark-700 rounded-2xl shadow-xl p-6 border border-gray-100 dark:border-dark-600 transform hover:scale-105 transition-all duration-300" data-aos="fade-up" data-aos-delay="400">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-4 bg-gradient-to-br from-pasta-peach/20 to-pasta-salmon/30 dark:from-pasta-peach/40 dark:to-pasta-salmon/30 rounded-xl">
                        <i data-lucide="users" class="w-7 h-7 text-pasta-salmon"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">Total Pelanggan</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e(number_format($stats['total_customers'], 0, ',', '.')); ?></p>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-pasta-salmon font-semibold">
                        +<?php echo e(number_format($stats['period_customers'], 0, ',', '.')); ?>

                    </span>
                    <span class="text-gray-400 mx-2">•</span>
                    <span class="text-gray-600 dark:text-gray-400"><?php echo e($dateRange); ?> hari terakhir</span>
                </div>
            </div>
        </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Revenue Chart -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="500">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Revenue Harian</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-primary text-white rounded-lg">7 Hari</button>
                    <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">30 Hari</button>
                </div>
            </div>
            <div class="h-64 flex items-center justify-center text-gray-500">
                <div class="text-center">
                    <svg class="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <p>Chart akan ditampilkan di sini</p>
                </div>
            </div>
        </div>

        <!-- Event Performance -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="600">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Performa Event</h2>
                <a href="<?php echo e(route('organizer.tickets.index')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                    Lihat Semua
                </a>
            </div>
            <div class="space-y-4">
                <?php $__currentLoopData = $eventPerformance->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900"><?php echo e($event['title']); ?></h3>
                        <p class="text-sm text-gray-600">
                            <?php echo e($event['sold_tickets']); ?>/<?php echo e($event['total_capacity']); ?> tiket terjual
                        </p>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-900">
                            Rp <?php echo e(number_format($event['revenue'], 0, ',', '.')); ?>

                        </p>
                        <p class="text-sm text-gray-600"><?php echo e($event['conversion_rate']); ?>%</p>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Recent Activities & Upcoming Tickets -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Activities -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="700">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Aktivitas Terbaru</h2>
                <div class="space-y-4">
                    <?php $__currentLoopData = $recentActivities->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-start space-x-4">
                        <div class="p-2 bg-primary/10 rounded-lg">
                            <?php if($activity['type'] === 'order'): ?>
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                </svg>
                            <?php else: ?>
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium text-gray-900"><?php echo e($activity['title']); ?></h3>
                            <p class="text-sm text-gray-600"><?php echo e($activity['description']); ?></p>
                            <p class="text-xs text-gray-500 mt-1">
                                <?php echo e($activity['created_at']->diffForHumans()); ?>

                            </p>
                        </div>
                        <?php if(isset($activity['amount'])): ?>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">
                                +Rp <?php echo e(number_format($activity['amount'], 0, ',', '.')); ?>

                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Tickets -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="800">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Event Mendatang</h2>
                    <a href="<?php echo e(route('organizer.tickets.create')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                        Buat Event
                    </a>
                </div>
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $upcomingTickets->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                            <span class="text-primary font-semibold text-sm">
                                <?php echo e(\Carbon\Carbon::parse($event['start_date'])->format('d')); ?>

                            </span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate"><?php echo e($event['title']); ?></h3>
                            <p class="text-sm text-gray-600">
                                <?php echo e(\Carbon\Carbon::parse($event['start_date'])->format('d M Y, H:i')); ?>

                            </p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                <?php echo e(($event['status'] ?? 'draft') === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                <?php echo e(ucfirst($event['status'] ?? 'draft')); ?>

                            </span>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <p class="text-gray-600">Belum ada event mendatang</p>
                        <a href="<?php echo e(route('organizer.tickets.create')); ?>" class="text-primary hover:text-primary/80 transition-colors">
                            Buat event pertama Anda
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/organizer/dashboard.blade.php ENDPATH**/ ?>