<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">

    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-6" aria-label="Breadcrumb" data-aos="fade-right">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="<?php echo e(route('home')); ?>" class="text-gray-500 hover:text-primary transition-colors duration-200">
                            Beranda
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <a href="<?php echo e(route('tickets.show', $event)); ?>" class="ml-1 text-gray-500 hover:text-primary transition-colors duration-200 md:ml-2"><?php echo e($event->title); ?></a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="ml-1 text-gray-700 md:ml-2">Beli Tiket</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    Beli Tiket Event
                </h1>
                <p class="text-lg text-gray-600" data-aos="fade-up" data-aos-delay="100">
                    Lengkapi informasi di bawah untuk membeli tiket
                </p>
            </div>
        </div>
    </section>

    <!-- Purchase Form -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Error Messages -->
            <?php if($errors->any()): ?>
                <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6" data-aos="fade-up">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <h3 class="text-sm font-semibold text-red-800 mb-2">Terjadi kesalahan:</h3>
                            <ul class="text-sm text-red-700 space-y-1">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>• <?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Success Messages -->
            <?php if(session('success')): ?>
                <div class="bg-green-50 border border-green-200 rounded-xl p-4 mb-6" data-aos="fade-up">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-green-700"><?php echo e(session('success')); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Warning Messages -->
            <?php if(session('error')): ?>
                <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6" data-aos="fade-up">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-red-700"><?php echo e(session('error')); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo e(route('tickets.store', $event)); ?>"
                  x-data="ticketPurchase()"
                  @submit="handleSubmit"
                  novalidate>
                <?php echo csrf_field(); ?>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                    <!-- Main Form -->
                    <div class="lg:col-span-2 space-y-6">

                        <!-- Event Summary -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Detail Event</h2>
                            <div class="flex items-start space-x-4">
                                <img src="<?php echo e($event->poster_url); ?>"
                                     alt="<?php echo e($event->title); ?>"
                                     class="w-20 h-20 object-cover rounded-lg">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 mb-2"><?php echo e($event->title); ?></h3>
                                    <div class="space-y-1 text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                            </svg>
                                            <?php echo e($event->start_date->format('d M Y, H:i')); ?> WIB
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            </svg>
                                            <?php echo e($event->venue_name); ?>, <?php echo e($event->city); ?>

                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                            </svg>
                                            Diselenggarakan oleh: <?php echo e($event->organization ?? $event->organizer->name ?? 'TikPro'); ?>

                                        </div>
                                        <?php if($event->price > 0): ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                                </svg>
                                                Rp <?php echo e(number_format($event->price, 0, ',', '.')); ?> / tiket
                                            </div>
                                        <?php else: ?>
                                            <div class="flex items-center text-green-600">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                Gratis
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Organizer Information -->
                            <?php if($event->organizer): ?>
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <h4 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                                        <svg class="w-4 h-4 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                        </svg>
                                        Penyelenggara
                                    </h4>
                                    <div class="flex items-start space-x-3">
                                        <?php if($event->organizer->avatar): ?>
                                            <img src="<?php echo e($event->organizer->avatar); ?>"
                                                 alt="<?php echo e($event->organizer->name); ?>"
                                                 class="w-12 h-12 rounded-full object-cover border-2 border-gray-200">
                                        <?php else: ?>
                                            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center border-2 border-gray-200">
                                                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-1">
                                                <h5 class="font-semibold text-gray-900"><?php echo e($event->organizer->name); ?></h5>
                                                <?php if($event->organizer->isPenjual() || $event->organizer->isAdmin()): ?>
                                                    <?php if (isset($component)) { $__componentOriginal63e2551db4476aff4bab3f62ef8c0aff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-level-badge','data' => ['user' => $event->organizer,'size' => 'xs']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-level-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($event->organizer),'size' => 'xs']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff)): ?>
<?php $attributes = $__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff; ?>
<?php unset($__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal63e2551db4476aff4bab3f62ef8c0aff)): ?>
<?php $component = $__componentOriginal63e2551db4476aff4bab3f62ef8c0aff; ?>
<?php unset($__componentOriginal63e2551db4476aff4bab3f62ef8c0aff); ?>
<?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                            <?php if($event->organizer->organization): ?>
                                                <p class="text-sm text-gray-600"><?php echo e($event->organizer->organization); ?></p>
                                            <?php endif; ?>
                                            <?php if($event->organizer->bio): ?>
                                                <p class="text-xs text-gray-500 mt-1 line-clamp-2"><?php echo e(Str::limit($event->organizer->bio, 100)); ?></p>
                                            <?php endif; ?>

                                            <!-- Social Media Links -->
                                            <?php if($event->organizer->social_media): ?>
                                                <div class="flex items-center space-x-3 mt-2">
                                                    <?php $__currentLoopData = $event->organizer->social_media; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($url): ?>
                                                            <a href="<?php echo e($url); ?>"
                                                               target="_blank"
                                                               rel="noopener noreferrer"
                                                               class="text-gray-400 hover:text-primary transition-colors duration-200"
                                                               title="<?php echo e(ucfirst($platform)); ?>">
                                                                <?php switch($platform):
                                                                    case ('instagram'): ?>
                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                                                        </svg>
                                                                        <?php break; ?>
                                                                    <?php case ('facebook'): ?>
                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                                        </svg>
                                                                        <?php break; ?>
                                                                    <?php case ('twitter'): ?>
                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                                                        </svg>
                                                                        <?php break; ?>
                                                                    <?php case ('linkedin'): ?>
                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                                                        </svg>
                                                                        <?php break; ?>
                                                                    <?php case ('youtube'): ?>
                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                                                        </svg>
                                                                        <?php break; ?>
                                                                    <?php case ('tiktok'): ?>
                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                                                            <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                                                                        </svg>
                                                                        <?php break; ?>
                                                                    <?php case ('website'): ?>
                                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                                                                        </svg>
                                                                        <?php break; ?>
                                                                    <?php default: ?>
                                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                                                        </svg>
                                                                <?php endswitch; ?>
                                                            </a>
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="100">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Jumlah Tiket</h2>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Pilih Jumlah Tiket
                                </label>
                                <div class="flex items-center space-x-4">
                                    <button type="button"
                                            @click="decreaseQuantity()"
                                            :disabled="quantity <= 1"
                                            class="w-12 h-12 rounded-full border-2 border-gray-300 flex items-center justify-center hover:border-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                        </svg>
                                    </button>

                                    <div class="flex-1 text-center">
                                        <input type="number"
                                               name="quantity"
                                               x-model="quantity"
                                               min="1"
                                               max="<?php echo e($maxQuantity); ?>"
                                               class="w-20 text-center text-2xl font-bold border-0 focus:ring-0 focus:outline-none">
                                        <p class="text-sm text-gray-500 mt-1">
                                            Maksimal <?php echo e($maxQuantity); ?> tiket
                                        </p>
                                    </div>

                                    <button type="button"
                                            @click="increaseQuantity()"
                                            :disabled="quantity >= maxQuantity"
                                            class="w-12 h-12 rounded-full border-2 border-gray-300 flex items-center justify-center hover:border-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <?php if($existingTickets > 0): ?>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <p class="text-sm text-blue-700">
                                            Anda sudah memiliki <?php echo e($existingTickets); ?> tiket untuk event ini.
                                        </p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Attendee Information -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                            <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                Informasi Peserta
                            </h2>

                            <!-- Personal Information -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Data Pribadi
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="attendee_name" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nama Lengkap <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               id="attendee_name"
                                               name="attendee_name"
                                               value="<?php echo e(old('attendee_name', auth()->user()->name)); ?>"
                                               required
                                               placeholder="Masukkan nama lengkap sesuai identitas"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        <?php $__errorArgs = ['attendee_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div>
                                        <label for="attendee_email" class="block text-sm font-medium text-gray-700 mb-2">
                                            Email <span class="text-red-500">*</span>
                                        </label>
                                        <input type="email"
                                               id="attendee_email"
                                               name="attendee_email"
                                               value="<?php echo e(old('attendee_email', auth()->user()->email)); ?>"
                                               required
                                               placeholder="<EMAIL>"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        <?php $__errorArgs = ['attendee_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div>
                                        <label for="attendee_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nomor Whatsapp <span class="text-red-500">*</span>
                                        </label>
                                        <input type="tel"
                                               id="attendee_phone"
                                               name="attendee_phone"
                                               value="<?php echo e(old('attendee_phone', auth()->user()->phone)); ?>"
                                               required
                                               placeholder="08xxxxxxxxxx"
                                               pattern="[0-9]{10,13}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        <?php $__errorArgs = ['attendee_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>




                                </div>
                            </div>

                            <!-- Identity Information -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"/>
                                    </svg>
                                    Identitas Diri
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="identity_type" class="block text-sm font-medium text-gray-700 mb-2">
                                            Jenis Identitas <span class="text-red-500">*</span>
                                        </label>
                                        <select id="identity_type"
                                                name="identity_type"
                                                required
                                                x-model="identityType"
                                                @change="updateIdentityPlaceholder()"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                            <option value="">Pilih jenis identitas</option>
                                            <option value="ktp" <?php echo e(old('identity_type') == 'ktp' ? 'selected' : ''); ?>>KTP (Kartu Tanda Penduduk)</option>
                                            <option value="sim" <?php echo e(old('identity_type') == 'sim' ? 'selected' : ''); ?>>SIM (Surat Izin Mengemudi)</option>
                                            <option value="passport" <?php echo e(old('identity_type') == 'passport' ? 'selected' : ''); ?>>Passport</option>
                                            <option value="kartu_pelajar" <?php echo e(old('identity_type') == 'kartu_pelajar' ? 'selected' : ''); ?>>Kartu Pelajar</option>
                                            <option value="ktm" <?php echo e(old('identity_type') == 'ktm' ? 'selected' : ''); ?>>KTM (Kartu Tanda Mahasiswa)</option>
                                            <option value="kta" <?php echo e(old('identity_type') == 'kta' ? 'selected' : ''); ?>>KTA (Kartu Tanda Anggota)</option>
                                        </select>
                                        <?php $__errorArgs = ['identity_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div>
                                        <label for="identity_number" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nomor Identitas <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               id="identity_number"
                                               name="identity_number"
                                               value="<?php echo e(old('identity_number')); ?>"
                                               required
                                               x-bind:placeholder="identityPlaceholder"
                                               x-bind:pattern="identityPattern"
                                               x-bind:maxlength="identityMaxLength"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200 font-mono">
                                        <?php $__errorArgs = ['identity_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <p class="text-xs text-gray-500 mt-1" x-text="identityHint"></p>
                                    </div>
                                </div>

                                <!-- Identity Type Information -->
                                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <div class="text-sm text-blue-800">
                                            <p class="font-semibold mb-2">Informasi Penting:</p>
                                            <ul class="space-y-1 text-xs">
                                                <li>• Pastikan data identitas sesuai dengan dokumen asli</li>
                                                <li>• Data akan digunakan untuk verifikasi saat check-in</li>
                                                <li>• Untuk keamanan, data identitas akan dienkripsi</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Emergency Contact -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    Kontak Darurat (Opsional)
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nama Kontak Darurat
                                        </label>
                                        <input type="text"
                                               id="emergency_contact_name"
                                               name="emergency_contact_name"
                                               value="<?php echo e(old('emergency_contact_name')); ?>"
                                               placeholder="Nama keluarga/teman terdekat"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        <?php $__errorArgs = ['emergency_contact_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div>
                                        <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nomor Kontak Darurat
                                        </label>
                                        <input type="tel"
                                               id="emergency_contact_phone"
                                               name="emergency_contact_phone"
                                               value="<?php echo e(old('emergency_contact_phone')); ?>"
                                               placeholder="08xxxxxxxxxx"
                                               pattern="[0-9]{10,13}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        <?php $__errorArgs = ['emergency_contact_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                        </svg>
                                        <div class="text-sm text-yellow-800">
                                            <p class="font-semibold mb-1">Kontak darurat akan dihubungi jika:</p>
                                            <ul class="space-y-1 text-xs">
                                                <li>• Terjadi keadaan darurat selama event</li>
                                                <li>• Anda tidak dapat dihubungi</li>
                                                <li>• Diperlukan konfirmasi medis</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- Payment Method -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="300" data-payment-methods>
                            <h2 class="text-xl font-bold text-gray-900 mb-6">Metode Pembayaran</h2>

                            <!-- Popular Methods -->
                            <div class="mb-6">
                                <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                    Metode Populer
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <!-- Bank Transfer -->
                                    <label class="relative">
                                        <input type="radio"
                                               name="payment_method"
                                               value="bank_transfer"
                                               class="sr-only peer"
                                               <?php echo e(old('payment_method', 'bank_transfer') == 'bank_transfer' ? 'checked' : ''); ?>>
                                        <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"/>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h3 class="font-semibold text-gray-900">Transfer Bank</h3>
                                                        <p class="text-sm text-gray-600">BCA, Mandiri, BNI, BRI</p>
                                                        <p class="text-xs text-gray-500">Proses: 5-10 menit • Biaya: Gratis</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </label>

                                    <!-- UangTix -->
                                    <label class="relative">
                                        <input type="radio"
                                               name="payment_method"
                                               value="uangtix"
                                               class="sr-only peer"
                                               <?php echo e(old('payment_method') == 'uangtix' ? 'checked' : ''); ?>>
                                        <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h3 class="font-semibold text-gray-900">UangTix</h3>
                                                        <p class="text-sm text-gray-600">Bayar dengan saldo UangTix</p>
                                                        <p class="text-xs text-gray-500">Proses: Instan • Biaya: Gratis</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- All Payment Methods -->
                            <div class="space-y-3">
                                <h3 class="text-sm font-semibold text-gray-700 mb-3">Semua Metode Pembayaran</h3>

                                <!-- E-Wallet -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="e_wallet"
                                           class="sr-only peer"
                                           <?php echo e(old('payment_method') == 'e_wallet' ? 'checked' : ''); ?>>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">E-Wallet</h3>
                                                    <p class="text-sm text-gray-600">GoPay, OVO, DANA, LinkAja, ShopeePay</p>
                                                    <p class="text-xs text-gray-500">Proses: Instan • Biaya: 1.5%</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <!-- Credit Card -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="credit_card"
                                           class="sr-only peer"
                                           <?php echo e(old('payment_method') == 'credit_card' ? 'checked' : ''); ?>>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">Kartu Kredit/Debit</h3>
                                                    <p class="text-sm text-gray-600">Visa, Mastercard, JCB, American Express</p>
                                                    <p class="text-xs text-gray-500">Proses: Instan • Biaya: 2.9%</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <!-- Virtual Account -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="virtual_account"
                                           class="sr-only peer"
                                           <?php echo e(old('payment_method') == 'virtual_account' ? 'checked' : ''); ?>>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">Virtual Account</h3>
                                                    <p class="text-sm text-gray-600">ATM, Mobile Banking, Internet Banking</p>
                                                    <p class="text-xs text-gray-500">Proses: 1-3 jam • Biaya: Rp 4,000</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <!-- Cash Payment -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="cash"
                                           class="sr-only peer"
                                           <?php echo e(old('payment_method') == 'cash' ? 'checked' : ''); ?>>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">Bayar di Tempat</h3>
                                                    <p class="text-sm text-gray-600">Bayar saat check-in event</p>
                                                    <p class="text-xs text-green-600 font-semibold">Gratis</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-4"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- UangTix Balance Information (shown only when UangTix is selected) -->
                        <div x-show="selectedPaymentMethod === 'uangtix'" class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200" x-cloak>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">Saldo UangTix Anda</h4>
                                    <?php if(auth()->guard()->check()): ?>
                                        <p class="text-lg font-bold text-gray-900"><?php echo e(auth()->user()->getUangTixBalance()->formatted_balance); ?></p>
                                        
                                        <!-- Show if balance is sufficient -->
                                        <p x-show="hasEnoughUangtixBalance" class="text-sm text-green-600 mt-1">
                                            <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Saldo mencukupi
                                        </p>
                                        
                                        <!-- Show if balance is insufficient -->
                                        <p x-show="!hasEnoughUangtixBalance" class="text-sm text-red-600 mt-1">
                                            <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Saldo tidak mencukupi
                                        </p>
                                    <?php else: ?>
                                        <p class="text-red-600 font-medium">Silakan login untuk menggunakan UangTix</p>
                                    <?php endif; ?>
                                </div>
                                
                                <div>
                                    <?php if(auth()->guard()->check()): ?>
                                        <?php if(auth()->user()->getUangTixBalance()->balance < $event->price): ?>
                                            <a href="<?php echo e(route('uangtix.index')); ?>" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                                Top Up Sekarang
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('login')); ?>?redirect=<?php echo e(url()->current()); ?>" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                            </svg>
                                            Login untuk Lanjutkan
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Terms & Conditions -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="400">
                            <label class="flex items-start space-x-3">
                                <input type="checkbox"
                                       name="terms_accepted"
                                       value="1"
                                       required
                                       class="mt-1 rounded border-gray-300 text-primary focus:ring-primary">
                                <div class="text-sm text-gray-600">
                                    Saya menyetujui <a href="#" class="text-primary hover:text-accent">syarat dan ketentuan</a>
                                    serta <a href="#" class="text-primary hover:text-accent">kebijakan privasi</a> TiXara.
                                </div>
                            </label>
                            <?php $__errorArgs = ['terms_accepted'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-2"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="sticky top-24">
                            <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                                <h2 class="text-xl font-bold text-gray-900 mb-6">Ringkasan Pesanan</h2>

                                <!-- Voucher Section -->
                                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <h3 class="text-sm font-semibold text-green-800 mb-3 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                        </svg>
                                        Kode Voucher
                                    </h3>

                                    <div class="flex space-x-2">
                                        <input type="text"
                                               x-model="voucherCode"
                                               placeholder="Masukkan kode voucher"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 text-sm"
                                               :disabled="voucherApplied">
                                        <button type="button"
                                                @click="applyVoucher()"
                                                :disabled="!voucherCode || voucherLoading || voucherApplied"
                                                :class="voucherApplied ? 'bg-gray-400' : 'bg-primary hover:bg-accent'"
                                                class="px-4 py-2 text-white rounded-lg font-medium text-sm transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <span x-show="!voucherLoading && !voucherApplied">Terapkan</span>
                                            <span x-show="voucherLoading">
                                                <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                            </span>
                                            <span x-show="voucherApplied">Diterapkan</span>
                                        </button>
                                    </div>

                                    <!-- Voucher Applied -->
                                    <div x-show="voucherApplied" class="mt-3 p-3 bg-green-100 border border-green-300 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                                </svg>
                                                <span class="text-sm font-medium text-green-800" x-text="voucherMessage"></span>
                                            </div>
                                            <button type="button"
                                                    @click="removeVoucher()"
                                                    class="text-green-600 hover:text-green-800 text-sm">
                                                Hapus
                                            </button>
                                        </div>
                                        <div class="mt-1 text-xs text-green-700" x-text="'Hemat ' + formatPrice(voucherDiscount) + ' (' + voucherSavingsPercentage + '%)'"></div>
                                    </div>

                                    <!-- Voucher Error -->
                                    <div x-show="voucherError" class="mt-3 p-3 bg-red-100 border border-red-300 rounded-lg">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                            </svg>
                                            <span class="text-sm text-red-800" x-text="voucherErrorMessage"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Price Breakdown -->
                                <div class="space-y-4 mb-6">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Harga tiket</span>
                                        <span x-text="formatPrice(<?php echo e($event->price); ?>)" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Jumlah</span>
                                        <span x-text="quantity + ' tiket'" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span x-text="formatPrice(subtotal)" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Biaya admin</span>
                                        <span x-text="formatPrice(adminFee)" class="font-semibold"></span>
                                    </div>

                                    <!-- Voucher Discount -->
                                    <div x-show="voucherApplied" class="flex justify-between items-center text-green-600">
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                            </svg>
                                            Diskon voucher
                                        </span>
                                        <span x-text="'-' + formatPrice(voucherDiscount)" class="font-semibold"></span>
                                    </div>

                                    <div class="border-t border-gray-200 pt-4">
                                        <div class="flex justify-between items-center">
                                            <span class="text-lg font-bold text-gray-900">Total</span>
                                            <span x-text="formatPrice(finalTotal)" class="text-lg font-bold text-primary"></span>
                                        </div>
                                        <div x-show="voucherApplied" class="text-right">
                                            <span class="text-sm text-gray-500 line-through" x-text="formatPrice(total)"></span>
                                            <span class="text-sm text-green-600 ml-2 font-medium" x-text="'Hemat ' + formatPrice(voucherDiscount)"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hidden Voucher Input -->
                                <input type="hidden" name="voucher_code" x-model="voucherCode" x-show="voucherApplied">

                                <!-- Purchase Button -->
                                <button type="submit"
                                        :disabled="loading"
                                        :class="loading ? 'opacity-75 cursor-not-allowed' : 'hover:shadow-lg transform hover:scale-105'"
                                        class="w-full bg-gradient-to-r from-primary to-secondary text-white py-4 rounded-xl font-bold text-lg transition-all duration-300">
                                    <span x-show="!loading" class="flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0L4 5H2m5 8h10m0 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6z"/>
                                        </svg>
                                        Beli Tiket Sekarang
                                    </span>
                                    <span x-show="loading" class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Memproses Pesanan...
                                    </span>
                                </button>

                                <!-- Additional Info -->
                                <div class="mt-4 text-center">
                                    <p class="text-sm text-gray-600">
                                        <svg class="w-4 h-4 inline mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                        </svg>
                                        Pesanan akan berlaku selama 30 menit untuk pembayaran
                                    </p>
                                </div>

                                <!-- Security Info -->
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                        </svg>
                                        Transaksi aman & terenkripsi
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function ticketPurchase() {
    return {
        quantity: 1,
        loading: false,
        eventPrice: <?php echo e($event->price); ?>,
        maxQuantity: <?php echo e($maxQuantity); ?>,
        selectedPaymentMethod: '<?php echo e(old('payment_method', 'bank_transfer')); ?>',
        identityType: '<?php echo e(old('identity_type', '')); ?>',
        identityPlaceholder: 'Pilih jenis identitas terlebih dahulu',
        identityPattern: '',
        identityMaxLength: '',
        identityHint: '',
        
        // UangTix properties
        uangtixBalance: <?php echo e(auth()->check() ? auth()->user()->getUangTixBalance()->balance : 0); ?>,
        hasUangtixAccess: <?php echo e(auth()->check() ? 'true' : 'false'); ?>,
        
        // Voucher properties
        voucherCode: '',
        voucherApplied: false,
        voucherLoading: false,
        voucherError: false,
        voucherErrorMessage: '',
        voucherMessage: '',
        voucherDiscount: 0,
        voucherSavingsPercentage: 0,
        appliedVoucher: null,

        init() {
            // Set default payment method
            this.selectedPaymentMethod = '<?php echo e(old('payment_method', 'bank_transfer')); ?>';

            // Set default identity type if exists
            if (this.identityType) {
                this.updateIdentityPlaceholder();
            }

            // Add form validation
            this.setupFormValidation();
        },
        
        // Check if UangTix balance is sufficient
        get hasEnoughUangtixBalance() {
            return this.uangtixBalance >= this.finalTotal;
        },
        
        // Get payment method fee
        get paymentMethodFee() {
            switch(this.selectedPaymentMethod) {
                case 'qris':
                    return Math.round(this.subtotal * 0.007); // 0.7%
                case 'e_wallet':
                    return Math.round(this.subtotal * 0.015); // 1.5%
                case 'credit_card':
                    return Math.round(this.subtotal * 0.029); // 2.9%
                case 'virtual_account':
                    return 4000; // Flat fee
                default:
                    return 0; // No fee for bank_transfer, uangtix, cash
            }
        },
        
        get subtotal() {
            return this.eventPrice * this.quantity;
        },
        
        get total() {
            return this.subtotal + this.paymentMethodFee;
        },
        
        get finalTotal() {
            return this.total - this.voucherDiscount;
        },
        
        increaseQuantity() {
            if (this.quantity < this.maxQuantity) {
                this.quantity++;
            }
        },

        decreaseQuantity() {
            if (this.quantity > 1) {
                this.quantity--;
            }
        },

        formatPrice(amount) {
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
        },

        selectPaymentMethod(method) {
            this.selectedPaymentMethod = method;
            // Update radio button
            const radio = document.querySelector(`input[name="payment_method"][value="${method}"]`);
            if (radio) {
                radio.checked = true;
            }
        },

        async applyVoucher() {
            if (!this.voucherCode.trim()) {
                return;
            }

            this.voucherLoading = true;
            this.voucherError = false;
            this.voucherErrorMessage = '';

            try {
                const response = await fetch('<?php echo e(route("vouchers.validate")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        voucher_code: this.voucherCode.trim().toUpperCase(),
                        event_id: <?php echo e($event->id); ?>,
                        order_amount: this.total,
                        ticket_quantity: this.quantity
                    })
                });

                const data = await response.json();

                if (data.success) {
                    this.voucherApplied = true;
                    this.voucherDiscount = data.discount;
                    this.voucherMessage = data.message;
                    this.voucherSavingsPercentage = data.savings_percentage || 0;
                    this.appliedVoucher = data.voucher;
                    this.voucherCode = data.voucher.code;

                    this.playNotificationSound('success');
                    this.showNotification('✅ Voucher berhasil diterapkan!', 'success');
                } else {
                    this.voucherError = true;
                    this.voucherErrorMessage = data.message;
                    this.playNotificationSound('error');
                }
            } catch (error) {
                console.error('Error applying voucher:', error);
                this.voucherError = true;
                this.voucherErrorMessage = 'Terjadi kesalahan saat memvalidasi voucher.';
                this.playNotificationSound('error');
            } finally {
                this.voucherLoading = false;
            }
        },

        removeVoucher() {
            this.voucherCode = '';
            this.voucherApplied = false;
            this.voucherDiscount = 0;
            this.voucherMessage = '';
            this.voucherSavingsPercentage = 0;
            this.appliedVoucher = null;
            this.voucherError = false;
            this.voucherErrorMessage = '';

            this.showNotification('Voucher dihapus', 'info');
        },

        validateForm() {
            let isValid = true;
            
            // Validate UangTix balance if selected
            if (this.selectedPaymentMethod === 'uangtix') {
                if (!this.hasUangtixAccess) {
                    this.showNotification('Silakan login terlebih dahulu untuk menggunakan UangTix.', 'error');
                    this.playNotificationSound('error');
                    isValid = false;
                } else if (!this.hasEnoughUangtixBalance) {
                    this.showNotification('Saldo UangTix Anda tidak mencukupi. Silakan top up terlebih dahulu.', 'error');
                    this.playNotificationSound('error');
                    isValid = false;
                }
            }
            
            // Other validations...
            
            return isValid;
        },

        getFormDebugData() {
            return {
                attendee_name: document.querySelector('[name="attendee_name"]')?.value || '',
                attendee_email: document.querySelector('[name="attendee_email"]')?.value || '',
                attendee_phone: document.querySelector('[name="attendee_phone"]')?.value || '',
                identity_type: document.querySelector('[name="identity_type"]')?.value || '',
                identity_number: document.querySelector('[name="identity_number"]')?.value || '',
                payment_method: document.querySelector('input[name="payment_method"]:checked')?.value || '',
                terms_accepted: document.querySelector('[name="terms_accepted"]')?.checked || false
            };
        },

        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        handleSubmit(event) {
            event.preventDefault();

            if (this.loading) {
                return false;
            }

            // Debug: Log form data before validation
            console.log('Form submission attempt:', this.getFormDebugData());

            // Validate form
            if (!this.validateForm()) {
                this.showNotification('Silakan lengkapi semua field yang wajib diisi.', 'error');
                this.playNotificationSound('error');
                return false;
            }

            // Additional custom validation
            if (!this.validateCustomFields()) {
                return false;
            }

            // Special handling for UangTix
            if (this.selectedPaymentMethod === 'uangtix' && !this.hasUangtixAccess) {
                window.location.href = '<?php echo e(route("login")); ?>?redirect=<?php echo e(url()->current()); ?>';
                return false;
            }

            this.loading = true;

            // Show loading notification
            this.showNotification('Memproses pesanan...', 'info');

            // Play processing sound
            this.playNotificationSound('processing');

            // Submit form
            setTimeout(() => {
                event.target.submit();
            }, 500);
        },

        validateCustomFields() {
            let isValid = true;
            const errors = [];

            // Validate phone number
            const phone = document.getElementById('attendee_phone').value;
            if (phone && !phone.match(/^08[0-9]{8,11}$/)) {
                errors.push('Nomor WhatsApp harus dimulai dengan 08 dan terdiri dari 10-13 digit');
                isValid = false;
            }

            // Validate identity number based on type
            const identityType = this.identityType;
            const identityNumber = document.getElementById('identity_number').value;

            if (identityType && identityNumber) {
                const validationRules = {
                    'ktp': /^[0-9]{16}$/,
                    'sim': /^[0-9]{12,14}$/,
                    'passport': /^[A-Z][0-9]{7}$/,
                    'kartu_pelajar': /^[0-9A-Za-z]{6,15}$/,
                    'ktm': /^[0-9A-Za-z]{8,15}$/,
                    'kta': /^[A-Za-z0-9]{6,15}$/
                };

                if (validationRules[identityType] && !identityNumber.match(validationRules[identityType])) {
                    errors.push('Format nomor identitas tidak sesuai dengan jenis identitas yang dipilih');
                    isValid = false;
                }
            }

            // Show errors if any
            if (errors.length > 0) {
                this.showNotification(errors.join('<br>'), 'error');
                this.playNotificationSound('error');
            }

            return isValid;
        },

        playNotificationSound(type = 'info') {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                const soundConfig = {
                    'success': { frequency: 800, duration: 0.3 },
                    'error': { frequency: 300, duration: 0.5 },
                    'processing': { frequency: 600, duration: 0.2 },
                    'info': { frequency: 500, duration: 0.2 }
                };

                const config = soundConfig[type] || soundConfig.info;

                oscillator.frequency.setValueAtTime(config.frequency, audioContext.currentTime);
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + config.duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + config.duration);

                // For success notifications, play multiple beeps
                if (type === 'success') {
                    setTimeout(() => this.playNotificationSound('info'), 200);
                    setTimeout(() => this.playNotificationSound('info'), 400);
                }

            } catch (error) {
                console.error('Error playing notification sound:', error);
            }
        },

        setupFormValidation() {
            // Real-time validation
            const form = document.querySelector('form');

            // Disable browser validation to use custom validation
            if (form) {
                form.setAttribute('novalidate', 'true');
            }

            // Email validation
            const emailInput = document.querySelector('[name="attendee_email"]');
            if (emailInput) {
                emailInput.addEventListener('blur', () => {
                    const email = emailInput.value;
                    if (email && !this.isValidEmail(email)) {
                        emailInput.classList.add('border-red-500');
                        this.showFieldError(emailInput, 'Format email tidak valid.');
                    } else {
                        emailInput.classList.remove('border-red-500');
                        this.clearFieldError(emailInput);
                    }
                });
            }

            // Name validation
            const nameInput = document.querySelector('[name="attendee_name"]');
            if (nameInput) {
                nameInput.addEventListener('blur', () => {
                    if (!nameInput.value.trim()) {
                        nameInput.classList.add('border-red-500');
                        this.showFieldError(nameInput, 'Nama lengkap wajib diisi.');
                    } else {
                        nameInput.classList.remove('border-red-500');
                        this.clearFieldError(nameInput);
                    }
                });
            }

            // Payment method validation
            const paymentInputs = document.querySelectorAll('input[name="payment_method"]');
            paymentInputs.forEach(input => {
                input.addEventListener('change', () => {
                    this.selectedPaymentMethod = input.value;
                    // Clear any payment method errors
                    const errorMsg = document.querySelector('[data-payment-methods] .error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                });
            });

            // Terms checkbox validation
            const termsCheckbox = document.querySelector('[name="terms_accepted"]');
            if (termsCheckbox) {
                termsCheckbox.addEventListener('change', () => {
                    // Clear terms errors when checked
                    if (termsCheckbox.checked) {
                        this.clearFieldError(termsCheckbox);
                    }
                });
            }
        },

        showFieldError(element, message) {
            this.clearFieldError(element);
            const errorMsg = document.createElement('p');
            errorMsg.className = 'field-error text-red-500 text-sm mt-1';
            errorMsg.textContent = message;
            element.parentNode.appendChild(errorMsg);
        },

        clearFieldError(element) {
            const existingError = element.parentNode.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }
        },

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        ${this.getIconPath(icon)}
                    </svg>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Animate out and remove
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        },

        getIconPath(icon) {
            const icons = {
                'check-circle': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>',
                'x-circle': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>',
                'info': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>'
            };
            return icons[icon] || icons.info;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for payment method cards
    document.querySelectorAll('label[class*="relative"]').forEach(label => {
        const radio = label.querySelector('input[type="radio"]');
        if (radio && radio.name === 'payment_method') {
            label.addEventListener('click', function() {
                // Update Alpine.js data
                const alpineData = Alpine.$data(document.querySelector('[x-data]'));
                if (alpineData) {
                    alpineData.selectedPaymentMethod = radio.value;
                }
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>








<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/tickets/purchase.blade.php ENDPATH**/ ?>