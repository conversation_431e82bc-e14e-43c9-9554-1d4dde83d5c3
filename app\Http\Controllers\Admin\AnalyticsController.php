<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Order;
use App\Models\User;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    public function index(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        // Revenue Analytics
        $revenueData = $this->getRevenueAnalytics($startDate, $endDate);
        
        // User Analytics
        $userAnalytics = $this->getUserAnalytics($startDate, $endDate);
        
        // Event Analytics
        $eventAnalytics = $this->getEventAnalytics($startDate, $endDate);
        
        // Geographic Analytics
        $geographicData = $this->getGeographicAnalytics($startDate, $endDate);
        
        // Category Performance
        $categoryPerformance = $this->getCategoryPerformance($startDate, $endDate);

        return view('pages.admin.analytics.index', compact(
            'revenueData',
            'userAnalytics', 
            'eventAnalytics',
            'geographicData',
            'categoryPerformance',
            'dateRange'
        ));
    }

    public function revenue(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $revenueData = $this->getRevenueAnalytics($startDate, $endDate);
        
        return response()->json($revenueData);
    }

    public function users(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $userAnalytics = $this->getUserAnalytics($startDate, $endDate);
        
        return response()->json($userAnalytics);
    }

    public function events(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $eventAnalytics = $this->getEventAnalytics($startDate, $endDate);
        
        return response()->json($eventAnalytics);
    }

    public function geographic(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $geographicData = $this->getGeographicAnalytics($startDate, $endDate);
        
        return response()->json($geographicData);
    }

    public function export(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $data = [
            'revenue' => $this->getRevenueAnalytics($startDate, $endDate),
            'users' => $this->getUserAnalytics($startDate, $endDate),
            'events' => $this->getEventAnalytics($startDate, $endDate),
            'geographic' => $this->getGeographicAnalytics($startDate, $endDate),
            'categories' => $this->getCategoryPerformance($startDate, $endDate),
        ];

        $filename = 'analytics_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.json';
        
        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    private function getRevenueAnalytics($startDate, $endDate)
    {
        $totalRevenue = Order::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_amount');

        $dailyRevenue = Order::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $previousPeriodRevenue = Order::where('status', 'completed')
            ->whereBetween('created_at', [$startDate->copy()->subDays($endDate->diffInDays($startDate)), $startDate])
            ->sum('total_amount');

        $growthRate = $previousPeriodRevenue > 0 
            ? (($totalRevenue - $previousPeriodRevenue) / $previousPeriodRevenue) * 100 
            : 0;

        return [
            'total_revenue' => $totalRevenue,
            'daily_revenue' => $dailyRevenue,
            'growth_rate' => round($growthRate, 2),
            'previous_period_revenue' => $previousPeriodRevenue
        ];
    }

    private function getUserAnalytics($startDate, $endDate)
    {
        $totalUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $totalOrganizers = User::where('role', 'penjual')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        $activeUsers = User::whereHas('orders', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();

        $dailyRegistrations = User::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as registrations')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total_users' => $totalUsers,
            'total_organizers' => $totalOrganizers,
            'active_users' => $activeUsers,
            'daily_registrations' => $dailyRegistrations
        ];
    }

    private function getEventAnalytics($startDate, $endDate)
    {
        $totalEvents = Event::whereBetween('created_at', [$startDate, $endDate])->count();
        $publishedEvents = Event::where('status', 'published')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        $totalTicketsSold = Order::where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('quantity');

        $topEvents = Event::withCount(['orders' => function($query) use ($startDate, $endDate) {
            $query->where('status', 'completed')
                  ->whereBetween('created_at', [$startDate, $endDate]);
        }])
        ->orderBy('orders_count', 'desc')
        ->take(10)
        ->get();

        return [
            'total_events' => $totalEvents,
            'published_events' => $publishedEvents,
            'total_tickets_sold' => $totalTicketsSold,
            'top_events' => $topEvents
        ];
    }

    private function getGeographicAnalytics($startDate, $endDate)
    {
        return Event::select('city as location', DB::raw('COUNT(*) as events_count'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('city')
            ->groupBy('city')
            ->orderBy('events_count', 'desc')
            ->take(10)
            ->get();
    }

    private function getCategoryPerformance($startDate, $endDate)
    {
        return Category::withCount(['events' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }])
        ->with(['events' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->withSum(['orders' => function($q) use ($startDate, $endDate) {
                      $q->where('status', 'completed')
                        ->whereBetween('created_at', [$startDate, $endDate]);
                  }], 'total_amount');
        }])
        ->orderBy('events_count', 'desc')
        ->take(10)
        ->get()
        ->map(function($category) {
            $category->total_revenue = $category->events->sum('orders_sum_total_amount') ?? 0;
            return $category;
        });
    }
}
