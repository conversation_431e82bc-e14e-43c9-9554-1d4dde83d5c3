@echo off
echo Fixing URL Routing Issues...

echo.
echo [1/8] Checking current web server configuration...
echo Current directory: %CD%
echo.

echo [2/8] Clearing Laravel caches...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo.
echo [3/8] Checking if .htaccess exists in root...
if exist ".htaccess" (
    echo ✓ Root .htaccess file exists
) else (
    echo ✗ Root .htaccess file missing - this might be the issue!
)

echo.
echo [4/8] Checking if public/.htaccess exists...
if exist "public\.htaccess" (
    echo ✓ Public .htaccess file exists
) else (
    echo ✗ Public .htaccess file missing!
)

echo.
echo [5/8] Testing route configuration...
php artisan route:list --name=tickets

echo.
echo [6/8] Testing specific routes...
php artisan tinker --execute="
try {
    echo 'Testing route generation:' . PHP_EOL;
    echo 'tickets.my-tickets: ' . route('tickets.my-tickets') . PHP_EOL;
    echo 'my-tickets: ' . route('my-tickets') . PHP_EOL;
    echo 'tickets.index: ' . route('tickets.index') . PHP_EOL;
    echo 'home: ' . route('home') . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [7/8] Checking APP_URL configuration...
php artisan tinker --execute="
echo 'APP_URL: ' . config('app.url') . PHP_EOL;
echo 'APP_ENV: ' . config('app.env') . PHP_EOL;
echo 'APP_DEBUG: ' . (config('app.debug') ? 'true' : 'false') . PHP_EOL;
"

echo.
echo [8/8] Testing URL generation and redirects...
php artisan tinker --execute="
try {
    // Test URL generation
    \$baseUrl = config('app.url');
    echo 'Base URL: ' . \$baseUrl . PHP_EOL;
    
    // Test route URLs
    \$routes = [
        'home' => route('home'),
        'tickets.index' => route('tickets.index'),
        'tickets.my-tickets' => route('tickets.my-tickets'),
        'my-tickets' => route('my-tickets'),
    ];
    
    foreach (\$routes as \$name => \$url) {
        echo \$name . ': ' . \$url . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Error testing URLs: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo ========================================
echo URL Routing Fix Summary
echo ========================================
echo.
echo The correct URLs should be:
echo ✓ http://localhost:8000/tickets/my-tickets (if using php artisan serve)
echo ✓ http://your-domain.com/tickets/my-tickets (if using web server)
echo.
echo WRONG URLs (will cause 404):
echo ✗ http://localhost/public/tickets/my-tickets
echo ✗ http://your-domain.com/public/tickets/my-tickets
echo.
echo Solutions:
echo 1. Use 'php artisan serve' for development
echo 2. Configure web server document root to 'public' folder
echo 3. Use the .htaccess file in root directory for URL rewriting
echo.
echo If you're using Laragon:
echo - Make sure virtual host points to the 'public' folder
echo - Or access via: http://project-tixara.my.id.test/tickets/my-tickets
echo.
pause
