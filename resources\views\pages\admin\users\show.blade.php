@extends('layouts.main')

@section('title', 'Detail Pengguna - ' . $user->name)

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6" data-aos="fade-up">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Detail Pengguna</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $user->name }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.users.edit', $user) }}"
               class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Edit Pengguna
            </a>
            <a href="{{ route('admin.users.index') }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Kembali
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- User Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Informasi Dasar</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Nama Lengkap</label>
                        <p class="text-gray-900 dark:text-gray-100 font-medium">{{ $user->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $user->email }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Role</label>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @if($user->role === 'admin') bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                            @elseif($user->role === 'staff') bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
                            @elseif($user->role === 'penjual') bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400
                            @else bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 @endif">
                            {{ ucfirst($user->role) }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                        <div class="flex flex-col space-y-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full w-fit
                                @if($user->is_active) bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                @else bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 @endif">
                                {{ $user->is_active ? 'Aktif' : 'Tidak Aktif' }}
                            </span>
                            @if($user->email_verified_at)
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 w-fit">
                                    Email Terverifikasi
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 w-fit">
                                    Email Belum Verifikasi
                                </span>
                            @endif
                        </div>
                    </div>
                    
                    @if($user->phone)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Telepon</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $user->phone }}</p>
                    </div>
                    @endif
                    
                    @if($user->date_of_birth)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Tanggal Lahir</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $user->date_of_birth->format('d M Y') }}</p>
                    </div>
                    @endif
                    
                    @if($user->gender)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Jenis Kelamin</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ ucfirst($user->gender) }}</p>
                    </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Bergabung</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $user->created_at->format('d M Y, H:i') }}</p>
                    </div>
                </div>
                
                @if($user->address)
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Alamat</label>
                    <p class="text-gray-900 dark:text-gray-100 mt-1">{{ $user->address }}</p>
                </div>
                @endif
            </div>

            <!-- Activity Information -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Aktivitas</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Total Event Diorganisir</label>
                        <p class="text-2xl font-bold text-primary">{{ $statistics['total_tickets'] }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Total Pesanan</label>
                        <p class="text-2xl font-bold text-blue-600">{{ $statistics['total_orders'] }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Total Tiket Dibeli</label>
                        <p class="text-2xl font-bold text-green-600">{{ $statistics['total_tickets'] }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Total Pengeluaran</label>
                        <p class="text-2xl font-bold text-accent">Rp {{ number_format($statistics['total_spent'], 0, ',', '.') }}</p>
                    </div>
                </div>

                @if($user->role === 'penjual')
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-dark-600">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Statistik Organizer</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Revenue dari Event</label>
                            <p class="text-2xl font-bold text-green-600">Rp {{ number_format($statistics['tickets_revenue'], 0, ',', '.') }}</p>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Profile Photo -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Foto Profil</h3>
                <div class="flex justify-center">
                    <img src="{{ $user->profile_photo_url }}" 
                         alt="{{ $user->name }}"
                         class="w-32 h-32 rounded-full object-cover ring-4 ring-primary/20">
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Aksi Cepat</h3>
                
                <div class="space-y-3">
                    @if($user->id !== auth()->id())
                        <form action="{{ route('admin.users.toggle-status', $user) }}" method="POST">
                            @csrf
                            <button type="submit" 
                                    class="w-full px-4 py-2 text-sm font-medium rounded-lg transition-colors
                                    @if($user->is_active) 
                                        bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400
                                    @else 
                                        bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400
                                    @endif">
                                @if($user->is_active)
                                    Nonaktifkan Pengguna
                                @else
                                    Aktifkan Pengguna
                                @endif
                            </button>
                        </form>
                    @endif
                    
                    @if(!$user->email_verified_at)
                        <form action="{{ route('admin.users.verify-email', $user) }}" method="POST">
                            @csrf
                            <button type="submit" 
                                    class="w-full px-4 py-2 text-sm font-medium bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors dark:bg-blue-900/20 dark:text-blue-400">
                                Verifikasi Email
                            </button>
                        </form>
                    @endif
                    
                    <a href="{{ route('admin.users.edit', $user) }}"
                       class="block w-full px-4 py-2 text-sm font-medium text-center bg-primary/10 text-primary rounded-lg hover:bg-primary/20 transition-colors">
                        Edit Pengguna
                    </a>
                    
                    @if($user->id !== auth()->id())
                        <form action="{{ route('admin.users.destroy', $user) }}" method="POST" 
                              onsubmit="return confirm('Apakah Anda yakin ingin menghapus pengguna ini?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full px-4 py-2 text-sm font-medium bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors dark:bg-red-900/20 dark:text-red-400">
                                Hapus Pengguna
                            </button>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Account Information -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="400">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Informasi Akun</h3>
                
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">User ID</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">#{{ $user->id }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Terakhir Login</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            {{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Belum pernah' }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Email Verified</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">
                            {{ $user->email_verified_at ? $user->email_verified_at->format('d M Y') : 'Belum' }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Dibuat</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $user->created_at->format('d M Y') }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Diperbarui</span>
                        <span class="font-medium text-gray-900 dark:text-gray-100">{{ $user->updated_at->format('d M Y') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
