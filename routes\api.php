<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\Event;
use App\Models\Notification;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Health check endpoint
Route::get('/health-check', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'service' => 'TiXara API',
        'version' => '1.0.0'
    ]);
});

// API Status endpoint with detailed information
Route::get('/status', function () {
    try {
        // Test database connection
        $dbStatus = 'ok';
        $dbMessage = 'Connected';
        try {
            DB::connection()->getPdo();
            $eventCount = DB::table('events')->count();
            $userCount = DB::table('users')->count();
        } catch (\Exception $e) {
            $dbStatus = 'error';
            $dbMessage = 'Connection failed';
            $eventCount = 0;
            $userCount = 0;
        }

        // Test cache (if available)
        $cacheStatus = 'ok';
        try {
            Cache::put('api_test', 'working', 60);
            $cacheTest = Cache::get('api_test');
            if ($cacheTest !== 'working') {
                $cacheStatus = 'error';
            }
        } catch (\Exception $e) {
            $cacheStatus = 'error';
        }

        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'service' => 'TiXara API',
            'version' => '1.0.0',
            'environment' => app()->environment(),
            'database' => [
                'status' => $dbStatus,
                'message' => $dbMessage,
                'events_count' => $eventCount,
                'users_count' => $userCount
            ],
            'cache' => [
                'status' => $cacheStatus
            ],
            'endpoints' => [
                'search' => '/api/search',
                'notifications' => '/api/notifications/latest',
                'health' => '/api/health-check'
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'timestamp' => now()->toISOString(),
            'service' => 'TiXara API',
            'version' => '1.0.0',
            'error' => 'System error',
            'message' => 'API temporarily unavailable'
        ], 500);
    }
});

// Search API
Route::get('/search', function (Request $request) {
    $query = $request->get('q', '');

    if (strlen($query) < 2) {
        return response()->json([
            'results' => [],
            'query' => $query,
            'message' => 'Query too short'
        ]);
    }

    try {
        // Search events by title, description, and venue
        $events = Event::where('status', 'published')
            ->where(function ($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('venue_name', 'LIKE', "%{$query}%")
                  ->orWhere('city', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'title', 'description', 'venue_name', 'city', 'slug', 'poster']);

        $results = $events->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'description' => \Str::limit(strip_tags($event->description), 100),
                'venue' => $event->venue_name,
                'city' => $event->city,
                'poster' => $event->poster_url ?? null,
                'url' => route('tickets.show', $event->slug)
            ];
        });

        return response()->json([
            'results' => $results,
            'query' => $query,
            'count' => $results->count(),
            'message' => $results->count() > 0 ? 'Search successful' : 'No events found'
        ]);
    } catch (\Exception $e) {
        \Log::error('Search API Error: ' . $e->getMessage(), [
            'query' => $query,
            'trace' => $e->getTraceAsString()
        ]);

        return response()->json([
            'results' => [],
            'query' => $query,
            'error' => 'Search temporarily unavailable',
            'message' => 'Please try again later'
        ], 500);
    }
});

// Notifications API (requires authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Get latest notifications
    Route::get('/notifications/latest', function (Request $request) {
        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'error' => 'User not authenticated',
                    'notifications' => [],
                    'unread_count' => 0
                ], 401);
            }

            $lastCheck = $request->get('last_check');

            $query = $user->notifications()->latest();

            if ($lastCheck) {
                $query->where('created_at', '>', $lastCheck);
            }

            $notifications = $query->limit(20)->get();
            $unreadCount = $user->unreadNotifications()->count();

            return response()->json([
                'notifications' => $notifications->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->data['title'] ?? 'Notifikasi',
                        'message' => $notification->data['message'] ?? '',
                        'type' => $notification->data['type'] ?? 'info',
                        'created_at' => $notification->created_at->toISOString(),
                        'read_at' => $notification->read_at?->toISOString(),
                        'action_url' => $notification->data['action_url'] ?? null
                    ];
                }),
                'unread_count' => $unreadCount,
                'has_new' => $lastCheck ? $notifications->count() > 0 : false,
                'message' => $notifications->count() > 0 ? 'Notifications loaded' : 'No notifications found'
            ]);
        } catch (\Exception $e) {
            \Log::error('Notifications API Error: ' . $e->getMessage(), [
                'user_id' => $request->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to load notifications',
                'notifications' => [],
                'unread_count' => 0,
                'message' => 'Please try again later'
            ], 500);
        }
    });

    // Mark notification as read
    Route::post('/notifications/{id}/read', function (Request $request, $id) {
        try {
            $user = $request->user();
            $notification = $user->notifications()->find($id);

            if ($notification) {
                $notification->markAsRead();
                return response()->json(['success' => true]);
            }

            return response()->json(['error' => 'Notification not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark notification as read'], 500);
        }
    });

    // Mark all notifications as read
    Route::post('/notifications/mark-all-read', function (Request $request) {
        try {
            $user = $request->user();
            $user->unreadNotifications()->update(['read_at' => now()]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark all notifications as read'], 500);
        }
    });
});
