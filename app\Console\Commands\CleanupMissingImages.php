<?php

namespace App\Console\Commands;

use App\Models\Event;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupMissingImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:cleanup {--dry-run : Show what would be cleaned without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up missing images from events and users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('🔍 Running in dry-run mode. No changes will be made.');
        }

        $this->info('🧹 Starting image cleanup...');

        // Clean up event posters
        $this->cleanupEventPosters($dryRun);

        // Clean up event galleries
        $this->cleanupEventGalleries($dryRun);

        // Clean up user avatars
        $this->cleanupUserAvatars($dryRun);

        $this->info('✅ Image cleanup completed!');
    }

    /**
     * Clean up event posters
     */
    private function cleanupEventPosters(bool $dryRun): void
    {
        $this->info('📸 Checking event posters...');

        $events = Event::whereNotNull('poster')->get();
        $cleaned = 0;

        foreach ($events as $event) {
            if (!Storage::disk('public')->exists($event->poster)) {
                $this->warn("Missing poster: {$event->poster} for event: {$event->title}");

                if (!$dryRun) {
                    $event->update(['poster' => null]);
                    $cleaned++;
                }
            }
        }

        if ($dryRun) {
            $this->info("Would clean {$cleaned} missing posters");
        } else {
            $this->info("Cleaned {$cleaned} missing posters");
        }
    }

    /**
     * Clean up event galleries
     */
    private function cleanupEventGalleries(bool $dryRun): void
    {
        $this->info('🖼️ Checking event galleries...');

        $events = Event::whereNotNull('gallery')->get();
        $totalCleaned = 0;

        foreach ($events as $event) {
            if (!$event->gallery) {
                continue;
            }

            $originalCount = count($event->gallery);
            $existingImages = collect($event->gallery)->filter(function ($image) {
                return Storage::disk('public')->exists($image);
            })->values()->toArray();

            $missingCount = $originalCount - count($existingImages);

            if ($missingCount > 0) {
                $this->warn("Event '{$event->title}' has {$missingCount} missing gallery images");

                if (!$dryRun) {
                    $event->update(['gallery' => $existingImages]);
                    $totalCleaned += $missingCount;
                }
            }
        }

        if ($dryRun) {
            $this->info("Would clean {$totalCleaned} missing gallery images");
        } else {
            $this->info("Cleaned {$totalCleaned} missing gallery images");
        }
    }

    /**
     * Clean up user avatars
     */
    private function cleanupUserAvatars(bool $dryRun): void
    {
        $this->info('👤 Checking user avatars...');

        $users = User::whereNotNull('avatar')->get();
        $cleaned = 0;

        foreach ($users as $user) {
            if (!Storage::disk('public')->exists($user->avatar)) {
                $this->warn("Missing avatar: {$user->avatar} for user: {$user->name}");

                if (!$dryRun) {
                    $user->update(['avatar' => null]);
                    $cleaned++;
                }
            }
        }

        if ($dryRun) {
            $this->info("Would clean {$cleaned} missing avatars");
        } else {
            $this->info("Cleaned {$cleaned} missing avatars");
        }
    }
}
