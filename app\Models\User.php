<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    use \App\Traits\HasUserLevel;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'role',
        'avatar',
        'birth_date',
        'gender',
        'address',
        'user_level',
        'level_upgraded_at',
        'level_expires_at',
        'level_benefits',
        'level_notes',
        'total_events_created',
        'total_tickets_sold',
        'total_revenue',
        'level_score',
        'organization',
        'bio',
        'social_media',
        'is_active',
        'last_login_at',
        'otp_code',
        'otp_expires_at',
        'email_notifications',
        'push_notifications',
        'sms_notifications',
        'settings',
        'company_name',
        'company_address',
        'website',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'otp_code',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'birth_date' => 'date',
        'last_login_at' => 'datetime',
        'otp_expires_at' => 'datetime',
        'level_upgraded_at' => 'datetime',
        'level_expires_at' => 'datetime',
        'level_benefits' => 'array',
        'total_events_created' => 'integer',
        'total_tickets_sold' => 'integer',
        'total_revenue' => 'decimal:2',
        'level_score' => 'decimal:2',
        'social_media' => 'array',
        'is_active' => 'boolean',
        'email_notifications' => 'boolean',
        'push_notifications' => 'boolean',
        'sms_notifications' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Role constants
     */
    const ROLE_ADMIN = 'admin';
    const ROLE_STAFF = 'staff';
    const ROLE_PENJUAL = 'penjual';
    const ROLE_PEMBELI = 'pembeli';

    /**
     * Get all available roles
     */
    public static function getRoles(): array
    {
        return [
            self::ROLE_ADMIN => 'Administrator',
            self::ROLE_STAFF => 'Staff',
            self::ROLE_PENJUAL => 'Penjual Event',
            self::ROLE_PEMBELI => 'Pembeli',
        ];
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole(self::ROLE_ADMIN);
    }

    /**
     * Check if user is staff
     */
    public function isStaff(): bool
    {
        return $this->hasRole(self::ROLE_STAFF);
    }

    /**
     * Check if user is penjual
     */
    public function isPenjual(): bool
    {
        return $this->hasRole(self::ROLE_PENJUAL);
    }

    /**
     * Check if user is pembeli
     */
    public function isPembeli(): bool
    {
        return $this->hasRole(self::ROLE_PEMBELI);
    }

    /**
     * Get user's avatar URL
     */
    public function getAvatarUrlAttribute(): string
    {
        // Check if user has uploaded avatar
        if ($this->avatar) {
            // Try Storage facade first
            if (Storage::exists('public/' . $this->avatar)) {
                return Storage::url($this->avatar);
            }
            // Fallback to file_exists check
            if (file_exists(public_path('storage/' . $this->avatar))) {
                return asset('storage/' . $this->avatar);
            }
        }

        // Generate avatar using UI Avatars service
        $name = urlencode($this->name);
        $background = 'A8D5BA'; // Primary color (green pasta theme)
        $color = 'FFFFFF';

        return "https://ui-avatars.com/api/?name={$name}&background={$background}&color={$color}&size=200&font-size=0.6&rounded=true&bold=true";
    }

    /**
     * Get profile photo URL (alias for avatar)
     */
    public function getProfilePhotoUrlAttribute(): string
    {
        return $this->avatar_url;
    }

    /**
     * Events organized by this user (for penjual role)
     */
    public function organizedEvents(): HasMany
    {
        return $this->hasMany(Event::class, 'organizer_id');
    }

    /**
     * Events organized by this user (alias for organizedEvents)
     */
    public function events(): HasMany
    {
        return $this->hasMany(Event::class, 'organizer_id');
    }

    /**
     * Get the user's ads
     */
    public function ads(): HasMany
    {
        return $this->hasMany(Ad::class, 'advertiser_id');
    }

    /**
     * Get the user's ad subscriptions
     */
    public function adSubscriptions(): HasMany
    {
        return $this->hasMany(AdSubscription::class);
    }

    /**
     * Get the user's active ad subscription
     */
    public function activeAdSubscription(): ?AdSubscription
    {
        return $this->adSubscriptions()
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * Orders made by this user
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Tickets purchased by this user (as buyer)
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class, 'buyer_id');
    }

    /**
     * Tickets validated by this user (for staff)
     */
    public function validatedTickets(): HasMany
    {
        return $this->hasMany(Ticket::class, 'validated_by');
    }

    /**
     * User's wishlist events (many-to-many)
     */
    public function wishlist(): BelongsToMany
    {
        return $this->belongsToMany(Event::class, 'user_wishlist', 'user_id', 'event_id')
            ->withTimestamps();
    }

    /**
     * Get user's notifications
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get user's unread notifications
     */
    public function unreadNotifications(): HasMany
    {
        return $this->hasMany(Notification::class)->whereNull('read_at');
    }

    /**
     * Get user's voucher usages
     */
    public function voucherUsages(): HasMany
    {
        return $this->hasMany(VoucherUsage::class);
    }

    /**
     * Get user's balance
     */
    public function userBalance(): HasOne
    {
        return $this->hasOne(UserBalance::class);
    }

    /**
     * Get user's balance transactions
     */
    public function balanceTransactions(): HasMany
    {
        return $this->hasMany(BalanceTransaction::class);
    }

    /**
     * Get user's subscriptions
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get user's active subscription
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(UserSubscription::class)
            ->where('status', UserSubscription::STATUS_ACTIVE)
            ->where('end_date', '>=', now()->toDateString());
    }

    /**
     * Get or create user balance
     */
    public function getBalance(): UserBalance
    {
        return $this->userBalance ?: $this->userBalance()->create([
            'balance' => 0,
            'pending_balance' => 0,
            'total_earned' => 0,
            'total_deposited' => 0,
            'total_withdrawn' => 0,
            'total_fees_paid' => 0,
        ]);
    }

    /**
     * Check if user has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * Check if user is exempt from transaction fees
     */
    public function isExemptFromFees(): bool
    {
        return $this->hasActiveSubscription();
    }

    /**
     * Get current balance amount
     */
    public function getBalanceAmount(): float
    {
        return $this->getBalance()->balance;
    }

    /**
     * Get available balance (excluding pending withdrawals)
     */
    public function getAvailableBalance(): float
    {
        return $this->getBalance()->available_balance;
    }

    /**
     * Add earnings to balance
     */
    public function addEarnings(float $amount, array $metadata = []): BalanceTransaction
    {
        return $this->getBalance()->addBalance($amount, 'earning', $metadata);
    }

    /**
     * Deduct platform fee
     */
    public function deductFee(float $amount, array $metadata = []): BalanceTransaction
    {
        return $this->getBalance()->deductBalance($amount, 'fee', $metadata);
    }

    /**
     * Get user's UangTix balance
     */
    public function uangTixBalance(): HasOne
    {
        return $this->hasOne(UangTixBalance::class);
    }

    /**
     * Get user's UangTix transactions
     */
    public function uangTixTransactions(): HasMany
    {
        return $this->hasMany(UangTixTransaction::class);
    }

    /**
     * Get user's UangTix requests
     */
    public function uangTixRequests(): HasMany
    {
        return $this->hasMany(UangTixRequest::class);
    }

    /**
     * Get or create UangTix balance
     */
    public function getUangTixBalance(): UangTixBalance
    {
        return $this->uangTixBalance ?: $this->uangTixBalance()->create([
            'balance' => 0,
            'total_earned' => 0,
            'total_spent' => 0,
            'total_deposited' => 0,
            'total_withdrawn' => 0,
            'is_active' => true,
        ]);
    }

    /**
     * Get UangTix balance amount
     */
    public function getUangTixBalanceAmount(): float
    {
        return $this->getUangTixBalance()->balance;
    }

    /**
     * Check if user can spend UangTix
     */
    public function canSpendUangTix(float $amount): bool
    {
        return $this->getUangTixBalance()->canSpend($amount);
    }

    /**
     * Add UangTix to balance
     */
    public function addUangTix(float $amount, string $type = 'admin_add', array $metadata = []): UangTixTransaction
    {
        return $this->getUangTixBalance()->addBalance($amount, $type, $metadata);
    }

    /**
     * Deduct UangTix from balance
     */
    public function deductUangTix(float $amount, string $type = 'purchase', array $metadata = []): UangTixTransaction
    {
        return $this->getUangTixBalance()->deductBalance($amount, $type, $metadata);
    }

    /**
     * Transfer UangTix to another user
     */
    public function transferUangTix(User $toUser, float $amount, string $description = 'Transfer UangTix'): array
    {
        return $this->getUangTixBalance()->transferTo($toUser, $amount, $description);
    }

    /**
     * Generate OTP for user
     */
    public function generateOtp(): string
    {
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        $this->update([
            'otp_code' => $otp,
            'otp_expires_at' => now()->addMinutes(10),
        ]);

        return $otp;
    }

    /**
     * Verify OTP
     */
    public function verifyOtp(string $otp): bool
    {
        if ($this->otp_code === $otp && $this->otp_expires_at && $this->otp_expires_at->isFuture()) {
            $this->update([
                'otp_code' => null,
                'otp_expires_at' => null,
                'email_verified_at' => now(),
            ]);

            return true;
        }

        return false;
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific role
     */
    public function scopeRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Get formatted organization name
     */
    public function getFormattedOrganizationAttribute(): string
    {
        return $this->organization ?: $this->name;
    }

    /**
     * Get social media link by platform
     */
    public function getSocialMediaLink(string $platform): ?string
    {
        return $this->social_media[$platform] ?? null;
    }

    /**
     * Set social media link for platform
     */
    public function setSocialMediaLink(string $platform, ?string $url): void
    {
        $socialMedia = $this->social_media ?: [];

        if ($url) {
            $socialMedia[$platform] = $url;
        } else {
            unset($socialMedia[$platform]);
        }

        $this->social_media = $socialMedia;
    }

    /**
     * Get all available social media platforms
     */
    public static function getSocialMediaPlatforms(): array
    {
        return [
            'instagram' => [
                'name' => 'Instagram',
                'placeholder' => 'https://instagram.com/username',
                'icon' => 'instagram',
            ],
            'facebook' => [
                'name' => 'Facebook',
                'placeholder' => 'https://facebook.com/username',
                'icon' => 'facebook',
            ],
            'twitter' => [
                'name' => 'Twitter/X',
                'placeholder' => 'https://twitter.com/username',
                'icon' => 'twitter',
            ],
            'linkedin' => [
                'name' => 'LinkedIn',
                'placeholder' => 'https://linkedin.com/in/username',
                'icon' => 'linkedin',
            ],
            'youtube' => [
                'name' => 'YouTube',
                'placeholder' => 'https://youtube.com/@username',
                'icon' => 'youtube',
            ],
            'tiktok' => [
                'name' => 'TikTok',
                'placeholder' => 'https://tiktok.com/@username',
                'icon' => 'tiktok',
            ],
            'website' => [
                'name' => 'Website',
                'placeholder' => 'https://yourwebsite.com',
                'icon' => 'website',
            ],
        ];
    }

    /**
     * Check if user has any social media links
     */
    public function hasSocialMedia(): bool
    {
        return !empty($this->social_media) && count(array_filter($this->social_media)) > 0;
    }

    /**
     * Get active social media links (non-empty)
     */
    public function getActiveSocialMediaAttribute(): array
    {
        if (!$this->social_media) {
            return [];
        }

        return array_filter($this->social_media, function ($url) {
            return !empty($url);
        });
    }
}
