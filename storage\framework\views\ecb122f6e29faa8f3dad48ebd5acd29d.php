<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">

    <!-- Header Section -->
    <section class="pt-8 pb-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    Jelajahi Event Menarik
                </h1>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Temukan berbagai event seru dari konser, seminar, hingga festival di seluruh Indonesia
                </p>
            </div>
        </div>
    </section>

    <!-- Search & Filter Section -->
    <section class="pb-8" x-data="eventFilter()">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Search Bar -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-6" data-aos="fade-up" data-aos-delay="200">
                <form method="GET" action="<?php echo e(route('tickets.index')); ?>" class="space-y-4">
                    <!-- Main Search -->
                    <div class="relative">
                        <input type="text"
                               name="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="Cari Tiket, artis, venue, atau kota..."
                               class="w-full px-6 py-4 pl-14 pr-32 text-lg rounded-xl border-2 border-gray-200 focus:border-primary focus:outline-none transition-all duration-300">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-tickets-none">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <button type="submit" class="absolute right-2 top-2 bottom-2 px-6 bg-gradient-to-r from-primary to-secondary text-white rounded-xl hover:shadow-lg transition-all duration-300 font-semibold">
                            Cari
                        </button>
                    </div>

                    <!-- Advanced Filters -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Category Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                            <select name="category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                                <option value="">Semua Kategori</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                        <?php echo e($category->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- City Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Kota</label>
                            <select name="city" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                                <option value="">Semua Kota</option>
                                <?php $__currentLoopData = $popularCities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($city); ?>" <?php echo e(request('city') == $city ? 'selected' : ''); ?>>
                                        <?php echo e($city); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Date Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal</label>
                            <input type="date"
                                   name="date_from"
                                   value="<?php echo e(request('date_from')); ?>"
                                   min="<?php echo e(date('Y-m-d')); ?>"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                        </div>

                        <!-- Price Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Harga</label>
                            <select name="price_range" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                                <option value="">Semua Harga</option>
                                <option value="free" <?php echo e(request('price_range') == 'free' ? 'selected' : ''); ?>>Gratis</option>
                                <option value="under_100k" <?php echo e(request('price_range') == 'under_100k' ? 'selected' : ''); ?>>Di bawah Rp 100K</option>
                                <option value="100k_500k" <?php echo e(request('price_range') == '100k_500k' ? 'selected' : ''); ?>>Rp 100K - 500K</option>
                                <option value="above_500k" <?php echo e(request('price_range') == 'above_500k' ? 'selected' : ''); ?>>Di atas Rp 500K</option>
                            </select>
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-between items-center pt-4 border-t border-gray-200">
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" name="free_only" value="1" <?php echo e(request('free_only') ? 'checked' : ''); ?> class="rounded border-gray-300 text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">Hanya event gratis</span>
                            </label>
                        </div>
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('tickets.index')); ?>" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200">
                                Reset Filter
                            </a>
                            <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">
                                Terapkan Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Sort & View Options -->
            <div class="flex flex-col sm:flex-row justify-between items-center mb-6" data-aos="fade-up" data-aos-delay="300">
                <div class="flex items-center space-x-4 mb-4 sm:mb-0">
                    <span class="text-gray-600">Urutkan:</span>
                    <select name="sort" onchange="updateSort(this.value)" class="px-3 py-1 border border-gray-300 rounded-lg focus:border-primary focus:outline-none">
                        <option value="start_date" <?php echo e(request('sort') == 'start_date' ? 'selected' : ''); ?>>Tanggal Terdekat</option>
                        <option value="price" <?php echo e(request('sort') == 'price' ? 'selected' : ''); ?>>Harga Terendah</option>
                        <option value="popularity" <?php echo e(request('sort') == 'popularity' ? 'selected' : ''); ?>>Paling Populer</option>
                        <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Terbaru</option>
                    </select>
                </div>
                <div class="text-gray-600">
                    Menampilkan <?php echo e($tickets->firstItem() ?? 0); ?> - <?php echo e($tickets->lastItem() ?? 0); ?> dari <?php echo e($tickets->total()); ?> event
                </div>
            </div>
        </div>
    </section>

    <!--  Tickets Grid -->
    <section class="pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <?php if($tickets->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $tickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card-hover bg-white rounded-2xl overflow-hidden shadow-lg" data-aos="fade-up" data-aos-delay="<?php echo e($loop->index * 100); ?>">
                            <div class="relative">
                                <img src="<?php echo e($event->poster_url); ?>"
                                     alt="<?php echo e($event->title); ?>"
                                     class="w-full h-48 object-cover">

                                <!-- Event Status Badge -->
                                <?php if($event->availability_status == 'limited'): ?>
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                            Terbatas
                                        </span>
                                    </div>
                                <?php elseif($event->availability_status == 'sold_out'): ?>
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                            Sold Out
                                        </span>
                                    </div>
                                <?php elseif($event->is_featured): ?>
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                            Featured
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <!-- Wishlist Button -->
                                <div class="absolute top-4 right-4">
                                    <button onclick="toggleWishlist(<?php echo e($event->id); ?>)"
                                            class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors duration-200 wishlist-btn"
                                            data-event-id="<?php echo e($event->id); ?>">
                                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="p-6">
                                <!-- Date & Category -->
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                        <?php echo e($event->start_date->format('d M Y')); ?>

                                    </div>
                                    <span class="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs">
                                        <?php echo e($event->category->name); ?>

                                    </span>
                                </div>

                                <!-- Title -->
                                <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                                    <?php echo e($event->title); ?>

                                </h3>

                                <!-- Location -->
                                <div class="flex items-center text-sm text-gray-500 mb-4">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    <?php echo e($event->venue_name); ?>, <?php echo e($event->city); ?>

                                </div>

                                <!-- Price & Action -->
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-xl font-bold text-primary"><?php echo e($event->formatted_price); ?></span>
                                        <?php if($event->discount_percentage): ?>
                                            <span class="text-sm text-gray-500 line-through ml-2">
                                                Rp <?php echo e(number_format($event->original_price, 0, ',', '.')); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <a href="<?php echo e(route('tickets.show', $event)); ?>"
                                       class="bg-gradient-to-r from-primary to-secondary text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 font-semibold text-sm">
                                        Detail
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-12" data-aos="fade-up">
                    <?php echo e($tickets->links('pagination.custom')); ?>

                </div>
            <?php else: ?>
                <!-- No Tickets Found -->
                <div class="text-center py-16" data-aos="fade-up">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Tidak ada event ditemukan</h3>
                    <p class="text-gray-600 mb-6">Coba ubah filter pencarian atau kata kunci Anda</p>
                    <a href="<?php echo e(route('tickets.index')); ?>"
                       class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        Reset Pencarian
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function eventFilter() {
    return {
        // Filter state management
    }
}

function updateSort(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    window.location.href = url.toString();
}

async function toggleWishlist(eventId) {
    if (!<?php echo json_encode(auth()->check(), 15, 512) ?>) {
        window.showNotification('Silakan login terlebih dahulu', 'warning');
        return;
    }

    const button = document.querySelector(`[data-event-id="${eventId}"]`);
    const icon = button.querySelector('svg');

    try {
        const response = await fetch(`/tickets/${eventId}/wishlist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (response.ok) {
            icon.classList.toggle('text-red-500');
            icon.classList.toggle('fill-current');
            window.showNotification(data.message, 'success');
        } else {
            window.showNotification(data.error || 'Terjadi kesalahan', 'error');
        }
    } catch (error) {
        window.showNotification('Terjadi kesalahan jaringan', 'error');
    }
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const filterInputs = filterForm.querySelectorAll('select, input[type="checkbox"]');

    filterInputs.forEach(input => {
        if (input.name !== 'search') {
            input.addEventListener('change', function() {
                filterForm.submit();
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/tickets/index.blade.php ENDPATH**/ ?>