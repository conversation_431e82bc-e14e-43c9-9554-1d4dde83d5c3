<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class GuideController extends Controller
{
    /**
     * Show event creator guide
     */
    public function eventCreator()
    {
        return view('pages.guide.event-creator');
    }

    /**
     * Show organizer guide
     */
    public function organizer()
    {
        $guides = [
            [
                'title' => 'Memulai sebagai Organizer',
                'description' => 'Panduan lengkap untuk memulai karir sebagai event organizer di TiXara',
                'icon' => 'user-plus',
                'duration' => '10 menit',
                'level' => 'Pemula',
                'topics' => [
                    'Registrasi akun organizer',
                    'Verifikasi identitas',
                    'Setup profil organizer',
                    'Memahami dashboard organizer'
                ]
            ],
            [
                'title' => 'Membuat Event Pertama',
                'description' => 'Step-by-step guide untuk membuat event pertama Anda',
                'icon' => 'calendar-plus',
                'duration' => '15 menit',
                'level' => 'Pemula',
                'topics' => [
                    'Mengisi detail event',
                    'Upload gambar dan media',
                    'Setting harga tiket',
                    'Konfigurasi pembayaran'
                ]
            ],
            [
                'title' => 'Strategi Marketing Event',
                'description' => 'Tips dan trik untuk mempromosikan event Anda secara efektif',
                'icon' => 'megaphone',
                'duration' => '20 menit',
                'level' => 'Menengah',
                'topics' => [
                    'Menggunakan ArtPosure',
                    'Social media marketing',
                    'Email marketing',
                    'Influencer collaboration'
                ]
            ],
            [
                'title' => 'Mengelola Peserta Event',
                'description' => 'Cara mengelola peserta, check-in, dan customer service',
                'icon' => 'users',
                'duration' => '12 menit',
                'level' => 'Menengah',
                'topics' => [
                    'Dashboard peserta',
                    'Check-in digital',
                    'Komunikasi dengan peserta',
                    'Handling refund'
                ]
            ],
            [
                'title' => 'Analytics dan Reporting',
                'description' => 'Memahami data analytics untuk optimasi event selanjutnya',
                'icon' => 'bar-chart-3',
                'duration' => '18 menit',
                'level' => 'Lanjutan',
                'topics' => [
                    'Reading analytics dashboard',
                    'Understanding metrics',
                    'ROI calculation',
                    'Performance optimization'
                ]
            ],
            [
                'title' => 'Monetisasi dan Scaling',
                'description' => 'Strategi untuk mengembangkan bisnis event organizer Anda',
                'icon' => 'trending-up',
                'duration' => '25 menit',
                'level' => 'Lanjutan',
                'topics' => [
                    'Multiple revenue streams',
                    'Building brand reputation',
                    'Scaling operations',
                    'Partnership opportunities'
                ]
            ]
        ];

        return view('pages.guide.organizer', compact('guides'));
    }

    /**
     * Show buyer guide
     */
    public function buyer()
    {
        $guides = [
            [
                'title' => 'Cara Membeli Tiket',
                'description' => 'Panduan lengkap untuk membeli tiket event di TiXara',
                'icon' => 'ticket',
                'duration' => '5 menit',
                'level' => 'Pemula',
                'steps' => [
                    'Browse event yang tersedia',
                    'Pilih event yang diinginkan',
                    'Pilih jenis tiket',
                    'Isi data pembeli',
                    'Pilih metode pembayaran',
                    'Konfirmasi pembelian'
                ]
            ],
            [
                'title' => 'Metode Pembayaran',
                'description' => 'Berbagai cara pembayaran yang tersedia di TiXara',
                'icon' => 'credit-card',
                'duration' => '3 menit',
                'level' => 'Pemula',
                'methods' => [
                    'Transfer Bank',
                    'E-Wallet (GoPay, OVO, DANA)',
                    'Virtual Account',
                    'Credit/Debit Card',
                    'QRIS'
                ]
            ],
            [
                'title' => 'Menggunakan E-Ticket',
                'description' => 'Cara menggunakan e-ticket untuk masuk event',
                'icon' => 'smartphone',
                'duration' => '4 menit',
                'level' => 'Pemula',
                'features' => [
                    'Download e-ticket',
                    'QR Code scanning',
                    'Offline access',
                    'Transfer tiket',
                    'Refund policy'
                ]
            ]
        ];

        return view('pages.guide.buyer', compact('guides'));
    }

    /**
     * Show FAQ page
     */
    public function faq()
    {
        $faqs = [
            [
                'category' => 'Umum',
                'questions' => [
                    [
                        'question' => 'Apa itu TiXara?',
                        'answer' => 'TiXara adalah platform digital untuk membeli dan menjual tiket event. Kami menghubungkan event organizer dengan calon peserta event di seluruh Indonesia.'
                    ],
                    [
                        'question' => 'Bagaimana cara mendaftar di TiXara?',
                        'answer' => 'Anda bisa mendaftar dengan mengklik tombol "Daftar" di halaman utama, lalu pilih sebagai Pembeli atau Organizer sesuai kebutuhan Anda.'
                    ],
                    [
                        'question' => 'Apakah TiXara gratis digunakan?',
                        'answer' => 'Untuk pembeli, TiXara gratis digunakan. Untuk organizer, kami mengenakan fee kecil dari setiap transaksi yang berhasil.'
                    ]
                ]
            ],
            [
                'category' => 'Pembeli',
                'questions' => [
                    [
                        'question' => 'Bagaimana cara membeli tiket?',
                        'answer' => 'Pilih event yang diinginkan, klik "Beli Tiket", pilih jenis tiket, isi data, pilih metode pembayaran, dan konfirmasi pembelian.'
                    ],
                    [
                        'question' => 'Metode pembayaran apa saja yang tersedia?',
                        'answer' => 'Kami menerima transfer bank, e-wallet (GoPay, OVO, DANA), virtual account, kartu kredit/debit, dan QRIS.'
                    ],
                    [
                        'question' => 'Bisakah saya refund tiket?',
                        'answer' => 'Kebijakan refund tergantung pada organizer event. Silakan cek detail kebijakan refund di halaman event.'
                    ]
                ]
            ],
            [
                'category' => 'Organizer',
                'questions' => [
                    [
                        'question' => 'Berapa fee yang dikenakan TiXara?',
                        'answer' => 'Fee TiXara adalah 5% dari harga tiket + biaya payment gateway. Fee ini sudah termasuk semua fitur platform.'
                    ],
                    [
                        'question' => 'Kapan saya menerima pembayaran?',
                        'answer' => 'Pembayaran akan ditransfer ke rekening Anda 3-7 hari kerja setelah event selesai, sesuai dengan terms & conditions.'
                    ],
                    [
                        'question' => 'Bisakah saya menggunakan domain sendiri?',
                        'answer' => 'Untuk paket Enterprise, Anda bisa menggunakan custom domain. Hubungi tim support untuk informasi lebih lanjut.'
                    ]
                ]
            ]
        ];

        return view('pages.guide.faq', compact('faqs'));
    }

    /**
     * Show help center
     */
    public function helpCenter()
    {
        $categories = [
            [
                'name' => 'Getting Started',
                'icon' => 'play-circle',
                'description' => 'Panduan dasar untuk memulai menggunakan TiXara',
                'articles' => 12,
                'color' => 'blue'
            ],
            [
                'name' => 'Pembeli',
                'icon' => 'shopping-cart',
                'description' => 'Panduan lengkap untuk pembeli tiket',
                'articles' => 8,
                'color' => 'green'
            ],
            [
                'name' => 'Organizer',
                'icon' => 'users',
                'description' => 'Panduan untuk event organizer',
                'articles' => 15,
                'color' => 'purple'
            ],
            [
                'name' => 'Pembayaran',
                'icon' => 'credit-card',
                'description' => 'Informasi tentang metode pembayaran',
                'articles' => 6,
                'color' => 'orange'
            ],
            [
                'name' => 'Teknis',
                'icon' => 'settings',
                'description' => 'Troubleshooting dan masalah teknis',
                'articles' => 10,
                'color' => 'red'
            ],
            [
                'name' => 'Kebijakan',
                'icon' => 'shield',
                'description' => 'Terms, privacy, dan kebijakan platform',
                'articles' => 5,
                'color' => 'gray'
            ]
        ];

        $popularArticles = [
            'Cara membeli tiket event',
            'Panduan menjadi organizer',
            'Metode pembayaran yang tersedia',
            'Cara refund tiket',
            'Menggunakan e-ticket',
            'Fee organizer dan pembayaran',
            'Troubleshooting login',
            'Kebijakan privasi TiXara'
        ];

        return view('pages.guide.help-center', compact('categories', 'popularArticles'));
    }

    /**
     * Search help articles
     */
    public function search(Request $request)
    {
        $query = $request->get('q');
        
        // Mock search results - in real implementation, this would search a database
        $results = [
            [
                'title' => 'Cara membeli tiket event di TiXara',
                'excerpt' => 'Panduan step-by-step untuk membeli tiket event...',
                'category' => 'Pembeli',
                'url' => '#'
            ],
            [
                'title' => 'Metode pembayaran yang tersedia',
                'excerpt' => 'TiXara menerima berbagai metode pembayaran...',
                'category' => 'Pembayaran',
                'url' => '#'
            ]
        ];

        return view('pages.guide.search-results', compact('query', 'results'));
    }
}
