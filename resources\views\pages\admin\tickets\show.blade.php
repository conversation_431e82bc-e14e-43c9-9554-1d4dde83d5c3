@extends('layouts.admin')

@section('title', 'Ticket Details - Admin Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Ticket Details</h1>
                    <p class="mt-2 text-gray-600">View and manage ticket information</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('admin.tickets.edit', $ticket->id ?? 1) }}" 
                       class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                        Edit Ticket
                    </a>
                    <a href="{{ route('admin.tickets.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to Tickets
                    </a>
                </div>
            </div>
        </div>

        <!-- Ticket Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Ticket Details -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Ticket Information</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Ticket Number</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-lg font-mono text-gray-900">{{ $ticket->ticket_number ?? 'TIK-SAMPLE-001' }}</span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    @php
                                        $status = $ticket->status ?? 'active';
                                        $statusColors = [
                                            'active' => 'bg-green-100 text-green-800',
                                            'used' => 'bg-blue-100 text-blue-800',
                                            'expired' => 'bg-yellow-100 text-yellow-800',
                                            'refunded' => 'bg-red-100 text-red-800'
                                        ];
                                        $statusColor = $statusColors[$status] ?? 'bg-gray-100 text-gray-800';
                                    @endphp
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $statusColor }}">
                                        {{ ucfirst($status) }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Price</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-lg font-semibold text-gray-900">
                                        Rp {{ number_format($ticket->price ?? 0, 0, ',', '.') }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-gray-900">{{ ucfirst($ticket->category ?? 'Regular') }}</span>
                                </div>
                            </div>

                            @if(isset($ticket->seat_number))
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Seat Number</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-gray-900">{{ $ticket->seat_number }}</span>
                                </div>
                            </div>
                            @endif

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Created Date</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-gray-900">
                                        {{ isset($ticket->created_at) ? $ticket->created_at->format('M d, Y H:i') : now()->format('M d, Y H:i') }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        @if(isset($ticket->notes) && $ticket->notes)
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <p class="text-gray-900">{{ $ticket->notes }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Event Information -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mt-6">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Event Information</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Event Title</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-gray-900">{{ $ticket->event->title ?? 'Sample Event' }}</span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Event Date</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-gray-900">
                                        {{ isset($ticket->event->date) ? $ticket->event->date->format('M d, Y') : now()->addDays(30)->format('M d, Y') }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-gray-900">{{ $ticket->event->location ?? 'Sample Location' }}</span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Organizer</label>
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <span class="text-gray-900">{{ $ticket->event->organizer->name ?? 'Sample Organizer' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- User Information -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Ticket Holder</h2>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-white">
                                    {{ substr($ticket->user->name ?? 'Sample User', 0, 1) }}
                                </span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $ticket->user->name ?? 'Sample User' }}</h3>
                            <p class="text-gray-600">{{ $ticket->user->email ?? '<EMAIL>' }}</p>
                            <p class="text-sm text-gray-500 mt-2">{{ ucfirst($ticket->user->role ?? 'pembeli') }}</p>
                        </div>

                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Phone</span>
                                    <span class="text-sm text-gray-900">{{ $ticket->user->phone ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Joined</span>
                                    <span class="text-sm text-gray-900">
                                        {{ isset($ticket->user->created_at) ? $ticket->user->created_at->format('M Y') : 'Jan 2024' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mt-6">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">QR Code</h2>
                        
                        <div class="text-center">
                            <div class="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <div class="text-center">
                                    <i data-lucide="qr-code" class="w-16 h-16 text-gray-400 mx-auto mb-2"></i>
                                    <p class="text-sm text-gray-500">QR Code</p>
                                    <p class="text-xs text-gray-400">{{ $ticket->ticket_number ?? 'TIK-SAMPLE-001' }}</p>
                                </div>
                            </div>
                            
                            <button class="w-full px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors duration-200">
                                <i data-lucide="download" class="w-4 h-4 mr-2 inline"></i>
                                Download QR Code
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mt-6">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Actions</h2>
                        
                        <div class="space-y-3">
                            @if(($ticket->status ?? 'active') === 'active')
                            <button class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                                <i data-lucide="check" class="w-4 h-4 mr-2 inline"></i>
                                Mark as Used
                            </button>
                            @endif
                            
                            @if(in_array($ticket->status ?? 'active', ['active', 'used']))
                            <button class="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200">
                                <i data-lucide="x-circle" class="w-4 h-4 mr-2 inline"></i>
                                Refund Ticket
                            </button>
                            @endif
                            
                            <button class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200">
                                <i data-lucide="mail" class="w-4 h-4 mr-2 inline"></i>
                                Send Email
                            </button>
                            
                            <button class="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200">
                                <i data-lucide="printer" class="w-4 h-4 mr-2 inline"></i>
                                Print Ticket
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
