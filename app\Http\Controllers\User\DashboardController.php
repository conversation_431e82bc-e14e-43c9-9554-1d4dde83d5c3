<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Order;
use App\Models\UangTix;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // User Statistics
        $stats = [
            'total_orders' => Order::where('user_id', $user->id)->count(),
            'upcoming_events' => Order::where('user_id', $user->id)
                ->where('status', 'completed')
                ->whereHas('event', function($q) {
                    $q->where('event_date', '>', Carbon::now());
                })
                ->count(),
            'uangtix_balance' => UangTix::where('user_id', $user->id)
                ->where('status', 'active')
                ->sum('balance'),
            'total_spent' => Order::where('user_id', $user->id)
                ->where('status', 'completed')
                ->sum('total_amount'),
        ];

        // Recent Orders
        $recentOrders = Order::where('user_id', $user->id)
            ->with(['event'])
            ->latest()
            ->take(5)
            ->get();

        // Upcoming Events (from user's orders)
        $upcomingEvents = Event::whereHas('orders', function($q) use ($user) {
                $q->where('user_id', $user->id)
                  ->where('status', 'completed');
            })
            ->where('event_date', '>', Carbon::now())
            ->orderBy('event_date')
            ->take(5)
            ->get();

        // Recommended Events (based on user's order history)
        $recommendedEvents = $this->getRecommendedEvents($user);

        return view('pages.user.dashboard', compact(
            'stats',
            'recentOrders',
            'upcomingEvents',
            'recommendedEvents'
        ));
    }

    private function getRecommendedEvents($user)
    {
        // Get categories from user's previous orders
        $userCategories = Event::whereHas('orders', function($q) use ($user) {
                $q->where('user_id', $user->id)
                  ->where('status', 'completed');
            })
            ->pluck('category_id')
            ->unique();

        // Get events in similar categories that user hasn't ordered
        $recommendedEvents = Event::where('status', 'published')
            ->where('event_date', '>', Carbon::now())
            ->whereIn('category_id', $userCategories)
            ->whereDoesntHave('orders', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->inRandomOrder()
            ->take(6)
            ->get();

        // If no recommendations based on categories, get popular events
        if ($recommendedEvents->count() < 3) {
            $popularEvents = Event::where('status', 'published')
                ->where('event_date', '>', Carbon::now())
                ->whereDoesntHave('orders', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->withCount(['orders' => function($q) {
                    $q->where('status', 'completed');
                }])
                ->orderBy('orders_count', 'desc')
                ->take(6 - $recommendedEvents->count())
                ->get();

            $recommendedEvents = $recommendedEvents->merge($popularEvents);
        }

        return $recommendedEvents;
    }
}
