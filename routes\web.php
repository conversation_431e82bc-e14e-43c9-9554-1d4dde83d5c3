<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\PasswordResetController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\Organizer\EventController as OrganizerEventController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Staff\DashboardController as StaffDashboardController;
use App\Http\Controllers\Organizer\DashboardController as OrganizerDashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/search', [HomeController::class, 'search'])->name('search');
Route::get('/category/{category}', [HomeController::class, 'ticketsByCategory'])->name('category');
Route::get('/nearby', [HomeController::class, 'nearbyTickets'])->name('nearby');

// Category Routes
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [CategoryController::class, 'index'])->name('index');
    Route::get('/search', [CategoryController::class, 'search'])->name('search');
    Route::get('/recommendations', [CategoryController::class, 'recommendations'])->name('recommendations');
    Route::get('/{category}', [CategoryController::class, 'show'])->name('show');
    Route::get('/{category}/analytics', [CategoryController::class, 'analytics'])->name('analytics')->middleware('admin');
});

// PWA Routes
Route::get('/offline', function () {
    return view('offline');
})->name('offline');

// Development/Testing Routes
Route::get('/credentials', function () {
    return view('auth.credentials');
})->name('auth.credentials');

// API Documentation
Route::get('/api/docs', function () {
    return view('api.documentation');
})->name('api.documentation');

// AJAX Routes
Route::get('/api/featured-tickets', [HomeController::class, 'featuredTickets'])->name('api.featured-tickets');
Route::get('/api/latest-tickets', [HomeController::class, 'latestTickets'])->name('api.latest-tickets');
Route::get('/api/popular-tickets', [HomeController::class, 'popularTickets'])->name('api.popular-tickets');
Route::get('/api/search-suggestions', [HomeController::class, 'searchSuggestions'])->name('api.search-suggestions');

// Ticket Routes (Public) - Event listing and browsing
Route::prefix('tickets')->name('tickets.')->group(function () {
    Route::get('/', [EventController::class, 'index'])->name('index');
    Route::get('/search', [EventController::class, 'search'])->name('search');
    Route::get('/featured', [EventController::class, 'featured'])->name('featured');
    Route::get('/upcoming', [EventController::class, 'upcoming'])->name('upcoming');
    Route::get('/nearby', [EventController::class, 'nearby'])->name('nearby');
    Route::get('/category/{category}', [EventController::class, 'byCategory'])->name('category');
    Route::get('/{event}', [EventController::class, 'show'])->name('show');

    // Wishlist routes (requires auth)
    Route::middleware('auth')->group(function () {
        Route::post('/{event}/wishlist', [EventController::class, 'addToWishlist'])->name('wishlist.add');
        Route::delete('/{event}/wishlist', [EventController::class, 'removeFromWishlist'])->name('wishlist.remove');

        // User ticket management routes
        Route::get('/my-tickets', [TicketController::class, 'myTickets'])->name('my-tickets');
        Route::get('/{event}/purchase', [TicketController::class, 'purchase'])->name('purchase');
        Route::post('/{event}', [TicketController::class, 'store'])->name('store');
        Route::get('/{ticket}/show', [TicketController::class, 'show'])->name('ticket.show');
        Route::get('/{ticket}/download', [TicketController::class, 'download'])->name('download');
        Route::post('/{ticket}/cancel', [TicketController::class, 'cancel'])->name('cancel');
        Route::post('/validate', [TicketController::class, 'validateTicket'])->name('validate');
    });
});

// Wishlist Management Routes
Route::middleware('auth')->prefix('wishlist')->name('wishlist.')->group(function () {
    Route::get('/', [App\Http\Controllers\WishlistController::class, 'index'])->name('index');
    Route::delete('/remove/{event}', [App\Http\Controllers\WishlistController::class, 'remove'])->name('remove');
    Route::delete('/clear', [App\Http\Controllers\WishlistController::class, 'clear'])->name('clear');
    Route::get('/count', [App\Http\Controllers\WishlistController::class, 'count'])->name('count');
});

// Redirect old routes for backward compatibility
// Remove the problematic self-redirecting routes that cause loops
Route::redirect('/tickets/tickets', '/tickets');
Route::redirect('/tickets/tickets/{path}', '/tickets/{path}')->where('path', '.*');

// Alias routes for backward compatibility
Route::get('/events/{event}', [EventController::class, 'show'])->name('events.show');

// Redirect routes for my-tickets (for backward compatibility and convenience)
Route::middleware('auth')->get('/my-tickets', function () {
    return redirect()->route('tickets.my-tickets');
})->name('my-tickets');

// Debug route to check if routing works
Route::get('/debug-routes', function () {
    $routes = collect(Route::getRoutes())->map(function ($route) {
        return [
            'method' => implode('|', $route->methods()),
            'uri' => $route->uri(),
            'name' => $route->getName(),
            'action' => $route->getActionName(),
        ];
    })->filter(function ($route) {
        return str_contains($route['uri'], 'tickets') || str_contains($route['name'] ?? '', 'tickets');
    });

    return response()->json($routes);
})->name('debug.routes');

// Order Routes (Requires Auth)
Route::middleware('auth')->prefix('orders')->name('orders.')->group(function () {
    Route::get('/', [OrderController::class, 'index'])->name('index');
    Route::get('/{order}', [OrderController::class, 'show'])->name('show');
    Route::get('/{order}/payment', [OrderController::class, 'payment'])->name('payment');
    Route::post('/{order}/payment', [OrderController::class, 'processPayment'])->name('process-payment');
    Route::get('/{order}/payment-details', [OrderController::class, 'paymentDetails'])->name('payment-details');
    Route::get('/{order}/check-status', [OrderController::class, 'checkPaymentStatus'])->name('check-status');
    Route::get('/{order}/success', [OrderController::class, 'success'])->name('success');
    Route::post('/{order}/cancel', [OrderController::class, 'cancel'])->name('cancel');
});

// Webhook Routes (No Auth Required)
Route::prefix('webhooks')->name('webhooks.')->group(function () {
    Route::post('/xendit', [App\Http\Controllers\WebhookController::class, 'xendit'])->name('xendit');
    Route::post('/midtrans', [App\Http\Controllers\WebhookController::class, 'midtrans'])->name('midtrans');
    Route::post('/tripay', [App\Http\Controllers\WebhookController::class, 'tripay'])->name('tripay');
});

// Voucher Routes (Requires Auth)
Route::middleware('auth')->prefix('vouchers')->name('vouchers.')->group(function () {
    Route::post('/validate', [App\Http\Controllers\VoucherController::class, 'validateVoucher'])->name('validate');
    Route::get('/available', [App\Http\Controllers\VoucherController::class, 'available'])->name('available');
    Route::get('/{code}', [App\Http\Controllers\VoucherController::class, 'show'])->name('show');
    Route::get('/usage/history', [App\Http\Controllers\VoucherController::class, 'userUsage'])->name('user-usage');
});

// UangTix Routes (Accessible to all authenticated users)
Route::middleware('auth')->prefix('uangtix')->name('uangtix.')->group(function () {
    Route::get('/', [App\Http\Controllers\UangTixController::class, 'index'])->name('index');
    Route::get('/balance', [App\Http\Controllers\UangTixController::class, 'balance'])->name('balance');
    Route::get('/transactions', [App\Http\Controllers\UangTixController::class, 'transactions'])->name('transactions');
    Route::post('/deposit', [App\Http\Controllers\UangTixController::class, 'deposit'])->name('deposit');
    Route::post('/withdraw', [App\Http\Controllers\UangTixController::class, 'withdraw'])->name('withdraw');
    Route::post('/transfer', [App\Http\Controllers\UangTixController::class, 'transfer'])->name('transfer');
    Route::get('/exchange-rate', [App\Http\Controllers\UangTixController::class, 'exchangeRate'])->name('exchange-rate');
});

// Purchase History Route (alias for orders.index)
Route::middleware('auth')->get('/history-pembelian', [OrderController::class, 'index'])->name('history-pembelian');

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Login
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Register
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);

    // Password Reset
    Route::get('/forgot-password', [PasswordResetController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [PasswordResetController::class, 'forgotPassword'])->name('password.email');
    Route::get('/reset-password/{token}', [PasswordResetController::class, 'showResetPassword'])->name('password.reset');
    Route::post('/reset-password', [PasswordResetController::class, 'resetPassword'])->name('password.update');
});

// Authenticated Routes
Route::middleware('auth')->group(function () {
    // Logout
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Email Verification
    Route::get('/verify-email', [AuthController::class, 'showVerifyEmail'])->name('auth.verify-email');
    Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
    Route::post('/resend-otp', [AuthController::class, 'resendOtp'])->name('auth.resend-otp');

    // Change Password
    Route::get('/change-password', [PasswordResetController::class, 'showChangePassword'])->name('password.change');
    Route::post('/change-password', [PasswordResetController::class, 'changePassword']);

    // Dashboard Routes (Role-based)
    Route::middleware('admin')->group(function () {
        Route::get('/admin/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');
        Route::get('/admin/dashboard/export', [AdminDashboardController::class, 'exportReport'])->name('admin.dashboard.export');
        Route::get('/admin/dashboard/report/{type}', [AdminDashboardController::class, 'generateReport'])->name('admin.dashboard.report');
        Route::get('/admin/dashboard/analytics', [AdminDashboardController::class, 'analytics'])->name('admin.dashboard.analytics');

        // Admin Event Management
        Route::prefix('admin/tickets')->name('admin.tickets.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\TicketController::class, 'index'])->name('index');

            Route::get('/create', [App\Http\Controllers\Admin\TicketController::class, 'create'])->name('create');

            Route::post('/', [App\Http\Controllers\Admin\TicketController::class, 'store'])->name('store');

            Route::get('/{event}/edit', [App\Http\Controllers\Admin\TicketController::class, 'edit'])->name('edit');

            Route::put('/{event}', [App\Http\Controllers\Admin\TicketController::class, 'update'])->name('update');
            Route::delete('/{event}', [App\Http\Controllers\Admin\TicketController::class, 'destroy'])->name('destroy');
            Route::get('/{event}', [App\Http\Controllers\Admin\TicketController::class, 'show'])->name('show');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\TicketController::class, 'bulkAction'])->name('bulk-action');
            Route::post('/{event}/toggle-featured', [App\Http\Controllers\Admin\TicketController::class, 'toggleFeatured'])->name('toggle-featured');
        });

        // Admin User Management
        Route::prefix('admin/users')->name('admin.users.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\UserController::class, 'create'])->name('create');

            Route::post('/', [App\Http\Controllers\Admin\UserController::class, 'store'])->name('store');
            Route::get('/{user}', [App\Http\Controllers\Admin\UserController::class, 'show'])->name('show');
            Route::get('/{user}/edit', [App\Http\Controllers\Admin\UserController::class, 'edit'])->name('edit');
            Route::put('/{user}', [App\Http\Controllers\Admin\UserController::class, 'update'])->name('update');
            Route::delete('/{user}', [App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\UserController::class, 'bulkAction'])->name('bulk-action');
            Route::post('/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/{user}/verify-email', [App\Http\Controllers\Admin\UserController::class, 'verifyEmail'])->name('verify-email');
        });

        // Admin User Level Management
        Route::prefix('admin/user-levels')->name('admin.user-levels.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\UserLevelController::class, 'index'])->name('index');
            Route::post('/{user}/update', [App\Http\Controllers\Admin\UserLevelController::class, 'updateLevel'])->name('update');
            Route::post('/bulk-update', [App\Http\Controllers\Admin\UserLevelController::class, 'bulkUpdateLevel'])->name('bulk-update');
            Route::post('/auto-upgrade', [App\Http\Controllers\Admin\UserLevelController::class, 'autoUpgrade'])->name('auto-upgrade');
            Route::get('/export', [App\Http\Controllers\Admin\UserLevelController::class, 'export'])->name('export');
            Route::get('/level-info/{level}', [App\Http\Controllers\Admin\UserLevelController::class, 'getLevelInfo'])->name('level-info');
        });

        // Admin Payment Management
        Route::prefix('admin/payments')->name('admin.payments.')->group(function () {
            Route::get('/', function () {
                $payments = \App\Models\Order::with(['user', 'event'])
                    ->whereNotNull('payment_method')
                    ->latest()
                    ->paginate(15);

                $stats = [
                    'total_revenue' => \App\Models\Order::where('status', 'completed')->sum('total_amount'),
                    'pending_payments' => \App\Models\Order::where('status', 'pending')->count(),
                    'completed_payments' => \App\Models\Order::where('status', 'completed')->count(),
                    'failed_payments' => \App\Models\Order::where('status', 'failed')->count(),
                ];

                return view('pages.admin.payments', compact('payments', 'stats'));
            })->name('index');

            Route::get('/{order}', function (\App\Models\Order $order) {
                $order->load(['user', 'event', 'tickets']);
                return view('pages.admin.payments.show', compact('order'));
            })->name('show');

            Route::post('/{order}/approve', function (\App\Models\Order $order) {
                $order->update(['status' => 'completed']);
                return redirect()->back()->with('success', 'Payment approved successfully!');
            })->name('approve');

            Route::post('/{order}/reject', function (\App\Models\Order $order) {
                $order->update(['status' => 'failed']);
                return redirect()->back()->with('success', 'Payment rejected successfully!');
            })->name('reject');
        });

        // Admin Notification Management
        Route::prefix('admin/notifications')->name('admin.notifications.')->group(function () {
            Route::get('/', function () {
                $notifications = \App\Models\Notification::with('user')
                    ->latest()
                    ->paginate(15);

                $stats = [
                    'total_notifications' => \App\Models\Notification::count(),
                    'unread_notifications' => \App\Models\Notification::whereNull('read_at')->count(),
                    'system_notifications' => \App\Models\Notification::where('type', 'system')->count(),
                    'user_notifications' => \App\Models\Notification::where('type', 'user')->count(),
                ];

                return view('pages.admin.notifications', compact('notifications', 'stats'));
            })->name('index');

            Route::get('/create', function () {
                $users = \App\Models\User::select('id', 'name', 'email')->get();
                return view('pages.admin.notifications.create', compact('users'));
            })->name('create');

            Route::post('/', function (\Illuminate\Http\Request $request) {
                $request->validate([
                    'title' => 'required|string|max:255',
                    'message' => 'required|string',
                    'type' => 'required|in:system,user,event,payment',
                    'recipients' => 'required|in:all,specific,role',
                    'user_ids' => 'required_if:recipients,specific|array',
                    'role' => 'required_if:recipients,role|in:admin,staff,penjual,pembeli',
                ]);

                $recipients = [];

                if ($request->recipients === 'all') {
                    $recipients = \App\Models\User::pluck('id')->toArray();
                } elseif ($request->recipients === 'specific') {
                    $recipients = $request->user_ids;
                } elseif ($request->recipients === 'role') {
                    $recipients = \App\Models\User::where('role', $request->role)->pluck('id')->toArray();
                }

                foreach ($recipients as $userId) {
                    \App\Models\Notification::create([
                        'user_id' => $userId,
                        'title' => $request->title,
                        'message' => $request->message,
                        'type' => $request->type,
                        'data' => json_encode(['sent_by' => auth()->id()]),
                    ]);
                }

                return redirect()->route('admin.notifications.index')->with('success', 'Notifications sent successfully!');
            })->name('store');
        });

        // Admin Organizer Management
        Route::prefix('admin/organizers')->name('admin.organizers.')->group(function () {
            Route::get('/', function () {
                $organizers = \App\Models\User::where('role', 'penjual')
                    ->withCount(['organizedEvents', 'orders'])
                    ->with(['organizedEvents' => function($query) {
                        $query->select('id', 'organizer_id', 'title', 'status', 'total_capacity')
                              ->withCount('tickets');
                    }])
                    ->latest()
                    ->paginate(10);

                $stats = [
                    'total_organizers' => \App\Models\User::where('role', 'penjual')->count(),
                    'active_organizers' => \App\Models\User::where('role', 'penjual')->where('is_active', true)->count(),
                    'pending_approval' => \App\Models\User::where('role', 'penjual')->whereNull('email_verified_at')->count(),
                    'total_tickets' => \App\Models\Event::count(),
                ];

                return view('pages.admin.organizers', compact('organizers', 'stats'));
            })->name('index');

            Route::get('/{organizer}', function (\App\Models\User $organizer) {
                $organizer->load(['organizedEvents.category', 'organizedEvents.tickets']);

                $analytics = [
                    'total_events' => $organizer->organizedEvents->count(),
                    'total_tickets' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->count();
                    }),
                    'total_revenue' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->where('status', 'used')->sum('price');
                    }),
                    'total_tickets_sold' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->where('status', 'used')->count();
                    }),
                    'avg_event_rating' => 4.5, // Placeholder for rating system
                ];

                return view('pages.admin.organizers.show', compact('organizer', 'analytics'));
            })->name('show');

            Route::post('/{organizer}/approve', function (\App\Models\User $organizer) {
                $organizer->update([
                    'email_verified_at' => now(),
                    'is_active' => true
                ]);

                // Send approval notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Approved',
                    'message' => 'Your organizer account has been approved. You can now create and manage tickets.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer approved successfully!');
            })->name('approve');

            Route::post('/{organizer}/suspend', function (\App\Models\User $organizer) {
                $organizer->update(['is_active' => false]);

                // Send suspension notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Suspended',
                    'message' => 'Your organizer account has been suspended. Please contact support for more information.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer suspended successfully!');
            })->name('suspend');

            Route::post('/{organizer}/activate', function (\App\Models\User $organizer) {
                $organizer->update(['is_active' => true]);

                // Send activation notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Activated',
                    'message' => 'Your organizer account has been activated. You can now create and manage tickets.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer activated successfully!');
            })->name('activate');
        });

        // Admin Category Management
        Route::prefix('admin/categories')->name('admin.categories.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\CategoryController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\CategoryController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\CategoryController::class, 'store'])->name('store');
            Route::get('/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'show'])->name('show');
            Route::get('/{category}/edit', [App\Http\Controllers\Admin\CategoryController::class, 'edit'])->name('edit');
            Route::put('/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'update'])->name('update');
            Route::delete('/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'destroy'])->name('destroy');
            Route::post('/{category}/toggle-status', [App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\CategoryController::class, 'bulkAction'])->name('bulk-action');
            Route::post('/update-sort-order', [App\Http\Controllers\Admin\CategoryController::class, 'updateSortOrder'])->name('update-sort-order');
            Route::get('/{category}/analytics', [App\Http\Controllers\Admin\CategoryController::class, 'analytics'])->name('analytics');
        });

        // Admin Order Management
        Route::prefix('admin/orders')->name('admin.orders.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\OrderController::class, 'index'])->name('index');
            Route::get('/{order}', [App\Http\Controllers\Admin\OrderController::class, 'show'])->name('show');
            Route::patch('/{order}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('update-status');
            Route::patch('/{order}/payment-status', [App\Http\Controllers\Admin\OrderController::class, 'updatePaymentStatus'])->name('update-payment-status');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\OrderController::class, 'bulkAction'])->name('bulk-action');
            Route::get('/export/csv', [App\Http\Controllers\Admin\OrderController::class, 'export'])->name('export');
        });

        // Admin Voucher Management
        Route::prefix('admin/vouchers')->name('admin.vouchers.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\VoucherController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\VoucherController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\VoucherController::class, 'store'])->name('store');
            Route::get('/{voucher}', [App\Http\Controllers\Admin\VoucherController::class, 'show'])->name('show');
            Route::get('/{voucher}/edit', [App\Http\Controllers\Admin\VoucherController::class, 'edit'])->name('edit');
            Route::put('/{voucher}', [App\Http\Controllers\Admin\VoucherController::class, 'update'])->name('update');
            Route::delete('/{voucher}', [App\Http\Controllers\Admin\VoucherController::class, 'destroy'])->name('destroy');
            Route::post('/{voucher}/toggle-status', [App\Http\Controllers\Admin\VoucherController::class, 'toggleStatus'])->name('toggle-status');
            Route::get('/generate/code', [App\Http\Controllers\Admin\VoucherController::class, 'generateCode'])->name('generate-code');
        });

        // Admin Settings Management
        Route::prefix('admin/settings')->name('admin.settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');
            Route::post('/update-app', [App\Http\Controllers\Admin\SettingsController::class, 'updateAppSettings'])->name('update-app');
            Route::post('/update-mail', [App\Http\Controllers\Admin\SettingsController::class, 'updateMailSettings'])->name('update-mail');
            Route::post('/update-fees', [App\Http\Controllers\Admin\SettingsController::class, 'updateFeeSettings'])->name('update-fees');
            Route::post('/update-subscriptions', [App\Http\Controllers\Admin\SettingsController::class, 'updateSubscriptionPricing'])->name('update-subscriptions');
            Route::post('/update-balance', [App\Http\Controllers\Admin\SettingsController::class, 'updateBalanceSettings'])->name('update-balance');
            Route::post('/clear-cache', [App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('clear-cache');
            Route::post('/optimize', [App\Http\Controllers\Admin\SettingsController::class, 'optimize'])->name('optimize');
            Route::post('/clean-storage', [App\Http\Controllers\Admin\SettingsController::class, 'cleanStorage'])->name('clean-storage');
        });

        // Admin Balance Management
        Route::prefix('admin/balance')->name('admin.balance.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\BalanceController::class, 'index'])->name('index');
            Route::get('/withdrawals', [App\Http\Controllers\Admin\BalanceController::class, 'withdrawals'])->name('withdrawals');
            Route::post('/{transaction}/approve', [App\Http\Controllers\Admin\BalanceController::class, 'approveWithdrawal'])->name('approve-withdrawal');
            Route::post('/{transaction}/reject', [App\Http\Controllers\Admin\BalanceController::class, 'rejectWithdrawal'])->name('reject-withdrawal');
            Route::post('/bulk-approve', [App\Http\Controllers\Admin\BalanceController::class, 'bulkApproveWithdrawals'])->name('bulk-approve');
            Route::post('/{user}/adjust', [App\Http\Controllers\Admin\BalanceController::class, 'adjustBalance'])->name('adjust');
            Route::get('/export', [App\Http\Controllers\Admin\BalanceController::class, 'export'])->name('export');
        });

        // Admin UangTix Management (Admin Only)
        Route::prefix('admin/uangtix')->name('admin.uangtix.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\UangTixController::class, 'index'])->name('index');
            Route::get('/settings', [App\Http\Controllers\Admin\UangTixController::class, 'settings'])->name('settings');
            Route::post('/settings', [App\Http\Controllers\Admin\UangTixController::class, 'updateSettings'])->name('update-settings');
            Route::get('/requests', [App\Http\Controllers\Admin\UangTixController::class, 'requests'])->name('requests');
            Route::post('/requests/{uangTixRequest}/approve', [App\Http\Controllers\Admin\UangTixController::class, 'approveRequest'])->name('approve-request');
            Route::post('/requests/{uangTixRequest}/reject', [App\Http\Controllers\Admin\UangTixController::class, 'rejectRequest'])->name('reject-request');
            Route::post('/requests/bulk-approve', [App\Http\Controllers\Admin\UangTixController::class, 'bulkApproveRequests'])->name('bulk-approve-requests');
            Route::post('/{user}/adjust', [App\Http\Controllers\Admin\UangTixController::class, 'adjustBalance'])->name('adjust');
            Route::post('/{user}/toggle-status', [App\Http\Controllers\Admin\UangTixController::class, 'toggleBalanceStatus'])->name('toggle-status');
            Route::get('/transactions', [App\Http\Controllers\Admin\UangTixController::class, 'transactions'])->name('transactions');
            Route::get('/export', [App\Http\Controllers\Admin\UangTixController::class, 'export'])->name('export');
        });

        // Admin Analytics Management
        Route::prefix('admin/analytics')->name('admin.analytics.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('index');
            Route::get('/revenue', [App\Http\Controllers\Admin\AnalyticsController::class, 'revenue'])->name('revenue');
            Route::get('/users', [App\Http\Controllers\Admin\AnalyticsController::class, 'users'])->name('users');
            Route::get('/events', [App\Http\Controllers\Admin\AnalyticsController::class, 'events'])->name('events');
            Route::get('/geographic', [App\Http\Controllers\Admin\AnalyticsController::class, 'geographic'])->name('geographic');
            Route::get('/export', [App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('export');
        });

        // Admin Help & Support Management
        Route::prefix('admin/help')->name('admin.help.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\HelpController::class, 'index'])->name('index');
            Route::get('/tickets', [App\Http\Controllers\Admin\HelpController::class, 'tickets'])->name('tickets');
            Route::get('/faq', [App\Http\Controllers\Admin\HelpController::class, 'faq'])->name('faq');
            Route::get('/documentation', [App\Http\Controllers\Admin\HelpController::class, 'documentation'])->name('documentation');
        });

        // Admin Profile Management
        Route::prefix('admin/profile')->name('admin.profile.')->group(function () {
            Route::get('/edit', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('edit');
            Route::put('/update', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('update');
            Route::put('/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('password');
        });
    });

    Route::middleware('staff')->group(function () {
        Route::get('/staff/dashboard', [StaffDashboardController::class, 'index'])->name('staff.dashboard');
        Route::get('/staff/dashboard/stats', [StaffDashboardController::class, 'getStats'])->name('staff.dashboard.stats');
        Route::get('/staff/dashboard/today-events', [StaffDashboardController::class, 'getTodayEventsApi'])->name('staff.dashboard.today-events');
        Route::get('/staff/dashboard/validation-history', [StaffDashboardController::class, 'getValidationHistory'])->name('staff.dashboard.validation-history');
        Route::post('/staff/validate-ticket', [StaffDashboardController::class, 'validateTicket'])->name('staff.validate-ticket');

        Route::get('/staff/scanner', function () {
            return view('pages.staff.scanner');
        })->name('staff.scanner');
    });

    Route::middleware('organizer')->group(function () {
        Route::get('/organizer/dashboard', [OrganizerDashboardController::class, 'index'])->name('organizer.dashboard');

        // Organizer Event Management
        Route::prefix('organizer/tickets')->name('organizer.tickets.')->group(function () {
            Route::get('/', [OrganizerEventController::class, 'index'])->name('index');
            Route::get('/create', [OrganizerEventController::class, 'create'])->name('create');
            Route::post('/', [OrganizerEventController::class, 'store'])->name('store');
            Route::get('/{event}', [OrganizerEventController::class, 'show'])->name('show');
            Route::get('/{event}/edit', [OrganizerEventController::class, 'edit'])->name('edit');
            Route::put('/{event}', [OrganizerEventController::class, 'update'])->name('update');
            Route::delete('/{event}', [OrganizerEventController::class, 'destroy'])->name('destroy');
            Route::post('/{event}/publish', [OrganizerEventController::class, 'publish'])->name('publish');
            Route::post('/{event}/unpublish', [OrganizerEventController::class, 'unpublish'])->name('unpublish');
            Route::get('/{event}/qr-code', [OrganizerEventController::class, 'downloadQRCode'])->name('qr-code');
        });

        // Organizer Order Management
        Route::prefix('organizer/orders')->name('organizer.orders.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\OrderController::class, 'index'])->name('index');
            Route::get('/{order}', [App\Http\Controllers\Organizer\OrderController::class, 'show'])->name('show');
            Route::patch('/{order}/status', [App\Http\Controllers\Organizer\OrderController::class, 'updateStatus'])->name('update-status');
            Route::get('/export/csv', [App\Http\Controllers\Organizer\OrderController::class, 'export'])->name('export');
        });

        // Organizer Analytics Management
        Route::prefix('organizer/analytics')->name('organizer.analytics.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\AnalyticsController::class, 'index'])->name('index');
            Route::get('/revenue', [App\Http\Controllers\Organizer\AnalyticsController::class, 'revenue'])->name('revenue');
            Route::get('/events', [App\Http\Controllers\Organizer\AnalyticsController::class, 'events'])->name('events');
            Route::get('/export', [App\Http\Controllers\Organizer\AnalyticsController::class, 'export'])->name('export');
        });

        // Organizer Payment Management
        Route::prefix('organizer/payments')->name('organizer.payments.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\PaymentController::class, 'index'])->name('index');
            Route::get('/{payment}', [App\Http\Controllers\Organizer\PaymentController::class, 'show'])->name('show');
            Route::get('/export/csv', [App\Http\Controllers\Organizer\PaymentController::class, 'export'])->name('export');
        });

        // Organizer Profile Management
        Route::prefix('organizer/profile')->name('organizer.profile.')->group(function () {
            Route::get('/edit', [App\Http\Controllers\Organizer\ProfileController::class, 'edit'])->name('edit');
            Route::put('/update', [App\Http\Controllers\Organizer\ProfileController::class, 'update'])->name('update');
            Route::put('/password', [App\Http\Controllers\Organizer\ProfileController::class, 'updatePassword'])->name('password');
        });

        // Organizer Settings Management
        Route::prefix('organizer/settings')->name('organizer.settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\SettingsController::class, 'index'])->name('index');
            Route::post('/update', [App\Http\Controllers\Organizer\SettingsController::class, 'update'])->name('update');
        });
    });

    // User Dashboard (for pembeli and others)
    Route::get('/dashboard', [App\Http\Controllers\User\DashboardController::class, 'index'])->name('dashboard');

    // User Events Routes
    Route::prefix('events')->name('events.')->group(function () {
        Route::get('/', [EventController::class, 'index'])->name('index');
        Route::get('/{event}', [EventController::class, 'show'])->name('show');
    });

    // User Settings Routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [App\Http\Controllers\User\SettingsController::class, 'index'])->name('index');
        Route::post('/update', [App\Http\Controllers\User\SettingsController::class, 'update'])->name('update');
    });

    // User Profile Edit Routes
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('edit');
    });

    // Test Routes (Development only)
    if (app()->environment(['local', 'staging'])) {
        Route::get('/test/mobile-payment', function () {
            return view('test.mobile-payment');
        })->name('test.mobile-payment');
    }

    // Profile Routes
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'show'])->name('profile');
    Route::post('/profile/update', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password');
    Route::post('/profile/photo', [App\Http\Controllers\ProfileController::class, 'updatePhoto'])->name('profile.photo');
    Route::post('/profile/notifications/email', [App\Http\Controllers\ProfileController::class, 'toggleEmailNotification'])->name('profile.notifications.email');
    Route::post('/profile/notifications/push', [App\Http\Controllers\ProfileController::class, 'togglePushNotification'])->name('profile.notifications.push');
    Route::post('/profile/notifications/sms', [App\Http\Controllers\ProfileController::class, 'toggleSmsNotification'])->name('profile.notifications.sms');
    Route::delete('/profile/delete', [App\Http\Controllers\ProfileController::class, 'deleteAccount'])->name('profile.delete');

    // Notification Routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\NotificationController::class, 'index'])->name('index');
        Route::get('/latest', [App\Http\Controllers\NotificationController::class, 'latest'])->name('latest');
        Route::get('/unread-count', [App\Http\Controllers\NotificationController::class, 'unreadCount'])->name('unread-count');
        Route::post('/{notification}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('mark-read');
        Route::post('/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::delete('/{notification}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('destroy');
        Route::post('/test', [App\Http\Controllers\NotificationController::class, 'test'])->name('test');
    });

    // Alias route for backward compatibility
    Route::get('/notifikasi', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifikasi');
});

// Test routes (only in development)
if (app()->environment('local')) {
    Route::get('/test/organizer-preview', function () {
        return view('test.organizer-preview');
    })->name('test.organizer-preview');

    Route::get('/test/user-levels', function () {
        return view('test.user-levels');
    })->name('test.user-levels');
}

// Fallback route for debugging 404 issues
Route::fallback(function () {
    $requestedUrl = request()->fullUrl();
    $requestedPath = request()->path();

    // Check if this is a common misrouted URL with /public/
    if (str_contains($requestedPath, 'public/')) {
        $correctedPath = str_replace('public/', '', $requestedPath);

        // Handle specific routes
        if ($correctedPath === 'tickets/my-tickets') {
            if (auth()->check()) {
                return redirect()->route('tickets.my-tickets');
            } else {
                return redirect()->route('login')->with('intended', route('tickets.my-tickets'));
            }
        }

        if ($correctedPath === 'my-tickets') {
            if (auth()->check()) {
                return redirect()->route('my-tickets');
            } else {
                return redirect()->route('login')->with('intended', route('my-tickets'));
            }
        }

        if (str_starts_with($correctedPath, 'tickets/')) {
            return redirect('/' . $correctedPath);
        }

        // General redirect for other /public/ URLs
        if (!empty($correctedPath)) {
            return redirect('/' . $correctedPath);
        } else {
            return redirect('/');
        }
    }

    // Check for other common URL patterns
    $commonRedirects = [
        'ticket/my-tickets' => 'tickets/my-tickets',
        'event/my-tickets' => 'tickets/my-tickets',
        'tickets/my-tickets' => 'tickets/my-tickets',
        'user/tickets' => 'tickets/my-tickets',
        'profile/tickets' => 'tickets/my-tickets',
    ];

    foreach ($commonRedirects as $pattern => $redirect) {
        if ($requestedPath === $pattern) {
            return redirect('/' . $redirect);
        }
    }

    // Log the 404 for debugging
    \Log::warning('404 Error - Route not found', [
        'url' => $requestedUrl,
        'path' => $requestedPath,
        'method' => request()->method(),
        'user_agent' => request()->userAgent(),
        'ip' => request()->ip(),
        'referer' => request()->header('referer'),
    ]);

    return response()->view('errors.404', [
        'requestedUrl' => $requestedUrl,
        'suggestedRoutes' => [
            'Home' => route('home'),
            'Tickets' => route('tickets.index'),
            'My Tickets' => auth()->check() ? route('tickets.my-tickets') : route('login'),
            'Login' => !auth()->check() ? route('login') : null,
        ]
    ], 404);
});

