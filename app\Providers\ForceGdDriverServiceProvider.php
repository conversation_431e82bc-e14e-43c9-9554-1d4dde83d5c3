<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class ForceGdDriverServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Force GD driver configuration as early as possible
        $this->forceGdDriverConfiguration();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Ensure GD driver is forced after all other providers are loaded
        $this->forceGdDriverConfiguration();
        
        // Log the driver being used for debugging
        if ($this->app->bound('log')) {
            \Log::info('Image driver forced to GD for compatibility');
        }
    }

    /**
     * Force GD driver configuration
     */
    private function forceGdDriverConfiguration(): void
    {
        // Force environment variables
        putenv('IMAGE_DRIVER=gd');
        $_ENV['IMAGE_DRIVER'] = 'gd';
        $_SERVER['IMAGE_DRIVER'] = 'gd';

        // Override configuration if available
        if ($this->app->bound('config')) {
            config(['image.driver' => 'gd']);
            config(['intervention.driver' => \Intervention\Image\Drivers\Gd\Driver::class]);
        }
    }
}
