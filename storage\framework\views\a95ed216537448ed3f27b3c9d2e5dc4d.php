<?php $__env->startSection('title', 'Permintaan UangTix'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-clock text-warning me-2"></i>
                Permintaan UangTix
            </h1>
            <p class="text-muted">Kelola permintaan deposit dan penarikan UangTix</p>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('admin.uangtix.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <button type="button" class="btn btn-success" onclick="bulkApprove()">
                <i class="fas fa-check-double"></i> Setu<PERSON><PERSON>
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Pending
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e($stats['pending_count']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Deposit Pending
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e($stats['pending_deposits']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Penarikan Pending
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e($stats['pending_withdrawals']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-minus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Selesai Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo e($stats['completed_today']); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter & Pencarian</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.uangtix.requests')); ?>" class="row">
                <div class="col-md-3 mb-3">
                    <label for="search" class="form-label">Pencarian</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo e(request('search')); ?>" placeholder="Nama atau email pengguna...">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="type" class="form-label">Jenis</label>
                    <select class="form-control" id="type" name="type">
                        <option value="">Semua Jenis</option>
                        <option value="deposit" <?php echo e(request('type') == 'deposit' ? 'selected' : ''); ?>>Deposit</option>
                        <option value="withdrawal" <?php echo e(request('type') == 'withdrawal' ? 'selected' : ''); ?>>Penarikan</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">Semua Status</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Disetujui</option>
                        <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Selesai</option>
                        <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Ditolak</option>
                        <option value="failed" <?php echo e(request('status') == 'failed' ? 'selected' : ''); ?>>Gagal</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="<?php echo e(route('admin.uangtix.requests')); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Requests Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Permintaan</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>No. Permintaan</th>
                            <th>Pengguna</th>
                            <th>Jenis</th>
                            <th>Jumlah</th>
                            <th>Status</th>
                            <th>Tanggal</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $requests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $request): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <?php if($request->isPending()): ?>
                                        <input type="checkbox" class="request-checkbox" value="<?php echo e($request->id); ?>">
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="font-weight-bold"><?php echo e($request->request_number); ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo e($request->user->avatar_url); ?>" alt="<?php echo e($request->user->name); ?>" 
                                             class="rounded-circle me-2" width="30" height="30">
                                        <div>
                                            <div class="font-weight-bold"><?php echo e($request->user->name); ?></div>
                                            <div class="text-muted small"><?php echo e($request->user->email); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo e($request->type == 'deposit' ? 'success' : 'warning'); ?>">
                                        <i class="fas fa-<?php echo e($request->type == 'deposit' ? 'plus' : 'minus'); ?> me-1"></i>
                                        <?php echo e($request->type_label); ?>

                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <span class="font-weight-bold"><?php echo e($request->formatted_amount); ?></span>
                                        <?php if($request->fee_amount > 0): ?>
                                            <div class="text-muted small">
                                                Fee: <?php echo e(number_format($request->fee_amount, 0, ',', '.')); ?> 
                                                <?php echo e($request->isDeposit() ? 'IDR' : 'UTX'); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo e($request->status_color); ?>">
                                        <?php echo e($request->status_label); ?>

                                    </span>
                                </td>
                                <td>
                                    <div><?php echo e($request->created_at->format('d/m/Y H:i')); ?></div>
                                    <?php if($request->processed_at): ?>
                                        <div class="text-muted small">
                                            Diproses: <?php echo e($request->processed_at->format('d/m/Y H:i')); ?>

                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($request->isPending()): ?>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-success" 
                                                    onclick="approveRequest(<?php echo e($request->id); ?>)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="rejectRequest(<?php echo e($request->id); ?>)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewRequestDetails(<?php echo e($request->id); ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>Tidak ada permintaan yang ditemukan</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($requests->hasPages()): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($requests->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Setujui Permintaan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="approveForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="adminNotes" class="form-label">Catatan Admin (Opsional)</label>
                        <textarea class="form-control" id="adminNotes" name="admin_notes" 
                                  rows="3" placeholder="Catatan untuk permintaan ini..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Setujui Permintaan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tolak Permintaan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">Alasan Penolakan</label>
                        <textarea class="form-control" id="rejectionReason" name="rejection_reason" 
                                  rows="3" required placeholder="Jelaskan alasan penolakan..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Tolak Permintaan</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentRequestId = null;

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.request-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function approveRequest(requestId) {
    currentRequestId = requestId;
    new bootstrap.Modal(document.getElementById('approveModal')).show();
}

function rejectRequest(requestId) {
    currentRequestId = requestId;
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

document.getElementById('approveForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    try {
        const response = await fetch(`/admin/uangtix/requests/${currentRequestId}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('approveModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

document.getElementById('rejectForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    try {
        const response = await fetch(`/admin/uangtix/requests/${currentRequestId}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

async function bulkApprove() {
    const selectedCheckboxes = document.querySelectorAll('.request-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        showAlert('warning', 'Pilih permintaan yang ingin disetujui');
        return;
    }
    
    const requestIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    if (!confirm(`Setujui ${requestIds.length} permintaan yang dipilih?`)) {
        return;
    }
    
    try {
        const response = await fetch('/admin/uangtix/requests/bulk-approve', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                request_ids: requestIds,
                admin_notes: 'Bulk approval'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

function showAlert(type, message) {
    // You can implement your preferred alert system here
    alert(message);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/uangtix/requests.blade.php ENDPATH**/ ?>