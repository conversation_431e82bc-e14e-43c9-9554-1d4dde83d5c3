# SEO Features Documentation

## Overview

TiXara now includes comprehensive SEO optimization features that help improve search engine visibility and social media sharing. This document outlines all the SEO features and how to use them.

## Features Added

### 1. Admin SEO Settings Management

**Location**: Admin Dashboard → Settings → SEO Settings

**Features**:
- **Basic SEO**: Site title, description, and keywords
- **Social Media**: Open Graph image and Twitter handle
- **Analytics & Tracking**: Google Analytics, Google Tag Manager, Facebook Pixel
- **Site Verification**: Google Search Console and Bing Webmaster verification
- **Advanced Settings**: Robots.txt content and structured data toggle

### 2. Dynamic Meta Tags

**Automatic Generation**:
- Page-specific titles and descriptions
- Open Graph tags for social media sharing
- Twitter Card meta tags
- Canonical URLs
- Site verification meta tags

**Usage in Views**:
```php
// In your controller
$seoTitle = 'Custom Page Title';
$seoDescription = 'Custom page description';
$seoImage = asset('images/custom-image.jpg');
$seoUrl = url()->current();
$seoType = 'article'; // or 'website', 'product', etc.

return view('your.view', compact('seoTitle', 'seoDescription', 'seoImage', 'seoUrl', 'seoType'));
```

### 3. Structured Data (JSON-LD)

**Automatic Generation**:
- Event structured data for event pages
- Organization structured data
- Breadcrumb navigation
- Product/Service data

**Event Example**:
```php
// In EventController
$structuredData = \App\Services\SeoService::generateEventStructuredData($event);
```

### 4. SEO Service Class

**Location**: `app/Services/SeoService.php`

**Available Methods**:
- `getMetaTags()` - Get meta tags for a page
- `generateEventStructuredData()` - Generate event structured data
- `generateOrganizationStructuredData()` - Generate organization data
- `generateBreadcrumbStructuredData()` - Generate breadcrumb data
- `getTrackingCodes()` - Get analytics tracking codes
- `getVerificationCodes()` - Get site verification codes
- `getRobotsTxt()` - Get robots.txt content
- `generateSitemapUrls()` - Generate sitemap URLs

### 5. Automatic SEO Routes

**Robots.txt**: `/robots.txt`
- Automatically generated from admin settings
- Includes sitemap reference
- Configurable disallow rules

**Sitemap.xml**: `/sitemap.xml`
- Automatically generated XML sitemap
- Includes all published events
- Cached for performance
- Proper lastmod, changefreq, and priority

### 6. Analytics Integration

**Supported Platforms**:
- Google Analytics 4 (GA4)
- Google Tag Manager (GTM)
- Facebook Pixel

**Features**:
- Automatic tracking code injection
- Page view tracking
- Event tracking (for Facebook Pixel)
- No-script fallbacks

## Configuration

### Admin Settings

1. **Access Settings**:
   - Login as admin
   - Go to Admin Dashboard → Settings
   - Scroll to "SEO Settings" section

2. **Basic SEO Configuration**:
   - Set default site title
   - Configure site description (160 characters max)
   - Add relevant keywords

3. **Social Media Setup**:
   - Upload Open Graph image (1200x630px recommended)
   - Set Twitter handle

4. **Analytics Setup**:
   - Add Google Analytics ID (format: G-XXXXXXXXXX)
   - Add Google Tag Manager ID (format: GTM-XXXXXXX)
   - Add Facebook Pixel ID (numeric)

5. **Site Verification**:
   - Add Google Search Console verification code
   - Add Bing Webmaster verification code

### Event-Specific SEO

Events automatically inherit SEO settings but can be customized:

1. **Event Meta Fields**:
   - `meta_title` - Custom title for the event
   - `meta_description` - Custom description for the event
   - `tags` - Event-specific tags

2. **Automatic Generation**:
   - If meta fields are empty, they're auto-generated from event data
   - Structured data is automatically created for all published events

## Best Practices

### 1. Title Optimization
- Keep titles under 60 characters
- Include primary keywords
- Make titles unique and descriptive

### 2. Description Optimization
- Keep descriptions between 150-160 characters
- Include call-to-action
- Summarize page content effectively

### 3. Image Optimization
- Use high-quality images (1200x630px for Open Graph)
- Include alt text for accessibility
- Optimize file sizes for fast loading

### 4. Structured Data
- Enable structured data for better search results
- Regularly validate using Google's Rich Results Test
- Keep event information accurate and up-to-date

### 5. Analytics Setup
- Set up goal tracking in Google Analytics
- Configure conversion tracking for ticket sales
- Monitor search performance in Search Console

## Monitoring and Maintenance

### 1. Regular Checks
- Monitor robots.txt accessibility
- Verify sitemap.xml updates automatically
- Check structured data validity
- Review analytics data regularly

### 2. Performance
- SEO data is cached for performance
- Cache is automatically cleared when settings change
- Sitemap is regenerated every hour

### 3. Troubleshooting
- Clear cache if SEO changes don't appear: Admin → Settings → Clear Cache
- Verify tracking codes are properly formatted
- Check that structured data is enabled in settings

## Technical Implementation

### Database Changes
- Added SEO settings to `platform_settings` table
- Event model includes SEO meta fields
- Cached settings for performance

### Service Integration
- `SeoService` class handles all SEO operations
- Automatic meta tag injection in layouts
- Dynamic structured data generation

### Route Integration
- SEO routes for robots.txt and sitemap.xml
- Admin routes for SEO settings management
- Automatic cache management

## Support

For technical support or questions about SEO features:
1. Check the admin settings for proper configuration
2. Verify that all required fields are filled
3. Test using Google's SEO tools
4. Contact development team for advanced customization

---

**Last Updated**: December 2024
**Version**: 1.0
