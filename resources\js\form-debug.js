/**
 * Form Debug Helper
 * Utility untuk debugging form validation issues
 */

class FormDebugger {
    constructor() {
        this.isDebugMode = window.location.search.includes('debug=true');
        this.init();
    }

    init() {
        if (this.isDebugMode) {
            console.log('🐛 Form Debug Mode Enabled');
            this.addDebugPanel();
            this.monitorFormChanges();
        }
    }

    addDebugPanel() {
        const debugPanel = document.createElement('div');
        debugPanel.id = 'form-debug-panel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 400px;
            background: #1a1a1a;
            color: #00ff00;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border-radius: 5px;
            z-index: 9999;
            overflow-y: auto;
            box-shadow: 0 4px 6px rgba(0,0,0,0.3);
        `;
        
        debugPanel.innerHTML = `
            <div style="border-bottom: 1px solid #333; padding-bottom: 5px; margin-bottom: 10px;">
                <strong>🐛 Form Debug Panel</strong>
                <button onclick="this.parentElement.parentElement.remove()" style="float: right; background: #ff4444; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">×</button>
            </div>
            <div id="debug-content">
                <div>Status: Monitoring...</div>
            </div>
        `;
        
        document.body.appendChild(debugPanel);
    }

    updateDebugPanel(data) {
        const content = document.getElementById('debug-content');
        if (!content) return;

        const timestamp = new Date().toLocaleTimeString();
        content.innerHTML = `
            <div><strong>Last Update:</strong> ${timestamp}</div>
            <div><strong>Form Data:</strong></div>
            <pre style="font-size: 10px; margin: 5px 0;">${JSON.stringify(data, null, 2)}</pre>
        `;
    }

    monitorFormChanges() {
        const form = document.querySelector('form');
        if (!form) return;

        // Monitor all form inputs
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.logFormState();
            });
        });

        // Monitor form submission
        form.addEventListener('submit', (e) => {
            console.log('🚀 Form submission intercepted');
            this.logFormState();
        });
    }

    logFormState() {
        const formData = this.getCompleteFormData();
        console.log('📋 Current Form State:', formData);
        
        if (this.isDebugMode) {
            this.updateDebugPanel(formData);
        }
    }

    getCompleteFormData() {
        return {
            // Basic attendee info
            attendee_name: this.getFieldValue('attendee_name'),
            attendee_email: this.getFieldValue('attendee_email'),
            attendee_phone: this.getFieldValue('attendee_phone'),
            attendee_gender: this.getFieldValue('attendee_gender'),
            attendee_birth_date: this.getFieldValue('attendee_birth_date'),
            attendee_occupation: this.getFieldValue('attendee_occupation'),
            
            // Identity info
            identity_type: this.getFieldValue('identity_type'),
            identity_number: this.getFieldValue('identity_number'),
            
            // Emergency contact
            emergency_contact_name: this.getFieldValue('emergency_contact_name'),
            emergency_contact_phone: this.getFieldValue('emergency_contact_phone'),
            
            // Payment and terms
            payment_method: this.getRadioValue('payment_method'),
            terms_accepted: this.getCheckboxValue('terms_accepted'),
            
            // Additional info
            quantity: this.getFieldValue('quantity'),
            
            // Validation status
            validation: this.getValidationStatus()
        };
    }

    getFieldValue(name) {
        const element = document.querySelector(`[name="${name}"]`);
        return element ? element.value.trim() : null;
    }

    getRadioValue(name) {
        const element = document.querySelector(`input[name="${name}"]:checked`);
        return element ? element.value : null;
    }

    getCheckboxValue(name) {
        const element = document.querySelector(`input[name="${name}"]`);
        return element ? element.checked : false;
    }

    getValidationStatus() {
        const requiredFields = [
            'attendee_name',
            'attendee_email', 
            'attendee_phone',
            'attendee_gender',
            'attendee_birth_date',
            'identity_type',
            'identity_number',
            'payment_method',
            'terms_accepted'
        ];

        const status = {};
        requiredFields.forEach(field => {
            let value;
            if (field === 'payment_method') {
                value = this.getRadioValue(field);
            } else if (field === 'terms_accepted') {
                value = this.getCheckboxValue(field);
            } else {
                value = this.getFieldValue(field);
            }
            
            status[field] = {
                value: value,
                isEmpty: !value || value === '' || (field === 'terms_accepted' && !value),
                element: document.querySelector(`[name="${field}"]`) ? 'found' : 'missing'
            };
        });

        return status;
    }

    // Test functions
    fillTestData() {
        console.log('🧪 Filling test data...');
        
        this.setFieldValue('attendee_name', 'John Doe Test');
        this.setFieldValue('attendee_email', '<EMAIL>');
        this.setFieldValue('attendee_phone', '081234567890');
        this.setFieldValue('attendee_gender', 'male');
        this.setFieldValue('attendee_birth_date', '1990-01-01');
        this.setFieldValue('attendee_occupation', 'Software Engineer');
        this.setFieldValue('identity_type', 'ktp');
        this.setFieldValue('identity_number', '****************');
        this.setFieldValue('emergency_contact_name', 'Jane Doe');
        this.setFieldValue('emergency_contact_phone', '************');
        
        // Set payment method
        const paymentRadio = document.querySelector('input[name="payment_method"][value="bank_transfer"]');
        if (paymentRadio) {
            paymentRadio.checked = true;
            paymentRadio.dispatchEvent(new Event('change'));
        }
        
        // Set terms checkbox
        const termsCheckbox = document.querySelector('input[name="terms_accepted"]');
        if (termsCheckbox) {
            termsCheckbox.checked = true;
            termsCheckbox.dispatchEvent(new Event('change'));
        }
        
        console.log('✅ Test data filled');
        this.logFormState();
    }

    setFieldValue(name, value) {
        const element = document.querySelector(`[name="${name}"]`);
        if (element) {
            element.value = value;
            element.dispatchEvent(new Event('change'));
        }
    }

    validateForm() {
        console.log('🔍 Running form validation test...');
        
        const alpineComponent = Alpine.$data(document.querySelector('[x-data]'));
        if (alpineComponent && alpineComponent.validateForm) {
            const isValid = alpineComponent.validateForm();
            console.log('Validation result:', isValid);
            return isValid;
        } else {
            console.error('Alpine component or validateForm method not found');
            return false;
        }
    }

    clearForm() {
        console.log('🧹 Clearing form...');
        
        const form = document.querySelector('form');
        if (form) {
            form.reset();
            
            // Clear Alpine.js data
            const alpineComponent = Alpine.$data(document.querySelector('[x-data]'));
            if (alpineComponent) {
                alpineComponent.selectedPaymentMethod = '';
                alpineComponent.identityType = '';
            }
        }
        
        this.logFormState();
    }
}

// Global debug functions
window.formDebugger = new FormDebugger();

// Add global debug functions
window.debugForm = () => window.formDebugger.logFormState();
window.fillTestData = () => window.formDebugger.fillTestData();
window.validateForm = () => window.formDebugger.validateForm();
window.clearForm = () => window.formDebugger.clearForm();

// Add debug info to console
if (window.formDebugger.isDebugMode) {
    console.log(`
🐛 Form Debug Mode Active!

Available commands:
- debugForm()     : Log current form state
- fillTestData()  : Fill form with test data
- validateForm()  : Test form validation
- clearForm()     : Clear all form data

Add ?debug=true to URL to enable debug panel.
    `);
}
