@extends('layouts.app')

@section('title', 'Riwayat Transaksi UangTix')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-history text-info me-2"></i>
                Riwayat Transaksi UangTix
            </h1>
            <p class="text-muted">Lihat semua transaksi UangTix Anda</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('uangtix.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="{{ route('uangtix.balance') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-line"></i> Detail <PERSON>
            </a>
        </div>
    </div>

    <!-- Balance Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white shadow">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white mb-1">{{ $balance->formatted_balance }}</h4>
                            <p class="text-white-75 mb-0">Saldo UangTix Saat Ini</p>
                            <small class="text-white-50">{{ $balance->formatted_balance_idr }}</small>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-wallet fa-3x text-white-25"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                Filter Transaksi
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('uangtix.transactions') }}" class="row">
                <div class="col-md-3 mb-3">
                    <label for="search" class="form-label">Pencarian</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="No. transaksi atau deskripsi...">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="type" class="form-label">Jenis Transaksi</label>
                    <select class="form-control" id="type" name="type">
                        <option value="">Semua Jenis</option>
                        @foreach($transactionTypes as $key => $label)
                            <option value="{{ $key }}" {{ request('type') == $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="date_from" class="form-label">Dari Tanggal</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="date_to" class="form-label">Sampai Tanggal</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ route('uangtix.transactions') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>



    <!-- Transactions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Transaksi</h6>
        </div>
        <div class="card-body">
            @if($transactions->count() > 0)
                <!-- Desktop View -->
                <div class="d-none d-md-block">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Jenis</th>
                                    <th>Deskripsi</th>
                                    <th>Jumlah</th>
                                    <th>Saldo Setelah</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($transactions as $transaction)
                                <tr>
                                    <td>
                                        <div>{{ $transaction->created_at->format('d/m/Y') }}</div>
                                        <div class="text-muted small">{{ $transaction->created_at->format('H:i:s') }}</div>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $transaction->type_color }}">
                                            <i class="{{ $transaction->type_icon }} me-1"></i>
                                            {{ $transaction->type_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>{{ $transaction->description }}</div>
                                        @if($transaction->fromUser)
                                            <div class="text-muted small">
                                                Dari: {{ $transaction->fromUser->name }}
                                            </div>
                                        @endif
                                        @if($transaction->toUser)
                                            <div class="text-muted small">
                                                Ke: {{ $transaction->toUser->name }}
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="font-weight-bold text-{{ $transaction->amount > 0 ? 'success' : 'danger' }}">
                                            {{ $transaction->formatted_amount }}
                                        </span>
                                        <div class="text-muted small">
                                            {{ $transaction->formatted_amount_idr }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="font-weight-bold">
                                            UTX {{ number_format($transaction->balance_after, 0, ',', '.') }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $transaction->status_color }}">
                                            {{ $transaction->status_label }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Mobile View -->
                <div class="d-md-none">
                    @foreach($transactions as $transaction)
                    <div class="card mb-3 border-left-{{ $transaction->type_color }}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <span class="badge badge-{{ $transaction->type_color }}">
                                        <i class="{{ $transaction->type_icon }} me-1"></i>
                                        {{ $transaction->type_label }}
                                    </span>
                                </div>
                                <div class="text-end">
                                    <div class="font-weight-bold text-{{ $transaction->amount > 0 ? 'success' : 'danger' }}">
                                        {{ $transaction->formatted_amount }}
                                    </div>
                                    <div class="text-muted small">
                                        {{ $transaction->formatted_amount_idr }}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-2">
                                <div class="font-weight-bold">{{ $transaction->description }}</div>
                                @if($transaction->fromUser)
                                    <div class="text-muted small">
                                        Dari: {{ $transaction->fromUser->name }}
                                    </div>
                                @endif
                                @if($transaction->toUser)
                                    <div class="text-muted small">
                                        Ke: {{ $transaction->toUser->name }}
                                    </div>
                                @endif
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="text-muted small">{{ $transaction->created_at->format('d/m/Y H:i:s') }}</div>
                                    <div class="text-muted small">
                                        Saldo: UTX {{ number_format($transaction->balance_after, 0, ',', '.') }}
                                    </div>
                                </div>
                                <span class="badge badge-{{ $transaction->status_color }}">
                                    {{ $transaction->status_label }}
                                </span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($transactions->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $transactions->appends(request()->query())->links() }}
                    </div>
                @endif
            @else
                <div class="text-center py-5">
                    <i class="fas fa-history fa-4x text-gray-300 mb-3"></i>
                    <h5 class="text-muted">Belum Ada Transaksi</h5>
                    <p class="text-muted">Transaksi UangTix Anda akan muncul di sini</p>
                    <a href="{{ route('uangtix.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Mulai Transaksi
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-set date_to when date_from is selected
document.getElementById('date_from').addEventListener('change', function() {
    const dateFrom = this.value;
    const dateTo = document.getElementById('date_to');

    if (dateFrom && !dateTo.value) {
        dateTo.value = dateFrom;
    }
});

// Set max date to today
const today = new Date().toISOString().split('T')[0];
document.getElementById('date_from').setAttribute('max', today);
document.getElementById('date_to').setAttribute('max', today);
</script>
@endpush
