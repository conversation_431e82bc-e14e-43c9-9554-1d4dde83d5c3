<?php $__env->startSection('title', 'System Settings'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Custom styles for System Settings */
.settings-card {
    transition: all 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.settings-section {
    scroll-margin-top: 100px;
}

.nav-tab {
    transition: all 0.2s ease;
}

.nav-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .cache-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Progress bars */
.progress-bar {
    transition: width 0.3s ease;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="settings" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">System Settings</h1>
                            <p class="text-gray-600 dark:text-gray-400">Manage application configuration and system maintenance</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <form action="<?php echo e(route('admin.settings.optimize')); ?>" method="POST" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-sm"
                                onclick="return confirm('This will optimize the application. Continue?')">
                            <i data-lucide="zap" class="w-4 h-4 mr-2"></i>
                            Optimize App
                        </button>
                    </form>
                    <button onclick="backupSettings()"
                            class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        Backup Settings
                    </button>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-2">
                <nav class="flex space-x-2" x-data="{ activeTab: 'app' }">
                    <button @click="activeTab = 'app'"
                            :class="activeTab === 'app' ? 'nav-tab active' : 'nav-tab'"
                            class="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="settings" class="w-4 h-4 inline mr-2"></i>
                        Application
                    </button>
                    <button @click="activeTab = 'mail'"
                            :class="activeTab === 'mail' ? 'nav-tab active' : 'nav-tab'"
                            class="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                        Mail
                    </button>
                    <button @click="activeTab = 'seo'"
                            :class="activeTab === 'seo' ? 'nav-tab active' : 'nav-tab'"
                            class="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                        SEO
                    </button>
                    <button @click="activeTab = 'cache'"
                            :class="activeTab === 'cache' ? 'nav-tab active' : 'nav-tab'"
                            class="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="database" class="w-4 h-4 inline mr-2"></i>
                        Cache
                    </button>
                    <button @click="activeTab = 'storage'"
                            :class="activeTab === 'storage' ? 'nav-tab active' : 'nav-tab'"
                            class="flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200">
                        <i data-lucide="hard-drive" class="w-4 h-4 inline mr-2"></i>
                        Storage
                    </button>
                </nav>
            </div>
        </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Settings -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Application Settings -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Application Settings</h2>
                    
                    <form action="<?php echo e(route('admin.settings.update-app')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="app_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Application Name</label>
                                <input type="text" 
                                       id="app_name" 
                                       name="app_name" 
                                       value="<?php echo e($settings['app_name']); ?>"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="app_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Application URL</label>
                                <input type="url" 
                                       id="app_url" 
                                       name="app_url" 
                                       value="<?php echo e($settings['app_url']); ?>"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="app_timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Timezone</label>
                                <select id="app_timezone" 
                                        name="app_timezone"
                                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="Asia/Jakarta" <?php echo e($settings['app_timezone'] == 'Asia/Jakarta' ? 'selected' : ''); ?>>Asia/Jakarta (WIB)</option>
                                    <option value="Asia/Makassar" <?php echo e($settings['app_timezone'] == 'Asia/Makassar' ? 'selected' : ''); ?>>Asia/Makassar (WITA)</option>
                                    <option value="Asia/Jayapura" <?php echo e($settings['app_timezone'] == 'Asia/Jayapura' ? 'selected' : ''); ?>>Asia/Jayapura (WIT)</option>
                                    <option value="UTC" <?php echo e($settings['app_timezone'] == 'UTC' ? 'selected' : ''); ?>>UTC</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                                <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                                Save Application Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Mail Settings -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Mail Settings</h2>
                    
                    <form action="<?php echo e(route('admin.settings.update-mail')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="mail_driver" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Mail Driver</label>
                                <select id="mail_driver" 
                                        name="mail_driver"
                                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="smtp" <?php echo e($settings['mail_driver'] == 'smtp' ? 'selected' : ''); ?>>SMTP</option>
                                    <option value="sendmail" <?php echo e($settings['mail_driver'] == 'sendmail' ? 'selected' : ''); ?>>Sendmail</option>
                                    <option value="mailgun" <?php echo e($settings['mail_driver'] == 'mailgun' ? 'selected' : ''); ?>>Mailgun</option>
                                    <option value="ses" <?php echo e($settings['mail_driver'] == 'ses' ? 'selected' : ''); ?>>Amazon SES</option>
                                    <option value="log" <?php echo e($settings['mail_driver'] == 'log' ? 'selected' : ''); ?>>Log (Testing)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="mail_from_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Address</label>
                                <input type="email" 
                                       id="mail_from_address" 
                                       name="mail_from_address" 
                                       value="<?php echo e($settings['mail_from_address']); ?>"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_from_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Name</label>
                                <input type="text" 
                                       id="mail_from_name" 
                                       name="mail_from_name" 
                                       value="<?php echo e($settings['mail_from_name']); ?>"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_host" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Host</label>
                                <input type="text" 
                                       id="mail_host" 
                                       name="mail_host" 
                                       placeholder="smtp.gmail.com"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Port</label>
                                <input type="number" 
                                       id="mail_port" 
                                       name="mail_port" 
                                       placeholder="587"
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Username</label>
                                <input type="text" 
                                       id="mail_username" 
                                       name="mail_username" 
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">SMTP Password</label>
                                <input type="password" 
                                       id="mail_password" 
                                       name="mail_password" 
                                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div>
                                <label for="mail_encryption" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Encryption</label>
                                <select id="mail_encryption" 
                                        name="mail_encryption"
                                        class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    <option value="">None</option>
                                    <option value="tls">TLS</option>
                                    <option value="ssl">SSL</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                                <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                                Save Mail Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- SEO Settings -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">SEO Settings</h2>

                    <form action="<?php echo e(route('admin.settings.update-seo')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="space-y-6">
                            <!-- Basic SEO -->
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label for="seo_site_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Site Title</label>
                                    <input type="text"
                                           id="seo_site_title"
                                           name="seo_site_title"
                                           value="<?php echo e($platformSettings['seo']['seo_site_title'] ?? 'TiXara - Platform Tiket Event Terpercaya Indonesia'); ?>"
                                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                           placeholder="Default site title for SEO">
                                </div>

                                <div>
                                    <label for="seo_site_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Site Description</label>
                                    <textarea id="seo_site_description"
                                              name="seo_site_description"
                                              rows="3"
                                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                              placeholder="Default site description for SEO"><?php echo e($platformSettings['seo']['seo_site_description'] ?? 'Temukan dan beli tiket event favorit Anda dengan mudah dan aman di TiXara.'); ?></textarea>
                                </div>

                                <div>
                                    <label for="seo_site_keywords" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Site Keywords</label>
                                    <input type="text"
                                           id="seo_site_keywords"
                                           name="seo_site_keywords"
                                           value="<?php echo e($platformSettings['seo']['seo_site_keywords'] ?? 'tiket event, konser, festival, seminar, workshop, acara, entertainment, Indonesia'); ?>"
                                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                           placeholder="Comma-separated keywords">
                                </div>
                            </div>

                            <!-- Social Media -->
                            <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                                <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">Social Media & Open Graph</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="seo_og_image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Open Graph Image URL</label>
                                        <input type="text"
                                               id="seo_og_image"
                                               name="seo_og_image"
                                               value="<?php echo e($platformSettings['seo']['seo_og_image'] ?? '/images/og-image.jpg'); ?>"
                                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                               placeholder="/images/og-image.jpg">
                                    </div>

                                    <div>
                                        <label for="seo_twitter_handle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Twitter Handle</label>
                                        <input type="text"
                                               id="seo_twitter_handle"
                                               name="seo_twitter_handle"
                                               value="<?php echo e($platformSettings['seo']['seo_twitter_handle'] ?? '@tixara_id'); ?>"
                                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                               placeholder="@tixara_id">
                                    </div>
                                </div>
                            </div>

                            <!-- Analytics & Tracking -->
                            <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                                <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">Analytics & Tracking</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="seo_google_analytics_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Google Analytics ID</label>
                                        <input type="text"
                                               id="seo_google_analytics_id"
                                               name="seo_google_analytics_id"
                                               value="<?php echo e($platformSettings['seo']['seo_google_analytics_id'] ?? ''); ?>"
                                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                               placeholder="G-XXXXXXXXXX">
                                    </div>

                                    <div>
                                        <label for="seo_google_tag_manager_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Google Tag Manager ID</label>
                                        <input type="text"
                                               id="seo_google_tag_manager_id"
                                               name="seo_google_tag_manager_id"
                                               value="<?php echo e($platformSettings['seo']['seo_google_tag_manager_id'] ?? ''); ?>"
                                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                               placeholder="GTM-XXXXXXX">
                                    </div>

                                    <div>
                                        <label for="seo_facebook_pixel_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Facebook Pixel ID</label>
                                        <input type="text"
                                               id="seo_facebook_pixel_id"
                                               name="seo_facebook_pixel_id"
                                               value="<?php echo e($platformSettings['seo']['seo_facebook_pixel_id'] ?? ''); ?>"
                                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                               placeholder="123456789012345">
                                    </div>
                                </div>
                            </div>

                            <!-- Site Verification -->
                            <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                                <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">Site Verification</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="seo_google_site_verification" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Google Search Console</label>
                                        <input type="text"
                                               id="seo_google_site_verification"
                                               name="seo_google_site_verification"
                                               value="<?php echo e($platformSettings['seo']['seo_google_site_verification'] ?? ''); ?>"
                                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                               placeholder="Verification meta tag content">
                                    </div>

                                    <div>
                                        <label for="seo_bing_site_verification" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bing Webmaster</label>
                                        <input type="text"
                                               id="seo_bing_site_verification"
                                               name="seo_bing_site_verification"
                                               value="<?php echo e($platformSettings['seo']['seo_bing_site_verification'] ?? ''); ?>"
                                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                               placeholder="Verification meta tag content">
                                    </div>
                                </div>
                            </div>

                            <!-- Advanced Settings -->
                            <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                                <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">Advanced Settings</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label for="seo_robots_txt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Robots.txt Content</label>
                                        <textarea id="seo_robots_txt"
                                                  name="seo_robots_txt"
                                                  rows="6"
                                                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white font-mono text-sm"
                                                  placeholder="User-agent: *&#10;Disallow: /admin/&#10;Sitemap: <?php echo e(url('/sitemap.xml')); ?>"><?php echo e($platformSettings['seo']['seo_robots_txt'] ?? "User-agent: *\nDisallow: /admin/\nDisallow: /api/\nSitemap: " . url('/sitemap.xml')); ?></textarea>
                                    </div>

                                    <div class="flex items-center">
                                        <input type="checkbox"
                                               id="seo_enable_structured_data"
                                               name="seo_enable_structured_data"
                                               value="1"
                                               <?php echo e(($platformSettings['seo']['seo_enable_structured_data'] ?? true) ? 'checked' : ''); ?>

                                               class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary dark:focus:ring-primary dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="seo_enable_structured_data" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Enable Structured Data (JSON-LD)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end">
                            <button type="submit"
                                    class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                                <i data-lucide="search" class="w-4 h-4 inline mr-2"></i>
                                Save SEO Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Cache Management -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Cache Management</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <form action="<?php echo e(route('admin.settings.clear-cache')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="config">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center">
                                <i data-lucide="settings" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear Config</div>
                                <div class="text-xs opacity-75"><?php echo e($cacheInfo['config_cached'] ? 'Cached' : 'Not Cached'); ?></div>
                            </button>
                        </form>
                        
                        <form action="<?php echo e(route('admin.settings.clear-cache')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="route">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-center">
                                <i data-lucide="route" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear Routes</div>
                                <div class="text-xs opacity-75"><?php echo e($cacheInfo['routes_cached'] ? 'Cached' : 'Not Cached'); ?></div>
                            </button>
                        </form>
                        
                        <form action="<?php echo e(route('admin.settings.clear-cache')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="view">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 text-center">
                                <i data-lucide="eye" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear Views</div>
                                <div class="text-xs opacity-75"><?php echo e($cacheInfo['views_cached'] ? 'Cached' : 'Not Cached'); ?></div>
                            </button>
                        </form>
                        
                        <form action="<?php echo e(route('admin.settings.clear-cache')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="all">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 text-center">
                                <i data-lucide="trash-2" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clear All</div>
                                <div class="text-xs opacity-75">All Caches</div>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Storage Management -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Storage Management</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <form action="<?php echo e(route('admin.settings.clean-storage')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="logs">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete all log files. Continue?')">
                                <i data-lucide="file-text" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Logs</div>
                                <div class="text-xs opacity-75"><?php echo e($storageInfo['logs_size']); ?></div>
                            </button>
                        </form>
                        
                        <form action="<?php echo e(route('admin.settings.clean-storage')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="cache">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete cache files. Continue?')">
                                <i data-lucide="database" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Cache</div>
                                <div class="text-xs opacity-75"><?php echo e($storageInfo['cache_size']); ?></div>
                            </button>
                        </form>
                        
                        <form action="<?php echo e(route('admin.settings.clean-storage')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="sessions">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete session files. Continue?')">
                                <i data-lucide="users" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Sessions</div>
                                <div class="text-xs opacity-75"><?php echo e($storageInfo['sessions_size']); ?></div>
                            </button>
                        </form>
                        
                        <form action="<?php echo e(route('admin.settings.clean-storage')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="type" value="temp">
                            <button type="submit" 
                                    class="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 text-center"
                                    onclick="return confirm('This will delete temporary files. Continue?')">
                                <i data-lucide="folder-x" class="w-5 h-5 mx-auto mb-1"></i>
                                <div class="text-sm font-medium">Clean Temp</div>
                                <div class="text-xs opacity-75">Temp Files</div>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- System Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Information</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">PHP Version</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($systemInfo['php_version']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Laravel Version</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($systemInfo['laravel_version']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Environment</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e(ucfirst($systemInfo['environment'])); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Debug Mode</span>
                            <span class="text-sm font-medium <?php echo e($systemInfo['debug_mode'] ? 'text-red-600' : 'text-green-600'); ?>">
                                <?php echo e($systemInfo['debug_mode'] ? 'Enabled' : 'Disabled'); ?>

                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Database</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e(ucfirst($systemInfo['database_connection'])); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Storage</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e(ucfirst($systemInfo['storage_disk'])); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Storage Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Storage Information</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Space</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($storageInfo['total_space']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Free Space</span>
                            <span class="text-sm font-medium text-green-600"><?php echo e($storageInfo['free_space']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Used Space</span>
                            <span class="text-sm font-medium text-orange-600"><?php echo e($storageInfo['used_space']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <form action="<?php echo e(route('admin.settings.clear-cache')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <button type="submit" 
                                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                                Clear All Cache
                            </button>
                        </form>
                        
                        <form action="<?php echo e(route('admin.settings.optimize')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <button type="submit" 
                                    class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                    onclick="return confirm('This will optimize the application. Continue?')">
                                <i data-lucide="zap" class="w-4 h-4 inline mr-2"></i>
                                Optimize Application
                            </button>
                        </form>
                        
                        <a href="<?php echo e(route('admin.dashboard')); ?>" 
                           class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors inline-block text-center">
                            <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/settings/index.blade.php ENDPATH**/ ?>