{{--
/**
 * Organizer Registration Page
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: <PERSON><PERSON><PERSON>zu<PERSON> P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */
--}}

@extends('layouts.app')

@section('title', 'Daftar Sebagai Organizer - TiXara')

@push('styles')
<style>
/* Organizer Register Styles */
.organizer-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.organizer-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="organizer" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23organizer)"/></svg>');
    opacity: 0.3;
}

.register-card {
    background: white;
    border-radius: 24px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.register-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 18px 32px;
    border: none;
    border-radius: 16px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
}

.benefit-card {
    background: white;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.benefit-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.benefit-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.benefit-icon.revenue {
    background: linear-gradient(135deg, #10b981, #059669);
}

.benefit-icon.marketing {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.benefit-icon.analytics {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.benefit-icon.support {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

@media (max-width: 768px) {
    .register-card {
        padding: 24px;
        margin: 16px;
    }
    
    .benefit-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
</style>
@endpush

@section('content')
<!-- Hero Section -->
<div class="organizer-hero text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 backdrop-blur-sm">
                <i data-lucide="users" class="w-10 h-10 text-white"></i>
            </div>
            <h1 class="text-5xl md:text-6xl font-bold mb-6">
                Daftar Sebagai Organizer
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                Mulai perjalanan Anda sebagai event organizer profesional
            </p>
            <p class="text-lg mb-12 max-w-2xl mx-auto opacity-80">
                Bergabunglah dengan 500+ organizer yang telah mempercayai TiXara untuk mengelola event mereka.
            </p>
        </div>
    </div>
</div>

<!-- Benefits Section -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Mengapa Memilih TiXara?
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Platform terlengkap untuk mengelola event Anda dari A sampai Z
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 benefit-grid">
            <!-- Revenue -->
            <div class="benefit-card text-center">
                <div class="benefit-icon revenue mx-auto">
                    <i data-lucide="trending-up" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Maksimalkan Revenue</h3>
                <p class="text-gray-600">Fee rendah hanya 5% + payment gateway. Terima pembayaran dari berbagai metode.</p>
            </div>

            <!-- Marketing -->
            <div class="benefit-card text-center">
                <div class="benefit-icon marketing mx-auto">
                    <i data-lucide="megaphone" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Marketing Tools</h3>
                <p class="text-gray-600">ArtPosure marketing suite untuk promosi di social media dan website placement.</p>
            </div>

            <!-- Analytics -->
            <div class="benefit-card text-center">
                <div class="benefit-icon analytics mx-auto">
                    <i data-lucide="bar-chart-3" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Analytics Mendalam</h3>
                <p class="text-gray-600">Dashboard analytics real-time untuk tracking penjualan dan performa event.</p>
            </div>

            <!-- Support -->
            <div class="benefit-card text-center">
                <div class="benefit-icon support mx-auto">
                    <i data-lucide="headphones" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Support 24/7</h3>
                <p class="text-gray-600">Tim support dedicated siap membantu Anda kapan saja melalui berbagai channel.</p>
            </div>
        </div>
    </div>
</div>

<!-- Registration Form -->
<div class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="register-card">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Mulai Sekarang
                </h2>
                <p class="text-gray-600 dark:text-gray-300">
                    Isi form di bawah untuk mendaftar sebagai organizer
                </p>
            </div>

            @if ($errors->any())
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                    <div class="flex items-center mb-2">
                        <i data-lucide="alert-circle" class="w-5 h-5 text-red-600 mr-2"></i>
                        <h4 class="text-red-800 font-semibold">Terjadi Kesalahan</h4>
                    </div>
                    <ul class="text-red-700 text-sm space-y-1">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('organizer.register') }}" method="POST" class="space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label for="name" class="form-label">Nama Lengkap *</label>
                        <input type="text" id="name" name="name" class="form-input" value="{{ old('name') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" id="email" name="email" class="form-input" value="{{ old('email') }}" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="phone" class="form-label">Nomor Telepon *</label>
                    <input type="tel" id="phone" name="phone" class="form-input" value="{{ old('phone') }}" required>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label for="password" class="form-label">Password *</label>
                        <input type="password" id="password" name="password" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="password_confirmation" class="form-label">Konfirmasi Password *</label>
                        <input type="password" id="password_confirmation" name="password_confirmation" class="form-input" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="company_name" class="form-label">Nama Perusahaan/Organisasi</label>
                    <input type="text" id="company_name" name="company_name" class="form-input" value="{{ old('company_name') }}">
                </div>
                
                <div class="form-group">
                    <label for="company_address" class="form-label">Alamat Perusahaan/Organisasi</label>
                    <textarea id="company_address" name="company_address" class="form-input form-textarea" rows="3">{{ old('company_address') }}</textarea>
                </div>
                
                <div class="form-group">
                    <label class="flex items-start space-x-3">
                        <input type="checkbox" name="terms" class="mt-1 w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500" required>
                        <span class="text-sm text-gray-600 dark:text-gray-300">
                            Saya menyetujui <a href="#" class="text-blue-600 hover:text-blue-700 underline">Syarat dan Ketentuan</a> 
                            serta <a href="#" class="text-blue-600 hover:text-blue-700 underline">Kebijakan Privasi</a> TiXara
                        </span>
                    </label>
                </div>
                
                <button type="submit" class="submit-btn">
                    <i data-lucide="user-plus" class="w-6 h-6 inline mr-2"></i>
                    Daftar Sebagai Organizer
                </button>
            </form>

            <div class="mt-8 text-center">
                <p class="text-gray-600 dark:text-gray-300">
                    Sudah punya akun? 
                    <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-700 font-semibold">Masuk di sini</a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-8">
            Fitur Lengkap untuk Organizer
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="calendar" class="w-8 h-8 text-blue-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Event Management</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">Kelola event dengan mudah dari dashboard terpusat</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="credit-card" class="w-8 h-8 text-green-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Payment Gateway</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">Terima pembayaran dari berbagai metode</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="qr-code" class="w-8 h-8 text-purple-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">QR Code Tickets</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">Sistem tiket digital dengan QR code</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="users" class="w-8 h-8 text-yellow-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Attendee Management</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">Kelola peserta dan check-in dengan mudah</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="share-2" class="w-8 h-8 text-red-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Social Sharing</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">Promosi otomatis di social media</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="shield-check" class="w-8 h-8 text-indigo-600"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Secure & Reliable</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm">Platform aman dengan uptime 99.9%</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
/*
 * Organizer Register JavaScript
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('password_confirmation').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Password dan konfirmasi password tidak cocok.');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('Password minimal 6 karakter.');
                return false;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('.submit-btn');
            submitBtn.innerHTML = '<i data-lucide="loader" class="w-6 h-6 inline mr-2 animate-spin"></i>Mendaftar...';
            submitBtn.disabled = true;
        });
    }
    
    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.startsWith('0')) {
                value = '62' + value.substring(1);
            } else if (!value.startsWith('62')) {
                value = '62' + value;
            }
            e.target.value = '+' + value;
        });
    }
});
</script>
@endpush
