@echo off
echo TiXara Database Master Fix Tool
echo ================================

echo.
echo This tool will diagnose and fix database structure issues.
echo.
echo Available options:
echo 1. Diagnose current database state
echo 2. Complete database structure fix (recommended)
echo 3. Create missing events table only
echo 4. Fix tickets table conflict only
echo 5. Fresh migration (deletes all data)
echo 6. Exit
echo.

set /p choice="Select option (1-6): "

if "%choice%"=="1" goto diagnose
if "%choice%"=="2" goto complete_fix
if "%choice%"=="3" goto events_only
if "%choice%"=="4" goto tickets_only
if "%choice%"=="5" goto fresh_migration
if "%choice%"=="6" goto exit

echo Invalid choice. Please select 1-6.
pause
goto start

:diagnose
echo.
echo Running database diagnosis...
call diagnose-database-structure.bat
goto end

:complete_fix
echo.
echo WARNING: This will fix all database structure issues.
echo This may modify existing tables and data.
echo.
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto start

echo.
echo Running complete database structure fix...
call fix-database-structure-complete.bat
goto end

:events_only
echo.
echo Creating missing events table only...
call create-missing-events-table.bat
goto end

:tickets_only
echo.
echo Fixing tickets table conflict...
call fix-tickets-table-conflict.bat
goto end

:fresh_migration
echo.
echo WARNING: This will delete ALL existing data!
echo This will completely reset the database.
echo.
set /p confirm="Are you absolutely sure? Type 'DELETE ALL DATA' to confirm: "
if not "%confirm%"=="DELETE ALL DATA" (
    echo Cancelled.
    goto start
)

echo.
echo Running fresh migration...
call fresh-migrate.bat
goto end

:exit
echo Exiting...
goto end

:end
echo.
echo ========================================
echo Database Fix Tool Completed
echo ========================================
echo.
echo Common next steps:
echo - Run: php artisan db:seed (to populate sample data)
echo - Test: php artisan serve (to start development server)
echo - Access: http://localhost:8000
echo.
echo If you still encounter issues:
echo 1. Check the output above for any errors
echo 2. Run diagnose-database-structure.bat for detailed analysis
echo 3. Contact support with the error details
echo.
pause

:start
