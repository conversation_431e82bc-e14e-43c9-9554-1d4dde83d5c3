<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class DiagnoseLogin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:diagnose {email?} {--fix}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose and fix login issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $fix = $this->option('fix');

        $this->info('🔍 Diagnosing Login Issues...');
        $this->newLine();

        // Check database connection
        try {
            \DB::connection()->getPdo();
            $this->info('✅ Database connection: OK');
        } catch (\Exception $e) {
            $this->error('❌ Database connection failed: ' . $e->getMessage());
            return 1;
        }

        // Check users table
        try {
            $userCount = User::count();
            $this->info("✅ Users table accessible. Total users: {$userCount}");
        } catch (\Exception $e) {
            $this->error('❌ Users table error: ' . $e->getMessage());
            return 1;
        }

        if ($userCount === 0) {
            $this->warn('⚠️  No users found in database!');
            if ($fix) {
                $this->info('🔧 Running seeders...');
                $this->call('db:seed', ['--class' => 'UserSeeder']);
                $this->call('db:seed', ['--class' => 'TiXaraSeeder']);
            } else {
                $this->info('💡 Run with --fix to create default users');
            }
            return 0;
        }

        // List all users
        $this->info('📋 Current Users:');
        $this->table(
            ['ID', 'Name', 'Email', 'Role', 'Active', 'Verified'],
            User::all()->map(function ($user) {
                return [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->role,
                    $user->is_active ? '✅' : '❌',
                    $user->email_verified_at ? '✅' : '❌'
                ];
            })
        );

        // Test specific user if provided
        if ($email) {
            $this->newLine();
            $this->info("🔍 Testing user: {$email}");
            
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                $this->error("❌ User not found: {$email}");
                return 1;
            }

            $this->info("✅ User found: {$user->name}");
            $this->info("📧 Email: {$user->email}");
            $this->info("👤 Role: {$user->role}");
            $this->info("🔓 Active: " . ($user->is_active ? 'Yes' : 'No'));
            $this->info("✉️  Verified: " . ($user->email_verified_at ? 'Yes' : 'No'));

            // Test password
            $this->newLine();
            $this->info('🔐 Testing passwords...');
            
            $testPasswords = [
                'TiXara@2024',
                'Staff@2024', 
                'Penjual@2024',
                'Pembeli@2024',
                'admin123',
                'staff123',
                'penjual123',
                'pembeli123',
                'password'
            ];

            $correctPassword = null;
            foreach ($testPasswords as $password) {
                if (Hash::check($password, $user->password)) {
                    $correctPassword = $password;
                    break;
                }
            }

            if ($correctPassword) {
                $this->info("✅ Password found: {$correctPassword}");
            } else {
                $this->error("❌ None of the test passwords work!");
                
                if ($fix) {
                    $newPassword = $this->getPasswordForRole($user->role);
                    $user->update(['password' => Hash::make($newPassword)]);
                    $this->info("🔧 Password reset to: {$newPassword}");
                }
            }

            // Fix user issues if requested
            if ($fix) {
                $this->newLine();
                $this->info('🔧 Fixing user issues...');
                
                $updates = [];
                
                if (!$user->is_active) {
                    $updates['is_active'] = true;
                    $this->info('✅ Activated user');
                }
                
                if (!$user->email_verified_at) {
                    $updates['email_verified_at'] = now();
                    $this->info('✅ Verified email');
                }
                
                if (!empty($updates)) {
                    $user->update($updates);
                    $this->info('💾 User updated successfully');
                } else {
                    $this->info('✅ User is already in good state');
                }
            }
        }

        // Test authentication system
        $this->newLine();
        $this->info('🔐 Testing Authentication System...');
        
        try {
            $testUser = User::where('is_active', true)->first();
            if ($testUser) {
                $password = $this->getPasswordForRole($testUser->role);
                
                if (\Auth::attempt(['email' => $testUser->email, 'password' => $password])) {
                    $this->info('✅ Authentication system working');
                    \Auth::logout();
                } else {
                    $this->error('❌ Authentication failed');
                    
                    if ($fix) {
                        $testUser->update(['password' => Hash::make($password)]);
                        $this->info("🔧 Reset password for {$testUser->email} to {$password}");
                    }
                }
            }
        } catch (\Exception $e) {
            $this->error('❌ Authentication test failed: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('🎉 Diagnosis complete!');
        
        if (!$fix) {
            $this->info('💡 Run with --fix to automatically fix issues');
        }

        return 0;
    }

    private function getPasswordForRole($role)
    {
        return match($role) {
            'admin' => 'TiXara@2024',
            'staff' => 'Staff@2024',
            'penjual' => 'Penjual@2024',
            'pembeli' => 'Pembeli@2024',
            default => 'TiXara@2024'
        };
    }
}
