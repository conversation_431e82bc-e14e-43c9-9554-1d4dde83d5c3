<?php $__env->startSection('title', 'Manajemen UangTix'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="coins" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Manajemen UangTix</h1>
                            <p class="text-gray-600 dark:text-gray-400">Kelola sistem mata uang digital platform</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="<?php echo e(route('admin.uangtix.settings')); ?>"
                       class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 shadow-sm">
                        <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                        Pengaturan
                    </a>

                    <a href="<?php echo e(route('admin.uangtix.requests')); ?>"
                       class="inline-flex items-center px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors duration-200 shadow-sm relative">
                        <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                        Permintaan
                        <?php if($stats['pending_deposits'] + $stats['pending_withdrawals'] > 0): ?>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-medium">
                                <?php echo e($stats['pending_deposits'] + $stats['pending_withdrawals']); ?>

                            </span>
                        <?php endif; ?>
                    </a>

                    <!-- Export Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-sm">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Export
                            <i data-lucide="chevron-down" class="w-4 h-4 ml-2"></i>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                            <div class="py-2">
                                <div class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
                                    Format Export
                                </div>
                                <a href="#" onclick="exportData('balances', 'json')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-code" class="w-4 h-4 mr-3"></i>
                                    Saldo (JSON)
                                </a>
                                <a href="#" onclick="exportData('balances', 'csv')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Saldo (CSV)
                                </a>
                                <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                                <a href="#" onclick="exportData('transactions', 'json')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="arrow-right-left" class="w-4 h-4 mr-3"></i>
                                    Transaksi (JSON)
                                </a>
                                <a href="#" onclick="exportData('transactions', 'csv')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="arrow-right-left" class="w-4 h-4 mr-3"></i>
                                    Transaksi (CSV)
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exchange Rate Info -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex items-center gap-3 mb-6">
                <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="trending-up" class="w-5 h-5 text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Informasi Sistem UangTix</h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Exchange Rates -->
                <div class="space-y-3">
                    <h3 class="text-sm font-medium text-yellow-600 dark:text-yellow-400 uppercase tracking-wide">Kurs Saat Ini</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">IDR → UTX:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_rates['idr_to_uangtix']); ?></span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">UTX → IDR:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_rates['uangtix_to_idr']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Transaction Limits -->
                <div class="space-y-3">
                    <h3 class="text-sm font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">Batas Transaksi</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Min Deposit:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_limits['min_deposit']); ?></span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Max Deposit:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_limits['max_deposit']); ?></span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Min Penarikan:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_limits['min_withdrawal']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Transaction Fees -->
                <div class="space-y-3">
                    <h3 class="text-sm font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">Biaya Transaksi</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Biaya Deposit:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_fees['deposit_fee']); ?></span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Biaya Penarikan:</span>
                            <span class="font-medium text-gray-900 dark:text-white"><?php echo e($exchangeRate->formatted_fees['withdrawal_fee']); ?></span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600 dark:text-gray-400">Biaya Transfer:</span>
                            <span class="font-medium text-gray-900 dark:text-white">0%</span>
                        </div>
                    </div>
                </div>

                <!-- Service Status -->
                <div class="space-y-3">
                    <h3 class="text-sm font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">Status Layanan</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Deposit:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($exchangeRate->deposits_enabled ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'); ?>">
                                <div class="w-1.5 h-1.5 rounded-full <?php echo e($exchangeRate->deposits_enabled ? 'bg-green-400' : 'bg-red-400'); ?> mr-1"></div>
                                <?php echo e($exchangeRate->deposits_enabled ? 'Aktif' : 'Nonaktif'); ?>

                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Penarikan:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($exchangeRate->withdrawals_enabled ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'); ?>">
                                <div class="w-1.5 h-1.5 rounded-full <?php echo e($exchangeRate->withdrawals_enabled ? 'bg-green-400' : 'bg-red-400'); ?> mr-1"></div>
                                <?php echo e($exchangeRate->withdrawals_enabled ? 'Aktif' : 'Nonaktif'); ?>

                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Transfer:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($exchangeRate->transfers_enabled ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'); ?>">
                                <div class="w-1.5 h-1.5 rounded-full <?php echo e($exchangeRate->transfers_enabled ? 'bg-green-400' : 'bg-red-400'); ?> mr-1"></div>
                                <?php echo e($exchangeRate->transfers_enabled ? 'Aktif' : 'Nonaktif'); ?>

                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total UangTix -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400 uppercase tracking-wide">Total UangTix</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            <?php echo e(number_format($stats['total_balances'], 0, ',', '.')); ?> <span class="text-lg text-gray-500">UTX</span>
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="coins" class="w-6 h-6 text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                </div>
            </div>

            <!-- Active Users -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">Pengguna Aktif</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            <?php echo e(number_format($stats['total_users'], 0, ',', '.')); ?>

                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>

            <!-- Today's Transactions -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">Transaksi Hari Ini</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            <?php echo e(number_format($stats['total_transactions_today'], 0, ',', '.')); ?>

                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="arrow-right-left" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Today's Volume -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">Volume Hari Ini</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            <?php echo e(number_format($stats['total_volume_today'], 0, ',', '.')); ?> <span class="text-lg text-gray-500">UTX</span>
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="trending-up" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex items-center gap-3 mb-6">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="filter" class="w-5 h-5 text-blue-600 dark:text-blue-400"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Filter & Pencarian</h2>
            </div>

            <form method="GET" action="<?php echo e(route('admin.uangtix.index')); ?>" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Search -->
                    <div class="lg:col-span-2">
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Pencarian Pengguna
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="search" class="w-5 h-5 text-gray-400"></i>
                            </div>
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="<?php echo e(request('search')); ?>"
                                   placeholder="Nama atau email pengguna..."
                                   class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                        </div>
                    </div>

                    <!-- Min Balance -->
                    <div>
                        <label for="min_balance" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Saldo Minimum (UTX)
                        </label>
                        <input type="number"
                               id="min_balance"
                               name="min_balance"
                               value="<?php echo e(request('min_balance')); ?>"
                               placeholder="0"
                               class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                    </div>

                    <!-- Max Balance -->
                    <div>
                        <label for="max_balance" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Saldo Maksimum (UTX)
                        </label>
                        <input type="number"
                               id="max_balance"
                               name="max_balance"
                               value="<?php echo e(request('max_balance')); ?>"
                               placeholder="1000000"
                               class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 shadow-sm">
                        <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                        Filter
                    </button>
                    <a href="<?php echo e(route('admin.uangtix.index')); ?>"
                       class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                        Reset
                    </a>
                </div>
            </form>
        </div>

        <!-- UangTix Balances Table -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <i data-lucide="wallet" class="w-5 h-5 text-primary"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Saldo UangTix Pengguna</h2>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Pengguna
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Saldo UangTix
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Setara IDR
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total Earned
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Total Spent
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Aksi
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php $__empty_1 = true; $__currentLoopData = $balances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $balance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full object-cover"
                                                 src="<?php echo e($balance->user->avatar_url); ?>"
                                                 alt="<?php echo e($balance->user->name); ?>">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?php echo e($balance->user->name); ?>

                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?php echo e($balance->user->email); ?>

                                            </div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1 <?php echo e($balance->user->role == 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'); ?>">
                                                <?php echo e(ucfirst($balance->user->role)); ?>

                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-bold text-yellow-600 dark:text-yellow-400">
                                        <?php echo e($balance->formatted_balance); ?>

                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-green-600 dark:text-green-400">
                                        <?php echo e($balance->formatted_balance_idr); ?>

                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <?php echo e(number_format($balance->total_earned, 0, ',', '.')); ?> UTX
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <?php echo e(number_format($balance->total_spent, 0, ',', '.')); ?> UTX
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($balance->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'); ?>">
                                        <div class="w-1.5 h-1.5 rounded-full <?php echo e($balance->is_active ? 'bg-green-400' : 'bg-red-400'); ?> mr-1"></div>
                                        <?php echo e($balance->is_active ? 'Aktif' : 'Nonaktif'); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open"
                                                class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-150">
                                            Aksi
                                            <i data-lucide="chevron-down" class="w-4 h-4 ml-1"></i>
                                        </button>

                                        <div x-show="open"
                                             x-transition:enter="transition ease-out duration-100"
                                             x-transition:enter-start="transform opacity-0 scale-95"
                                             x-transition:enter-end="transform opacity-100 scale-100"
                                             x-transition:leave="transition ease-in duration-75"
                                             x-transition:leave-start="transform opacity-100 scale-100"
                                             x-transition:leave-end="transform opacity-0 scale-95"
                                             @click.away="open = false"
                                             class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                                            <div class="py-1">
                                                <a href="#"
                                                   onclick="showAdjustBalanceModal(<?php echo e($balance->user->id); ?>, '<?php echo e($balance->user->name); ?>', <?php echo e($balance->balance); ?>)"
                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                    <i data-lucide="edit" class="w-4 h-4 mr-3"></i>
                                                    Sesuaikan Saldo
                                                </a>
                                                <a href="<?php echo e(route('admin.uangtix.transactions')); ?>?search=<?php echo e($balance->user->email); ?>"
                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                    <i data-lucide="history" class="w-4 h-4 mr-3"></i>
                                                    Lihat Transaksi
                                                </a>
                                                <a href="#"
                                                   onclick="toggleBalanceStatus(<?php echo e($balance->user->id); ?>, <?php echo e($balance->is_active ? 'false' : 'true'); ?>)"
                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                    <i data-lucide="<?php echo e($balance->is_active ? 'ban' : 'check'); ?>" class="w-4 h-4 mr-3"></i>
                                                    <?php echo e($balance->is_active ? 'Nonaktifkan' : 'Aktifkan'); ?>

                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center">
                                        <i data-lucide="coins" class="w-12 h-12 text-gray-400 mb-4"></i>
                                        <p class="text-gray-500 dark:text-gray-400 text-lg font-medium">Tidak ada data saldo UangTix</p>
                                        <p class="text-gray-400 dark:text-gray-500 text-sm">Belum ada pengguna dengan saldo UangTix yang ditemukan</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($balances->hasPages()): ?>
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <?php echo e($balances->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Adjust Balance Modal -->
<div id="adjustBalanceModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <form id="adjustBalanceForm">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 dark:bg-yellow-900/20 sm:mx-0 sm:h-10 sm:w-10">
                            <i data-lucide="coins" class="w-6 h-6 text-yellow-600 dark:text-yellow-400"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                Sesuaikan Saldo UangTix
                            </h3>
                            <div class="mt-4 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Pengguna</label>
                                    <input type="text"
                                           id="userName"
                                           readonly
                                           class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white">
                                    <input type="hidden" id="userId">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Saldo Saat Ini</label>
                                    <input type="text"
                                           id="currentBalance"
                                           readonly
                                           class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white">
                                </div>

                                <div>
                                    <label for="adjustType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Jenis Penyesuaian</label>
                                    <select id="adjustType"
                                            name="type"
                                            required
                                            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        <option value="add">Tambah UangTix</option>
                                        <option value="deduct">Kurangi UangTix</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="adjustAmount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Jumlah (UTX)</label>
                                    <input type="number"
                                           id="adjustAmount"
                                           name="amount"
                                           min="0.01"
                                           step="0.01"
                                           required
                                           class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                </div>

                                <div>
                                    <label for="adjustDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Keterangan</label>
                                    <textarea id="adjustDescription"
                                              name="description"
                                              rows="3"
                                              required
                                              placeholder="Alasan penyesuaian saldo UangTix..."
                                              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white placeholder-gray-400"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit"
                            class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                        Sesuaikan Saldo
                    </button>
                    <button type="button"
                            onclick="closeAdjustBalanceModal()"
                            class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Batal
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function showAdjustBalanceModal(userId, userName, currentBalance) {
    document.getElementById('userId').value = userId;
    document.getElementById('userName').value = userName;
    document.getElementById('currentBalance').value = new Intl.NumberFormat('id-ID').format(currentBalance) + ' UTX';

    document.getElementById('adjustBalanceModal').classList.remove('hidden');
}

function closeAdjustBalanceModal() {
    document.getElementById('adjustBalanceModal').classList.add('hidden');
    document.getElementById('adjustBalanceForm').reset();
}

document.getElementById('adjustBalanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const userId = document.getElementById('userId').value;
    const formData = new FormData(this);
    
    try {
        const response = await fetch(`/admin/uangtix/${userId}/adjust`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            closeAdjustBalanceModal();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

async function toggleBalanceStatus(userId, isActive) {
    if (!confirm(`Apakah Anda yakin ingin ${isActive === 'true' ? 'mengaktifkan' : 'menonaktifkan'} akun UangTix pengguna ini?`)) {
        return;
    }

    try {
        const response = await fetch(`/admin/uangtix/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: isActive === 'true'
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

async function exportData(type, format = 'json') {
    try {
        const url = `/admin/uangtix/export?type=${type}&format=${format}`;

        if (format === 'csv') {
            // For CSV, directly download the file
            window.location.href = url;
            showAlert('success', 'Data CSV berhasil didownload');
            return;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (data.success) {
            // Download JSON file
            const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            a.click();
            window.URL.revokeObjectURL(url);

            showAlert('success', 'Data berhasil diexport');
        } else {
            showAlert('error', 'Gagal export data');
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

function showAlert(type, message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/uangtix/index.blade.php ENDPATH**/ ?>