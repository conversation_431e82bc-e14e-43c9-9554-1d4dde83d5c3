<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ad_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('plan_type'); // basic, premium, enterprise
            $table->string('plan_name');
            $table->text('plan_description')->nullable();
            $table->decimal('monthly_price', 10, 2);
            $table->decimal('yearly_price', 10, 2)->nullable();
            $table->string('billing_cycle')->default('monthly'); // monthly, yearly
            $table->integer('max_ads')->default(5);
            $table->integer('max_impressions_per_day')->default(1000);
            $table->integer('max_clicks_per_day')->default(100);
            $table->json('features')->nullable(); // JSON array of features
            $table->boolean('priority_placement')->default(false);
            $table->boolean('analytics_access')->default(false);
            $table->boolean('custom_targeting')->default(false);
            $table->datetime('starts_at');
            $table->datetime('expires_at');
            $table->string('status')->default('active'); // active, expired, cancelled, suspended
            $table->decimal('amount_paid', 10, 2)->default(0);
            $table->string('payment_method')->nullable();
            $table->string('payment_reference')->nullable();
            $table->datetime('last_payment_at')->nullable();
            $table->datetime('next_payment_at')->nullable();
            $table->boolean('auto_renewal')->default(true);
            $table->text('cancellation_reason')->nullable();
            $table->datetime('cancelled_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['plan_type']);
            $table->index(['expires_at']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_subscriptions');
    }
};
