<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class PlatformSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public',
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get setting value with proper type casting
     */
    public function getTypedValueAttribute()
    {
        return match($this->type) {
            'boolean' => filter_var($this->value, FILTER_VALIDATE_BOOLEAN),
            'number' => is_numeric($this->value) ? (float) $this->value : 0,
            'json' => json_decode($this->value, true),
            default => $this->value
        };
    }

    /**
     * Set setting value
     */
    public static function set(string $key, $value, string $type = 'string', string $group = 'general', string $description = null): self
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => is_array($value) ? json_encode($value) : (string) $value,
                'type' => $type,
                'group' => $group,
                'description' => $description,
            ]
        );

        // Clear cache
        Cache::forget("platform_setting_{$key}");
        Cache::forget("platform_settings_{$group}");

        return $setting;
    }

    /**
     * Get setting value
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("platform_setting_{$key}", 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->first();
            
            if (!$setting) {
                return $default;
            }

            return $setting->typed_value;
        });
    }

    /**
     * Get settings by group
     */
    public static function getGroup(string $group): array
    {
        return Cache::remember("platform_settings_{$group}", 3600, function () use ($group) {
            return self::where('group', $group)
                ->get()
                ->mapWithKeys(function ($setting) {
                    return [$setting->key => $setting->typed_value];
                })
                ->toArray();
        });
    }

    /**
     * Initialize default platform settings
     */
    public static function initializeDefaults(): void
    {
        $defaults = [
            // Fee settings
            'platform_fee_percentage' => [
                'value' => '5',
                'type' => 'number',
                'group' => 'fees',
                'description' => 'Platform fee percentage per transaction'
            ],
            'platform_fee_fixed' => [
                'value' => '2500',
                'type' => 'number',
                'group' => 'fees',
                'description' => 'Fixed platform fee per transaction (IDR)'
            ],
            'minimum_withdrawal' => [
                'value' => '50000',
                'type' => 'number',
                'group' => 'fees',
                'description' => 'Minimum withdrawal amount (IDR)'
            ],
            'withdrawal_fee' => [
                'value' => '5000',
                'type' => 'number',
                'group' => 'fees',
                'description' => 'Withdrawal fee (IDR)'
            ],
            
            // Subscription settings
            'subscription_monthly_price' => [
                'value' => '50000',
                'type' => 'number',
                'group' => 'subscriptions',
                'description' => 'Monthly subscription price (IDR)'
            ],
            'subscription_quarterly_price' => [
                'value' => '135000',
                'type' => 'number',
                'group' => 'subscriptions',
                'description' => 'Quarterly subscription price (IDR)'
            ],
            'subscription_semi_annual_price' => [
                'value' => '255000',
                'type' => 'number',
                'group' => 'subscriptions',
                'description' => 'Semi-annual subscription price (IDR)'
            ],
            'subscription_annual_price' => [
                'value' => '480000',
                'type' => 'number',
                'group' => 'subscriptions',
                'description' => 'Annual subscription price (IDR)'
            ],
            
            // Balance settings
            'auto_approve_withdrawals' => [
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'balance',
                'description' => 'Auto approve withdrawal requests'
            ],
            'withdrawal_processing_days' => [
                'value' => '3',
                'type' => 'number',
                'group' => 'balance',
                'description' => 'Withdrawal processing time in days'
            ],
            
            // General settings
            'platform_name' => [
                'value' => 'TiXara',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Platform name'
            ],
            'support_email' => [
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Support email address'
            ],

            // SEO settings
            'seo_site_title' => [
                'value' => 'TiXara - Platform Tiket Event Terpercaya Indonesia',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Default site title for SEO'
            ],
            'seo_site_description' => [
                'value' => 'Temukan dan beli tiket event favorit Anda dengan mudah dan aman di TiXara. Platform tiket event terpercaya dengan berbagai pilihan acara menarik.',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default site description for SEO'
            ],
            'seo_site_keywords' => [
                'value' => 'tiket event, konser, festival, seminar, workshop, acara, entertainment, Indonesia',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default site keywords for SEO'
            ],
            'seo_og_image' => [
                'value' => '/images/og-image.jpg',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Default Open Graph image URL'
            ],
            'seo_twitter_handle' => [
                'value' => '@tixara_id',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Twitter handle for Twitter Cards'
            ],
            'seo_google_analytics_id' => [
                'value' => '',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Google Analytics tracking ID'
            ],
            'seo_google_tag_manager_id' => [
                'value' => '',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Google Tag Manager ID'
            ],
            'seo_facebook_pixel_id' => [
                'value' => '',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Facebook Pixel ID'
            ],
            'seo_google_site_verification' => [
                'value' => '',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Google Search Console verification code'
            ],
            'seo_bing_site_verification' => [
                'value' => '',
                'type' => 'string',
                'group' => 'seo',
                'description' => 'Bing Webmaster verification code'
            ],
            'seo_robots_txt' => [
                'value' => "User-agent: *\nDisallow: /admin/\nDisallow: /api/\nSitemap: " . url('/sitemap.xml'),
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Robots.txt content'
            ],
            'seo_enable_structured_data' => [
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'seo',
                'description' => 'Enable structured data (JSON-LD)'
            ],
        ];

        foreach ($defaults as $key => $config) {
            self::firstOrCreate(
                ['key' => $key],
                $config
            );
        }
    }

    /**
     * Get fee configuration
     */
    public static function getFeeConfig(): array
    {
        return [
            'percentage' => self::get('platform_fee_percentage', 5),
            'fixed' => self::get('platform_fee_fixed', 2500),
            'minimum_withdrawal' => self::get('minimum_withdrawal', 50000),
            'withdrawal_fee' => self::get('withdrawal_fee', 5000),
        ];
    }

    /**
     * Get subscription prices
     */
    public static function getSubscriptionPrices(): array
    {
        return [
            'monthly' => self::get('subscription_monthly_price', 50000),
            'quarterly' => self::get('subscription_quarterly_price', 135000),
            'semi_annual' => self::get('subscription_semi_annual_price', 255000),
            'annual' => self::get('subscription_annual_price', 480000),
        ];
    }

    /**
     * Calculate platform fee for amount
     */
    public static function calculateFee(float $amount): float
    {
        $config = self::getFeeConfig();
        $percentageFee = ($amount * $config['percentage']) / 100;
        return $percentageFee + $config['fixed'];
    }

    /**
     * Check if user is exempt from fees (has active subscription)
     */
    public static function isExemptFromFees(User $user): bool
    {
        return $user->hasActiveSubscription();
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache(): void
    {
        $groups = ['fees', 'subscriptions', 'balance', 'general', 'seo'];

        foreach ($groups as $group) {
            Cache::forget("platform_settings_{$group}");
        }

        // Clear individual setting caches
        $settings = self::all();
        foreach ($settings as $setting) {
            Cache::forget("platform_setting_{$setting->key}");
        }
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($setting) {
            Cache::forget("platform_setting_{$setting->key}");
            Cache::forget("platform_settings_{$setting->group}");
        });

        static::deleted(function ($setting) {
            Cache::forget("platform_setting_{$setting->key}");
            Cache::forget("platform_settings_{$setting->group}");
        });
    }
}
