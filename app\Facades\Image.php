<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver as GdDriver;

/**
 * @method static \Intervention\Image\Interfaces\ImageInterface make(mixed $input)
 * @method static \Intervention\Image\Interfaces\ImageInterface read(mixed $input)
 * @method static \Intervention\Image\Interfaces\ImageInterface create(int $width, int $height)
 * @method static \Intervention\Image\ImageManager driver(\Intervention\Image\Interfaces\DriverInterface $driver)
 * @method static \Intervention\Image\ImageManager config(array $config)
 *
 * @see \Intervention\Image\ImageManager
 */
class Image extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'image';
    }

    /**
     * Create a safe image manager instance that always uses GD
     */
    public static function createSafeManager(): ImageManager
    {
        // Ensure GD is available
        if (!extension_loaded('gd')) {
            throw new \Exception('GD extension is required for image processing but not available');
        }

        // Always return GD driver
        return new ImageManager(new GdDriver());
    }

    /**
     * Safe image read method with error handling
     */
    public static function safeRead(mixed $input): \Intervention\Image\Interfaces\ImageInterface
    {
        try {
            $manager = static::createSafeManager();
            return $manager->read($input);
        } catch (\Exception $e) {
            \Log::error('Image processing error: ' . $e->getMessage());
            throw new \Exception('Failed to process image: ' . $e->getMessage());
        }
    }

    /**
     * Check if image processing is available
     */
    public static function isAvailable(): bool
    {
        return extension_loaded('gd');
    }

    /**
     * Get current driver information
     */
    public static function getDriverInfo(): array
    {
        if (!static::isAvailable()) {
            return [
                'available' => false,
                'driver' => 'none',
                'error' => 'GD extension not available'
            ];
        }

        $gdInfo = gd_info();

        return [
            'available' => true,
            'driver' => 'gd',
            'version' => $gdInfo['GD Version'] ?? 'Unknown',
            'formats' => [
                'jpeg' => $gdInfo['JPEG Support'] ?? false,
                'png' => $gdInfo['PNG Support'] ?? false,
                'gif' => $gdInfo['GIF Read Support'] ?? false,
                'webp' => $gdInfo['WebP Support'] ?? false,
            ]
        ];
    }
}
