/**
 * Payment Monitoring System
 * Handles real-time payment status monitoring and notifications
 */

class PaymentMonitor {
    constructor() {
        this.orderId = null;
        this.monitoringInterval = null;
        this.maxRetries = 60; // 5 minutes with 5-second intervals
        this.retryCount = 0;
        this.isMonitoring = false;
        this.audioContext = null;
        
        this.initAudioContext();
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Audio context not supported:', error);
        }
    }

    startMonitoring(orderId) {
        if (this.isMonitoring) {
            this.stopMonitoring();
        }

        this.orderId = orderId;
        this.isMonitoring = true;
        this.retryCount = 0;

        console.log('Starting payment monitoring for order:', orderId);
        
        // Start monitoring immediately
        this.checkPaymentStatus();
        
        // Set up interval for periodic checks
        this.monitoringInterval = setInterval(() => {
            this.checkPaymentStatus();
        }, 5000); // Check every 5 seconds
    }

    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.isMonitoring = false;
        console.log('Payment monitoring stopped');
    }

    async checkPaymentStatus() {
        if (!this.orderId || this.retryCount >= this.maxRetries) {
            this.stopMonitoring();
            return;
        }

        try {
            const response = await fetch(`/orders/${this.orderId}/check-status`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.status === 'paid') {
                this.handlePaymentSuccess(data);
            } else if (data.status === 'failed' || data.status === 'expired') {
                this.handlePaymentFailure(data);
            } else {
                // Payment still pending, continue monitoring
                this.retryCount++;
                this.updatePaymentStatus(data);
            }

        } catch (error) {
            console.error('Error checking payment status:', error);
            this.retryCount++;
            
            if (this.retryCount >= this.maxRetries) {
                this.handleMonitoringTimeout();
            }
        }
    }

    handlePaymentSuccess(data) {
        console.log('Payment successful!', data);
        
        this.stopMonitoring();
        
        // Play success sound
        this.playSuccessSound();
        
        // Show success notification
        this.showNotification(
            '🎉 Pembayaran berhasil! Mengalihkan ke halaman tiket...', 
            'success'
        );
        
        // Trigger confetti effect
        this.triggerConfetti();
        
        // Redirect to success page after a short delay
        setTimeout(() => {
            window.location.href = `/orders/${this.orderId}/success`;
        }, 2000);
    }

    handlePaymentFailure(data) {
        console.log('Payment failed:', data);
        
        this.stopMonitoring();
        
        // Play error sound
        this.playErrorSound();
        
        // Show error notification
        this.showNotification(
            '❌ Pembayaran gagal atau kedaluwarsa. Silakan coba lagi.', 
            'error'
        );
        
        // Redirect back to payment page
        setTimeout(() => {
            window.location.href = `/orders/${this.orderId}/payment`;
        }, 3000);
    }

    handleMonitoringTimeout() {
        console.log('Payment monitoring timeout');
        
        this.stopMonitoring();
        
        this.showNotification(
            '⏰ Waktu monitoring habis. Silakan refresh halaman untuk memeriksa status pembayaran.', 
            'warning'
        );
    }

    updatePaymentStatus(data) {
        // Update UI with current payment status
        const statusElement = document.getElementById('payment-status');
        if (statusElement) {
            statusElement.textContent = `Status: ${data.status || 'Menunggu pembayaran...'}`;
        }

        // Update countdown if available
        if (data.expires_at) {
            this.updateCountdown(data.expires_at);
        }
    }

    updateCountdown(expiresAt) {
        const countdownElement = document.getElementById('payment-countdown');
        if (!countdownElement) return;

        const expiryTime = new Date(expiresAt).getTime();
        const now = new Date().getTime();
        const timeLeft = expiryTime - now;

        if (timeLeft <= 0) {
            countdownElement.textContent = 'Waktu habis';
            return;
        }

        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    playSuccessSound() {
        if (!this.audioContext) return;

        try {
            // Play a celebratory melody
            const frequencies = [523, 659, 784, 1047]; // C, E, G, C (major chord)
            
            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    this.playTone(freq, 0.3, 0.1);
                }, index * 150);
            });
            
            // Final celebration tone
            setTimeout(() => {
                this.playTone(1047, 0.5, 0.15);
            }, 800);
            
        } catch (error) {
            console.error('Error playing success sound:', error);
        }
    }

    playErrorSound() {
        if (!this.audioContext) return;

        try {
            // Play error sound (descending tones)
            this.playTone(400, 0.2, 0.1);
            setTimeout(() => this.playTone(300, 0.3, 0.1), 200);
        } catch (error) {
            console.error('Error playing error sound:', error);
        }
    }

    playTone(frequency, duration, volume = 0.1) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(volume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const bgColor = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'warning': 'bg-yellow-500',
            'info': 'bg-blue-500'
        }[type] || 'bg-blue-500';

        const icon = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }[type] || 'ℹ️';

        notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 max-w-md`;
        notification.innerHTML = `
            <div class="flex items-start space-x-3">
                <span class="text-xl">${icon}</span>
                <div class="flex-1">
                    <p class="font-semibold">Notifikasi Pembayaran</p>
                    <p class="text-sm mt-1">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds (except for errors)
        if (type !== 'error') {
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, 300);
                }
            }, 5000);
        }
    }

    triggerConfetti() {
        // Create confetti effect
        for (let i = 0; i < 100; i++) {
            setTimeout(() => {
                this.createConfetti();
            }, i * 10);
        }
    }

    createConfetti() {
        const confetti = document.createElement('div');
        const colors = ['#A8D5BA', '#4CAF50', '#2196F3', '#FF9800', '#E91E63'];
        const color = colors[Math.floor(Math.random() * colors.length)];
        
        confetti.style.cssText = `
            position: fixed;
            width: 8px;
            height: 8px;
            background: ${color};
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            left: ${Math.random() * 100}vw;
            top: -10px;
            animation: confetti-fall ${Math.random() * 3 + 2}s linear forwards;
        `;

        document.body.appendChild(confetti);

        setTimeout(() => {
            if (document.body.contains(confetti)) {
                document.body.removeChild(confetti);
            }
        }, 5000);
    }
}

// Add CSS for confetti animation
const style = document.createElement('style');
style.textContent = `
    @keyframes confetti-fall {
        to {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Export for global use
window.PaymentMonitor = PaymentMonitor;
