<?php $__env->startSection('title', 'UangTix - Dompet Digital'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* UangTix Styles */
.uangtix-hero {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #92400e 100%);
    position: relative;
    overflow: hidden;
}

.uangtix-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="wallet" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23wallet)"/></svg>');
    opacity: 0.3;
}

.balance-card {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
    border-radius: 24px;
    padding: 32px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f59e0b, #d97706);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #f59e0b;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.action-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 16px 24px;
    border: none;
    border-radius: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(245, 158, 11, 0.3);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.action-btn.secondary:hover {
    box-shadow: 0 12px 32px rgba(107, 114, 128, 0.3);
}

.transaction-item {
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.transaction-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-color: #f59e0b;
}

.modal-content {
    border-radius: 24px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 2px solid #f3f4f6;
    border-radius: 24px 24px 0 0;
    padding: 24px;
}

.modal-body {
    padding: 24px;
}

.form-control {
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #f59e0b;
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.1);
}

@media (max-width: 768px) {
    .balance-card {
        padding: 24px;
    }

    .feature-card {
        padding: 20px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="uangtix-hero text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 backdrop-blur-sm">
                <i data-lucide="wallet" class="w-10 h-10 text-white"></i>
            </div>
            <h1 class="text-5xl md:text-6xl font-bold mb-6">
                UangTix
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                Dompet Digital TiXara
            </p>
            <p class="text-lg mb-12 max-w-2xl mx-auto opacity-80">
                Kelola saldo digital Anda dengan mudah dan aman. Deposit, tarik, transfer, dan pantau transaksi dalam satu platform.
            </p>
        </div>
    </div>
</div>

<!-- Main Dashboard -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Balance Card -->
        <div class="balance-card mb-12">
            <div class="relative z-10">
                <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
                    <div>
                        <h2 class="text-lg opacity-90 mb-2">Saldo UangTix Anda</h2>
                        <div class="text-4xl md:text-5xl font-bold mb-4">
                            <?php echo e(number_format($balance->balance, 0, ',', '.')); ?> <span class="text-2xl opacity-80">UTX</span>
                        </div>
                        <div class="text-lg opacity-80">
                            ≈ Rp <?php echo e(number_format($balance->balance * $exchangeRate->rate_uangtix_to_idr, 0, ',', '.')); ?>

                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button class="action-btn" data-bs-toggle="modal" data-bs-target="#depositModal">
                            <i data-lucide="plus" class="w-5 h-5"></i>
                            Deposit
                        </button>
                        <button class="action-btn secondary" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                            <i data-lucide="minus" class="w-5 h-5"></i>
                            Tarik
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="trending-up" class="w-8 h-8 text-blue-600 dark:text-blue-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Total Earned</h3>
                <p class="text-2xl font-bold text-blue-600"><?php echo e(number_format($stats['total_earned'], 0, ',', '.')); ?> UTX</p>
            </div>

            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="arrow-down" class="w-8 h-8 text-green-600 dark:text-green-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Total Deposited</h3>
                <p class="text-2xl font-bold text-green-600"><?php echo e(number_format($stats['total_deposited'], 0, ',', '.')); ?> UTX</p>
            </div>

            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="arrow-up" class="w-8 h-8 text-red-600 dark:text-red-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Total Withdrawn</h3>
                <p class="text-2xl font-bold text-red-600"><?php echo e(number_format($stats['total_withdrawn'], 0, ',', '.')); ?> UTX</p>
            </div>

            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="activity" class="w-8 h-8 text-purple-600 dark:text-purple-400"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Transaksi Bulan Ini</h3>
                <p class="text-2xl font-bold text-purple-600"><?php echo e($stats['transactions_this_month']); ?></p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div class="feature-card text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="plus" class="w-10 h-10 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Deposit</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">Tambah saldo UangTix dari rekening bank</p>
                <button class="action-btn w-full" data-bs-toggle="modal" data-bs-target="#depositModal">
                    <i data-lucide="plus" class="w-5 h-5"></i>
                    Deposit Sekarang
                </button>
            </div>

            <div class="feature-card text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="minus" class="w-10 h-10 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Tarik Saldo</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">Tarik saldo UangTix ke rekening bank</p>
                <button class="action-btn secondary w-full" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                    <i data-lucide="minus" class="w-5 h-5"></i>
                    Tarik Sekarang
                </button>
            </div>

            <div class="feature-card text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="send" class="w-10 h-10 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Transfer</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">Transfer UangTix ke pengguna lain</p>
                <button class="action-btn w-full" style="background: linear-gradient(135deg, #10b981, #059669);" data-bs-toggle="modal" data-bs-target="#transferModal">
                    <i data-lucide="send" class="w-5 h-5"></i>
                    Transfer Sekarang
                </button>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-0">Transaksi Terbaru</h2>
                <a href="<?php echo e(route('uangtix.transactions')); ?>" class="action-btn">
                    <i data-lucide="list" class="w-5 h-5"></i>
                    Lihat Semua
                </a>
            </div>

            <?php if($recentTransactions->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $recentTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="transaction-item">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 rounded-xl flex items-center justify-center
                                    <?php if($transaction->type === 'deposit'): ?> bg-green-100 text-green-600
                                    <?php elseif($transaction->type === 'withdrawal'): ?> bg-red-100 text-red-600
                                    <?php elseif($transaction->type === 'transfer_in'): ?> bg-blue-100 text-blue-600
                                    <?php elseif($transaction->type === 'transfer_out'): ?> bg-orange-100 text-orange-600
                                    <?php else: ?> bg-gray-100 text-gray-600
                                    <?php endif; ?>">
                                    <?php if($transaction->type === 'deposit'): ?>
                                        <i data-lucide="arrow-down" class="w-6 h-6"></i>
                                    <?php elseif($transaction->type === 'withdrawal'): ?>
                                        <i data-lucide="arrow-up" class="w-6 h-6"></i>
                                    <?php elseif($transaction->type === 'transfer_in'): ?>
                                        <i data-lucide="arrow-down-left" class="w-6 h-6"></i>
                                    <?php elseif($transaction->type === 'transfer_out'): ?>
                                        <i data-lucide="arrow-up-right" class="w-6 h-6"></i>
                                    <?php else: ?>
                                        <i data-lucide="activity" class="w-6 h-6"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 dark:text-white">
                                        <?php if($transaction->type === 'deposit'): ?> Deposit
                                        <?php elseif($transaction->type === 'withdrawal'): ?> Penarikan
                                        <?php elseif($transaction->type === 'transfer_in'): ?> Transfer Masuk
                                        <?php elseif($transaction->type === 'transfer_out'): ?> Transfer Keluar
                                        <?php else: ?> <?php echo e(ucfirst($transaction->type)); ?>

                                        <?php endif; ?>
                                    </h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($transaction->created_at->format('d M Y, H:i')); ?></p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold
                                    <?php if(in_array($transaction->type, ['deposit', 'transfer_in', 'earning'])): ?> text-green-600
                                    <?php else: ?> text-red-600
                                    <?php endif; ?>">
                                    <?php if(in_array($transaction->type, ['deposit', 'transfer_in', 'earning'])): ?>
                                        +<?php echo e(number_format($transaction->amount, 0, ',', '.')); ?>

                                    <?php else: ?>
                                        -<?php echo e(number_format(abs($transaction->amount), 0, ',', '.')); ?>

                                    <?php endif; ?>
                                    UTX
                                </p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    Saldo: <?php echo e(number_format($transaction->balance_after, 0, ',', '.')); ?> UTX
                                </p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="activity" class="w-8 h-8 text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Belum ada transaksi</h3>
                    <p class="text-gray-600 dark:text-gray-300">Mulai dengan melakukan deposit pertama Anda</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Deposit Modal -->
<div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="depositModalLabel">
                    <i data-lucide="plus" class="w-5 h-5 inline mr-2"></i>
                    Deposit UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="depositForm">
                    <?php echo csrf_field(); ?>
                    <div class="mb-4">
                        <label for="depositAmount" class="form-label">Jumlah Deposit (IDR)</label>
                        <input type="number" class="form-control" id="depositAmount" name="amount_idr"
                               min="<?php echo e($exchangeRate->min_deposit_idr); ?>"
                               max="<?php echo e($exchangeRate->max_deposit_idr); ?>"
                               placeholder="Masukkan jumlah dalam Rupiah" required>
                        <div class="form-text">
                            Minimum: Rp <?php echo e(number_format($exchangeRate->min_deposit_idr, 0, ',', '.')); ?> -
                            Maximum: Rp <?php echo e(number_format($exchangeRate->max_deposit_idr, 0, ',', '.')); ?>

                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="paymentMethod" class="form-label">Metode Pembayaran</label>
                        <select class="form-control" id="paymentMethod" name="payment_method" required>
                            <option value="">Pilih metode pembayaran</option>
                            <option value="bank_transfer">Transfer Bank</option>
                            <option value="virtual_account">Virtual Account</option>
                            <option value="e_wallet">E-Wallet</option>
                            <option value="qris">QRIS</option>
                        </select>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-4">
                        <h6 class="font-semibold mb-3">Ringkasan Deposit</h6>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Jumlah Deposit:</span>
                                <span id="depositSummaryAmount">Rp 0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Fee (<?php echo e($exchangeRate->deposit_fee_percentage); ?>%):</span>
                                <span id="depositSummaryFee">Rp 0</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Jumlah Bersih:</span>
                                <span id="depositSummaryNet">Rp 0</span>
                            </div>
                            <hr>
                            <div class="flex justify-between font-bold">
                                <span>UangTix yang Diterima:</span>
                                <span id="depositSummaryUTX">0 UTX</span>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-secondary flex-1" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary flex-1">Proses Deposit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1" aria-labelledby="withdrawModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="withdrawModalLabel">
                    <i data-lucide="minus" class="w-5 h-5 inline mr-2"></i>
                    Tarik Saldo UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="withdrawForm">
                    <?php echo csrf_field(); ?>
                    <div class="mb-4">
                        <label for="withdrawAmount" class="form-label">Jumlah Penarikan (UTX)</label>
                        <input type="number" class="form-control" id="withdrawAmount" name="amount_uangtix"
                               min="<?php echo e($exchangeRate->min_withdrawal_uangtix); ?>"
                               max="<?php echo e($balance->balance); ?>"
                               placeholder="Masukkan jumlah UangTix" required>
                        <div class="form-text">
                            Saldo tersedia: <?php echo e(number_format($balance->balance, 0, ',', '.')); ?> UTX -
                            Minimum: <?php echo e(number_format($exchangeRate->min_withdrawal_uangtix, 0, ',', '.')); ?> UTX
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="bankName" class="form-label">Nama Bank</label>
                            <select class="form-control" id="bankName" name="bank_name" required>
                                <option value="">Pilih Bank</option>
                                <option value="BCA">BCA</option>
                                <option value="BNI">BNI</option>
                                <option value="BRI">BRI</option>
                                <option value="Mandiri">Mandiri</option>
                                <option value="CIMB">CIMB Niaga</option>
                                <option value="Danamon">Danamon</option>
                                <option value="Permata">Permata</option>
                                <option value="BTN">BTN</option>
                                <option value="BSI">BSI</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="bankAccountNumber" class="form-label">Nomor Rekening</label>
                            <input type="text" class="form-control" id="bankAccountNumber" name="bank_account_number"
                                   placeholder="Nomor rekening" required>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="bankAccountName" class="form-label">Nama Pemilik Rekening</label>
                        <input type="text" class="form-control" id="bankAccountName" name="bank_account_name"
                               placeholder="Nama sesuai rekening bank" required>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-4">
                        <h6 class="font-semibold mb-3">Ringkasan Penarikan</h6>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Jumlah Penarikan:</span>
                                <span id="withdrawSummaryAmount">0 UTX</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Fee (<?php echo e($exchangeRate->withdrawal_fee_percentage); ?>%):</span>
                                <span id="withdrawSummaryFee">0 UTX</span>
                            </div>
                            <hr>
                            <div class="flex justify-between font-bold">
                                <span>Diterima di Rekening:</span>
                                <span id="withdrawSummaryNet">Rp 0</span>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-secondary flex-1" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-danger flex-1">Proses Penarikan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1" aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transferModalLabel">
                    <i data-lucide="send" class="w-5 h-5 inline mr-2"></i>
                    Transfer UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="transferForm">
                    <?php echo csrf_field(); ?>
                    <div class="mb-4">
                        <label for="toUserEmail" class="form-label">Email Penerima</label>
                        <input type="email" class="form-control" id="toUserEmail" name="to_user_email"
                               placeholder="Masukkan email penerima" required>
                        <div class="form-text">Pastikan email penerima terdaftar di TiXara</div>
                    </div>

                    <div class="mb-4">
                        <label for="transferAmount" class="form-label">Jumlah Transfer (UTX)</label>
                        <input type="number" class="form-control" id="transferAmount" name="amount"
                               min="1" max="<?php echo e($balance->balance); ?>"
                               placeholder="Masukkan jumlah UangTix" required>
                        <div class="form-text">
                            Saldo tersedia: <?php echo e(number_format($balance->balance, 0, ',', '.')); ?> UTX
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="transferDescription" class="form-label">Keterangan (Opsional)</label>
                        <textarea class="form-control" id="transferDescription" name="description"
                                  rows="3" placeholder="Keterangan transfer..."></textarea>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 mb-4">
                        <h6 class="font-semibold mb-3">Ringkasan Transfer</h6>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Jumlah Transfer:</span>
                                <span id="transferSummaryAmount">0 UTX</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Biaya Transfer:</span>
                                <span id="transferSummaryFee">0 UTX (Gratis)</span>
                            </div>
                            <hr>
                            <div class="flex justify-between font-bold">
                                <span>Total yang Dikirim:</span>
                                <span id="transferSummaryTotal">0 UTX</span>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-secondary flex-1" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-success flex-1">Kirim Transfer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
/*
 * UangTix JavaScript
 *
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 *
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Deposit form handling
    document.getElementById('depositForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("uangtix.deposit")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('depositModal')).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat memproses deposit');
            }
        } catch (error) {
            console.error('Deposit error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Withdraw form handling
    document.getElementById('withdrawForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("uangtix.withdraw")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('withdrawModal')).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat memproses penarikan');
            }
        } catch (error) {
            console.error('Withdraw error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Transfer form handling
    document.getElementById('transferForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("uangtix.transfer")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat memproses transfer');
            }
        } catch (error) {
            console.error('Transfer error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Real-time calculation for deposit
    document.getElementById('depositAmount').addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;
        const exchangeRate = <?php echo e($exchangeRate->rate_idr_to_uangtix ?? 0.001); ?>;
        const feePercentage = <?php echo e($exchangeRate->deposit_fee_percentage ?? 2.5); ?>;
        const minDeposit = <?php echo e($exchangeRate->min_deposit_idr ?? 10000); ?>;

        if (amount >= minDeposit) {
            const fee = amount * (feePercentage / 100);
            const netAmount = amount - fee;
            const uangTixAmount = netAmount * exchangeRate;

            document.getElementById('depositSummaryAmount').textContent = `Rp ${amount.toLocaleString('id-ID')}`;
            document.getElementById('depositSummaryFee').textContent = `Rp ${fee.toLocaleString('id-ID')}`;
            document.getElementById('depositSummaryNet').textContent = `Rp ${netAmount.toLocaleString('id-ID')}`;
            document.getElementById('depositSummaryUTX').textContent = `${Math.floor(uangTixAmount).toLocaleString('id-ID')} UTX`;
        } else {
            document.getElementById('depositSummaryAmount').textContent = 'Rp 0';
            document.getElementById('depositSummaryFee').textContent = 'Rp 0';
            document.getElementById('depositSummaryNet').textContent = 'Rp 0';
            document.getElementById('depositSummaryUTX').textContent = '0 UTX';
        }
    });

    // Real-time calculation for withdrawal
    document.getElementById('withdrawAmount').addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;
        const exchangeRate = <?php echo e($exchangeRate->rate_uangtix_to_idr ?? 1000); ?>;
        const feePercentage = <?php echo e($exchangeRate->withdrawal_fee_percentage ?? 2.5); ?>;
        const minWithdrawal = <?php echo e($exchangeRate->min_withdrawal_uangtix ?? 10); ?>;

        if (amount >= minWithdrawal) {
            const grossIdr = amount * exchangeRate;
            const fee = amount * (feePercentage / 100);
            const netIdr = (amount - fee) * exchangeRate;

            document.getElementById('withdrawSummaryAmount').textContent = `${amount.toLocaleString('id-ID')} UTX`;
            document.getElementById('withdrawSummaryFee').textContent = `${fee.toLocaleString('id-ID')} UTX`;
            document.getElementById('withdrawSummaryNet').textContent = `Rp ${Math.floor(netIdr).toLocaleString('id-ID')}`;
        } else {
            document.getElementById('withdrawSummaryAmount').textContent = '0 UTX';
            document.getElementById('withdrawSummaryFee').textContent = '0 UTX';
            document.getElementById('withdrawSummaryNet').textContent = 'Rp 0';
        }
    });

    // Real-time calculation for transfer
    document.getElementById('transferAmount').addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;

        document.getElementById('transferSummaryAmount').textContent = `${amount.toLocaleString('id-ID')} UTX`;
        document.getElementById('transferSummaryTotal').textContent = `${amount.toLocaleString('id-ID')} UTX`;
    });

    // Reset forms when modals are hidden
    document.getElementById('depositModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('depositForm').reset();
        document.getElementById('depositSummaryAmount').textContent = 'Rp 0';
        document.getElementById('depositSummaryFee').textContent = 'Rp 0';
        document.getElementById('depositSummaryNet').textContent = 'Rp 0';
        document.getElementById('depositSummaryUTX').textContent = '0 UTX';
    });

    document.getElementById('withdrawModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('withdrawForm').reset();
        document.getElementById('withdrawSummaryAmount').textContent = '0 UTX';
        document.getElementById('withdrawSummaryFee').textContent = '0 UTX';
        document.getElementById('withdrawSummaryNet').textContent = 'Rp 0';
    });

    document.getElementById('transferModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('transferForm').reset();
        document.getElementById('transferSummaryAmount').textContent = '0 UTX';
        document.getElementById('transferSummaryTotal').textContent = '0 UTX';
    });
});

function showToast(type, message) {
    // Remove existing toasts
    document.querySelectorAll('.toast-notification').forEach(toast => toast.remove());

    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/uangtix/index.blade.php ENDPATH**/ ?>