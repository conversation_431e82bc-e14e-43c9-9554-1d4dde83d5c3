<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserBalance;
use App\Models\BalanceTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BalanceController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display balance overview
     */
    public function index(Request $request)
    {
        $query = UserBalance::with(['user'])
            ->whereHas('user', function($q) {
                $q->whereIn('role', ['penjual', 'admin']);
            });

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by balance range
        if ($request->filled('min_balance')) {
            $query->where('balance', '>=', $request->min_balance);
        }
        if ($request->filled('max_balance')) {
            $query->where('balance', '<=', $request->max_balance);
        }

        $balances = $query->orderBy('balance', 'desc')->paginate(20);

        // Statistics
        $stats = [
            'total_balances' => UserBalance::sum('balance'),
            'total_pending' => UserBalance::sum('pending_balance'),
            'total_earned' => UserBalance::sum('total_earned'),
            'total_withdrawn' => UserBalance::sum('total_withdrawn'),
            'total_fees_collected' => UserBalance::sum('total_fees_paid'),
            'users_with_balance' => UserBalance::where('balance', '>', 0)->count(),
        ];

        return view('pages.admin.balance.index', compact('balances', 'stats'));
    }

    /**
     * Display withdrawal requests
     */
    public function withdrawals(Request $request)
    {
        $query = BalanceTransaction::with(['user'])
            ->where('type', BalanceTransaction::TYPE_WITHDRAWAL);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $withdrawals = $query->orderBy('created_at', 'desc')->paginate(20);

        // Statistics
        $stats = [
            'pending_count' => BalanceTransaction::withdrawals()->pending()->count(),
            'pending_amount' => BalanceTransaction::withdrawals()->pending()->sum(DB::raw('ABS(amount)')),
            'completed_today' => BalanceTransaction::withdrawals()->completed()
                ->whereDate('approved_at', today())->count(),
            'completed_amount_today' => BalanceTransaction::withdrawals()->completed()
                ->whereDate('approved_at', today())->sum(DB::raw('ABS(amount)')),
        ];

        return view('pages.admin.balance.withdrawals', compact('withdrawals', 'stats'));
    }

    /**
     * Approve withdrawal
     */
    public function approveWithdrawal(Request $request, BalanceTransaction $transaction)
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:500'
        ]);

        if (!$transaction->isPending() || !$transaction->isWithdrawal()) {
            return response()->json([
                'success' => false,
                'message' => 'Transaksi tidak dapat disetujui'
            ], 400);
        }

        $success = $transaction->approve(auth()->user(), $request->admin_notes);

        if ($success) {
            // Create notification for user
            \App\Models\Notification::create([
                'user_id' => $transaction->user_id,
                'title' => 'Penarikan Disetujui',
                'message' => "Penarikan sebesar {$transaction->formatted_amount} telah disetujui dan diproses.",
                'type' => 'balance',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'amount' => abs($transaction->amount)
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Penarikan berhasil disetujui'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Gagal menyetujui penarikan'
        ], 500);
    }

    /**
     * Reject withdrawal
     */
    public function rejectWithdrawal(Request $request, BalanceTransaction $transaction)
    {
        $request->validate([
            'admin_notes' => 'required|string|max:500'
        ]);

        if (!$transaction->isPending() || !$transaction->isWithdrawal()) {
            return response()->json([
                'success' => false,
                'message' => 'Transaksi tidak dapat ditolak'
            ], 400);
        }

        $success = $transaction->reject(auth()->user(), $request->admin_notes);

        if ($success) {
            // Create notification for user
            \App\Models\Notification::create([
                'user_id' => $transaction->user_id,
                'title' => 'Penarikan Ditolak',
                'message' => "Penarikan sebesar {$transaction->formatted_amount} ditolak. Alasan: {$request->admin_notes}",
                'type' => 'balance',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'amount' => abs($transaction->amount),
                    'reason' => $request->admin_notes
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Penarikan berhasil ditolak'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Gagal menolak penarikan'
        ], 500);
    }

    /**
     * Bulk approve withdrawals
     */
    public function bulkApproveWithdrawals(Request $request)
    {
        $request->validate([
            'transaction_ids' => 'required|array',
            'transaction_ids.*' => 'exists:balance_transactions,id',
            'admin_notes' => 'nullable|string|max:500'
        ]);

        $transactions = BalanceTransaction::whereIn('id', $request->transaction_ids)
            ->where('type', BalanceTransaction::TYPE_WITHDRAWAL)
            ->where('status', BalanceTransaction::STATUS_PENDING)
            ->get();

        $approved = 0;
        foreach ($transactions as $transaction) {
            if ($transaction->approve(auth()->user(), $request->admin_notes)) {
                $approved++;

                // Create notification
                \App\Models\Notification::create([
                    'user_id' => $transaction->user_id,
                    'title' => 'Penarikan Disetujui',
                    'message' => "Penarikan sebesar {$transaction->formatted_amount} telah disetujui dan diproses.",
                    'type' => 'balance',
                    'data' => [
                        'transaction_id' => $transaction->id,
                        'amount' => abs($transaction->amount)
                    ]
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$approved} penarikan berhasil disetujui"
        ]);
    }

    /**
     * Adjust user balance
     */
    public function adjustBalance(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric',
            'type' => 'required|in:add,deduct',
            'description' => 'required|string|max:255'
        ]);

        $amount = abs($request->amount);
        $userBalance = $user->getBalance();

        try {
            if ($request->type === 'add') {
                $transaction = $userBalance->addBalance($amount, 'adjustment', [
                    'admin_id' => auth()->id(),
                    'reason' => $request->description
                ]);
            } else {
                $transaction = $userBalance->deductBalance($amount, 'adjustment', [
                    'admin_id' => auth()->id(),
                    'reason' => $request->description
                ]);
            }

            // Create notification
            \App\Models\Notification::create([
                'user_id' => $user->id,
                'title' => 'Penyesuaian Saldo',
                'message' => "Saldo Anda telah disesuaikan sebesar {$transaction->formatted_amount}. {$request->description}",
                'type' => 'balance',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Saldo berhasil disesuaikan',
                'new_balance' => $userBalance->fresh()->formatted_balance
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Export balance data
     */
    public function export(Request $request)
    {
        $balances = UserBalance::with(['user'])
            ->whereHas('user', function($q) {
                $q->whereIn('role', ['penjual', 'admin']);
            })
            ->get();

        $data = $balances->map(function($balance) {
            return [
                'user_name' => $balance->user->name,
                'user_email' => $balance->user->email,
                'user_role' => $balance->user->role,
                'balance' => $balance->balance,
                'pending_balance' => $balance->pending_balance,
                'available_balance' => $balance->available_balance,
                'total_earned' => $balance->total_earned,
                'total_deposited' => $balance->total_deposited,
                'total_withdrawn' => $balance->total_withdrawn,
                'total_fees_paid' => $balance->total_fees_paid,
                'created_at' => $balance->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data,
            'filename' => 'user_balances_' . now()->format('Y-m-d_H-i-s') . '.json'
        ]);
    }
}
