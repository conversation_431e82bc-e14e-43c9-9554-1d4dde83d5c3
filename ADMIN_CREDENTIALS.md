# 🔐 Admin Credentials - TiXara Application

## 📋 Current Admin Account

### 👑 **Primary Admin Account**
- **Name:** Admin TiXara
- **Email:** `<EMAIL>`
- **Password:** `TiXara@2024`
- **Role:** Administrator
- **Status:** Active ✅
- **Email Verified:** Yes ✅

## 🚀 How to Login

1. **Access Login Page:**
   ```
   http://127.0.0.1:8000/login
   ```

2. **Enter Credentials:**
   - Email: `<EMAIL>`
   - Password: `TiXara@2024`

3. **Admin Dashboard Access:**
   ```
   http://127.0.0.1:8000/admin/dashboard
   ```

## 🛠️ Admin Management Commands

### Update Admin Password
```bash
# Update all admin users
php artisan admin:password --password=NewPassword123

# Update specific admin user
php artisan admin:password <EMAIL> --password=NewPassword123

# Interactive password update (will prompt for password)
php artisan admin:password
```

### Check Admin Users
```bash
# Using Tinker
php artisan tinker

# Then run:
User::where('role', 'admin')->get(['name', 'email', 'is_active']);
```

## 🔑 Admin Capabilities

The admin account has full access to:

- ✅ **User Management** - Create, edit, delete users
- ✅ **Event Management** - Manage all events and tickets
- ✅ **Category Management** - Create and manage event categories
- ✅ **Payment Management** - View and manage all payments
- ✅ **Notification Management** - Send and manage notifications
- ✅ **System Settings** - Configure application settings
- ✅ **Reports & Analytics** - Access all system reports
- ✅ **Staff Management** - Manage staff accounts

## 🔒 Security Notes

1. **Password Requirements:**
   - Minimum 8 characters
   - Contains uppercase and lowercase letters
   - Contains numbers and special characters
   - Current password: `TiXara@2024` meets all requirements

2. **Account Security:**
   - Account is active and verified
   - Two-factor authentication can be enabled
   - Login attempts are logged

3. **Best Practices:**
   - Change password regularly
   - Use strong, unique passwords
   - Enable two-factor authentication
   - Monitor login logs

## 📱 Access Methods

### Web Browser
- Desktop: Full admin dashboard access
- Mobile: Responsive admin interface

### API Access
- Admin users can access all API endpoints
- Use Sanctum tokens for API authentication

## 🆘 Troubleshooting

### If Login Fails:
1. **Check Credentials:**
   - Email: `<EMAIL>`
   - Password: `TiXara@2024`

2. **Reset Password:**
   ```bash
   php artisan admin:password <EMAIL> --password=TiXara@2024
   ```

3. **Check Account Status:**
   ```bash
   php artisan tinker
   User::where('email', '<EMAIL>')->first(['is_active', 'email_verified_at']);
   ```

### If Account is Locked:
```bash
php artisan tinker
$admin = User::where('email', '<EMAIL>')->first();
$admin->update(['is_active' => true, 'email_verified_at' => now()]);
```

## 📞 Support

For technical support or password reset assistance:
- Check the application logs: `storage/logs/laravel.log`
- Use the admin management commands above
- Contact the development team

---

**Last Updated:** June 1, 2025  
**Password Last Changed:** June 1, 2025  
**Next Recommended Password Change:** July 1, 2025
