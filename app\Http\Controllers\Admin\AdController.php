<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Ad;
use App\Models\AdSubscription;
use App\Models\AdAnalytic;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AdController extends Controller
{
    /**
     * Display ads management dashboard
     */
    public function index(Request $request)
    {
        $query = Ad::with(['advertiser', 'event']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhereHas('advertiser', function ($q2) use ($search) {
                      $q2->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $ads = $query->orderBy('created_at', 'desc')->paginate(15);

        // Statistics
        $stats = [
            'total_ads' => Ad::count(),
            'active_ads' => Ad::where('status', 'approved')->where('is_active', true)->count(),
            'pending_approval' => Ad::where('status', 'pending')->count(),
            'total_revenue' => Ad::sum('spent_amount'),
            'total_impressions' => Ad::sum('impressions'),
            'total_clicks' => Ad::sum('clicks'),
        ];

        return view('pages.admin.ads.index', compact('ads', 'stats'));
    }

    /**
     * Show ad details
     */
    public function show(Ad $ad)
    {
        $ad->load(['advertiser', 'event', 'analytics']);
        
        // Get analytics summary
        $analytics = AdAnalytic::getSummaryForAd($ad->id, now()->subDays(30), now());
        
        return view('pages.admin.ads.show', compact('ad', 'analytics'));
    }

    /**
     * Approve ad
     */
    public function approve(Request $request, Ad $ad)
    {
        $ad->update([
            'status' => 'approved',
            'is_approved' => true,
            'admin_notes' => $request->admin_notes,
        ]);

        return back()->with('success', 'Iklan berhasil disetujui.');
    }

    /**
     * Reject ad
     */
    public function reject(Request $request, Ad $ad)
    {
        $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ]);

        $ad->update([
            'status' => 'rejected',
            'is_approved' => false,
            'admin_notes' => $request->admin_notes,
        ]);

        return back()->with('success', 'Iklan berhasil ditolak.');
    }

    /**
     * Pause ad
     */
    public function pause(Ad $ad)
    {
        $ad->update([
            'status' => 'paused',
            'is_active' => false,
        ]);

        return back()->with('success', 'Iklan berhasil dijeda.');
    }

    /**
     * Resume ad
     */
    public function resume(Ad $ad)
    {
        $ad->update([
            'status' => 'approved',
            'is_active' => true,
        ]);

        return back()->with('success', 'Iklan berhasil dilanjutkan.');
    }

    /**
     * Delete ad
     */
    public function destroy(Ad $ad)
    {
        // Delete image if exists
        if ($ad->image_url) {
            Storage::disk('public')->delete($ad->image_url);
        }

        $ad->delete();

        return back()->with('success', 'Iklan berhasil dihapus.');
    }

    /**
     * Bulk approve ads
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'ad_ids' => 'required|array',
            'ad_ids.*' => 'exists:ads,id',
        ]);

        Ad::whereIn('id', $request->ad_ids)->update([
            'status' => 'approved',
            'is_approved' => true,
        ]);

        return back()->with('success', count($request->ad_ids) . ' iklan berhasil disetujui.');
    }

    /**
     * Bulk reject ads
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'ad_ids' => 'required|array',
            'ad_ids.*' => 'exists:ads,id',
            'admin_notes' => 'required|string|max:1000',
        ]);

        Ad::whereIn('id', $request->ad_ids)->update([
            'status' => 'rejected',
            'is_approved' => false,
            'admin_notes' => $request->admin_notes,
        ]);

        return back()->with('success', count($request->ad_ids) . ' iklan berhasil ditolak.');
    }

    /**
     * Analytics dashboard
     */
    public function analytics(Request $request)
    {
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Overall statistics
        $totalStats = [
            'total_impressions' => AdAnalytic::dateRange($startDate, $endDate)->sum('impressions'),
            'total_clicks' => AdAnalytic::dateRange($startDate, $endDate)->sum('clicks'),
            'total_cost' => AdAnalytic::dateRange($startDate, $endDate)->sum('cost'),
            'average_ctr' => AdAnalytic::dateRange($startDate, $endDate)->avg('ctr') ?: 0,
        ];

        // Top performing ads
        $topAds = AdAnalytic::getTopPerformingAds('clicks', 10, $startDate, $endDate);

        // Daily analytics
        $dailyAnalytics = AdAnalytic::dateRange($startDate, $endDate)
            ->selectRaw('date, SUM(impressions) as impressions, SUM(clicks) as clicks, SUM(cost) as cost')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('pages.admin.ads.analytics', compact(
            'totalStats', 
            'topAds', 
            'dailyAnalytics', 
            'startDate', 
            'endDate'
        ));
    }

    /**
     * Ad subscriptions management
     */
    public function subscriptions(Request $request)
    {
        $query = AdSubscription::with('user');

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('plan_type')) {
            $query->where('plan_type', $request->plan_type);
        }

        $subscriptions = $query->orderBy('created_at', 'desc')->paginate(15);

        $stats = [
            'total_subscriptions' => AdSubscription::count(),
            'active_subscriptions' => AdSubscription::where('status', 'active')->count(),
            'expired_subscriptions' => AdSubscription::expired()->count(),
            'monthly_revenue' => AdSubscription::where('created_at', '>=', now()->startOfMonth())->sum('amount_paid'),
        ];

        return view('pages.admin.ads.subscriptions', compact('subscriptions', 'stats'));
    }

    /**
     * Export ads data
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        
        $ads = Ad::with(['advertiser', 'event'])->get();

        if ($format === 'csv') {
            $filename = 'ads_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
            
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function() use ($ads) {
                $file = fopen('php://output', 'w');
                
                // CSV headers
                fputcsv($file, [
                    'ID', 'Title', 'Advertiser', 'Type', 'Status', 'Impressions', 
                    'Clicks', 'CTR (%)', 'Spent Amount', 'Created At'
                ]);

                // CSV data
                foreach ($ads as $ad) {
                    fputcsv($file, [
                        $ad->id,
                        $ad->title,
                        $ad->advertiser->name,
                        $ad->type,
                        $ad->status,
                        $ad->impressions,
                        $ad->clicks,
                        $ad->ctr,
                        $ad->spent_amount,
                        $ad->created_at->format('Y-m-d H:i:s'),
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }

        return back()->with('error', 'Format export tidak didukung.');
    }
}
