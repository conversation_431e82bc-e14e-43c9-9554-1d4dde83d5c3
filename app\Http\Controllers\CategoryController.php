<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CategoryController extends Controller
{
    /**
     * Display all categories with statistics
     */
    public function index(Request $request)
    {
        $categories = Cache::remember('categories_with_stats', 1800, function () {
            return Category::active()
                ->ordered()
                ->withCount(['tickets as active_events_count' => function ($query) {
                    $query->where('status', 'published')
                          ->where('start_date', '>', now());
                }])
                ->get()
                ->map(function ($category) {
                    $category->total_revenue = $this->getCategoryRevenue($category->id);
                    $category->upcoming_events = $this->getUpcomingEvents($category->id);
                    return $category;
                });
        });

        if ($request->ajax()) {
            return response()->json([
                'categories' => $categories,
                'trending' => $this->getTrendingCategories(),
            ]);
        }

        return view('pages.categories.index', [
            'title' => 'Kategori Event',
            'categories' => $categories,
            'trending' => $this->getTrendingCategories(),
        ]);
    }

    /**
     * Show category detail with events
     */
    public function show(Category $category, Request $request)
    {
        $query = Event::with(['organizer', 'category'])
            ->where('category_id', $category->id)
            ->where('status', 'published')
            ->where('start_date', '>', now());

        // Apply filters
        if ($request->filled('location')) {
            $query->where('city', 'like', '%' . $request->location . '%');
        }

        if ($request->filled('date_range')) {
            $dateRange = $request->date_range;
            switch ($dateRange) {
                case 'today':
                    $query->whereDate('start_date', today());
                    break;
                case 'tomorrow':
                    $query->whereDate('start_date', today()->addDay());
                    break;
                case 'this_week':
                    $query->whereBetween('start_date', [now(), now()->endOfWeek()]);
                    break;
                case 'this_month':
                    $query->whereBetween('start_date', [now(), now()->endOfMonth()]);
                    break;
            }
        }

        if ($request->filled('price_range')) {
            $priceRange = $request->price_range;
            switch ($priceRange) {
                case 'free':
                    $query->where('price', 0);
                    break;
                case 'under_100k':
                    $query->where('price', '<', 100000);
                    break;
                case '100k_500k':
                    $query->whereBetween('price', [100000, 500000]);
                    break;
                case 'above_500k':
                    $query->where('price', '>', 500000);
                    break;
            }
        }

        // Sort options
        $sortBy = $request->get('sort', 'date');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('available_capacity', 'desc');
                break;
            case 'date':
            default:
                $query->orderBy('start_date', 'asc');
                break;
        }

        $events = $query->paginate(12)->withQueryString();

        // Category statistics
        $stats = [
            'total_events' => $category->tickets()->where('status', 'published')->count(),
            'upcoming_events' => $category->tickets()
                ->where('status', 'published')
                ->where('start_date', '>', now())
                ->count(),
            'total_revenue' => $this->getCategoryRevenue($category->id),
            'avg_price' => $category->tickets()
                ->where('status', 'published')
                ->avg('price'),
        ];

        if ($request->ajax()) {
            return response()->json([
                'events' => $events,
                'stats' => $stats,
            ]);
        }

        return view('pages.categories.show', [
            'title' => $category->name,
            'category' => $category,
            'events' => $events,
            'stats' => $stats,
            'filters' => $request->only(['location', 'date_range', 'price_range', 'sort']),
        ]);
    }

    /**
     * Get trending categories based on recent activity
     */
    private function getTrendingCategories()
    {
        return Cache::remember('trending_categories', 3600, function () {
            return Category::select('categories.*')
                ->join('events', 'categories.id', '=', 'events.category_id')
                ->join('orders', 'events.id', '=', 'orders.event_id')
                ->where('orders.created_at', '>=', now()->subDays(7))
                ->where('orders.payment_status', 'paid')
                ->groupBy('categories.id')
                ->orderByRaw('COUNT(orders.id) DESC')
                ->limit(5)
                ->get();
        });
    }

    /**
     * Get category revenue
     */
    private function getCategoryRevenue($categoryId)
    {
        return DB::table('orders')
            ->join('events', 'orders.event_id', '=', 'events.id')
            ->where('events.category_id', $categoryId)
            ->where('orders.payment_status', 'paid')
            ->sum('orders.total_amount');
    }

    /**
     * Get upcoming events count for category
     */
    private function getUpcomingEvents($categoryId)
    {
        return Event::where('category_id', $categoryId)
            ->where('status', 'published')
            ->where('start_date', '>', now())
            ->count();
    }

    /**
     * Get category recommendations based on user preferences
     */
    public function recommendations(Request $request)
    {
        $user = auth()->user();
        
        if (!$user) {
            // For guest users, return popular categories
            $categories = $this->getTrendingCategories();
        } else {
            // For authenticated users, get recommendations based on their order history
            $userCategories = DB::table('orders')
                ->join('events', 'orders.event_id', '=', 'events.id')
                ->join('categories', 'events.category_id', '=', 'categories.id')
                ->where('orders.user_id', $user->id)
                ->where('orders.payment_status', 'paid')
                ->select('categories.*')
                ->groupBy('categories.id')
                ->orderByRaw('COUNT(orders.id) DESC')
                ->limit(3)
                ->get();

            if ($userCategories->isEmpty()) {
                $categories = $this->getTrendingCategories();
            } else {
                $categories = $userCategories;
            }
        }

        return response()->json([
            'recommendations' => $categories,
        ]);
    }

    /**
     * Search categories
     */
    public function search(Request $request)
    {
        $query = $request->get('q');
        
        if (empty($query)) {
            return response()->json([
                'categories' => [],
                'message' => 'Query tidak boleh kosong'
            ], 400);
        }

        $categories = Category::active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%');
            })
            ->withCount(['tickets as events_count' => function ($q) {
                $q->where('status', 'published')
                  ->where('start_date', '>', now());
            }])
            ->ordered()
            ->limit(10)
            ->get();

        return response()->json([
            'categories' => $categories,
        ]);
    }

    /**
     * Get category analytics for admin
     */
    public function analytics(Category $category)
    {
        $this->authorize('viewAny', Event::class); // Only admin can view analytics

        $analytics = [
            'events_by_month' => $this->getEventsByMonth($category->id),
            'revenue_by_month' => $this->getRevenueByMonth($category->id),
            'top_organizers' => $this->getTopOrganizers($category->id),
            'price_distribution' => $this->getPriceDistribution($category->id),
            'location_distribution' => $this->getLocationDistribution($category->id),
        ];

        return response()->json($analytics);
    }

    /**
     * Get events by month for analytics
     */
    private function getEventsByMonth($categoryId)
    {
        return Event::where('category_id', $categoryId)
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupByRaw('YEAR(created_at), MONTH(created_at)')
            ->orderByRaw('YEAR(created_at), MONTH(created_at)')
            ->get();
    }

    /**
     * Get revenue by month for analytics
     */
    private function getRevenueByMonth($categoryId)
    {
        return DB::table('orders')
            ->join('events', 'orders.event_id', '=', 'events.id')
            ->where('events.category_id', $categoryId)
            ->where('orders.payment_status', 'paid')
            ->where('orders.created_at', '>=', now()->subMonths(12))
            ->selectRaw('MONTH(orders.created_at) as month, YEAR(orders.created_at) as year, SUM(orders.total_amount) as revenue')
            ->groupByRaw('YEAR(orders.created_at), MONTH(orders.created_at)')
            ->orderByRaw('YEAR(orders.created_at), MONTH(orders.created_at)')
            ->get();
    }

    /**
     * Get top organizers for category
     */
    private function getTopOrganizers($categoryId)
    {
        return DB::table('events')
            ->join('users', 'events.organizer_id', '=', 'users.id')
            ->where('events.category_id', $categoryId)
            ->where('events.status', 'published')
            ->select('users.name', 'users.email', DB::raw('COUNT(events.id) as events_count'))
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderByDesc('events_count')
            ->limit(10)
            ->get();
    }

    /**
     * Get price distribution for category
     */
    private function getPriceDistribution($categoryId)
    {
        return Event::where('category_id', $categoryId)
            ->where('status', 'published')
            ->selectRaw('
                CASE 
                    WHEN price = 0 THEN "Gratis"
                    WHEN price < 100000 THEN "< 100K"
                    WHEN price BETWEEN 100000 AND 500000 THEN "100K - 500K"
                    WHEN price > 500000 THEN "> 500K"
                END as price_range,
                COUNT(*) as count
            ')
            ->groupByRaw('
                CASE 
                    WHEN price = 0 THEN "Gratis"
                    WHEN price < 100000 THEN "< 100K"
                    WHEN price BETWEEN 100000 AND 500000 THEN "100K - 500K"
                    WHEN price > 500000 THEN "> 500K"
                END
            ')
            ->get();
    }

    /**
     * Get location distribution for category
     */
    private function getLocationDistribution($categoryId)
    {
        return Event::where('category_id', $categoryId)
            ->where('status', 'published')
            ->select('city', DB::raw('COUNT(*) as count'))
            ->groupBy('city')
            ->orderByDesc('count')
            ->limit(10)
            ->get();
    }
}
