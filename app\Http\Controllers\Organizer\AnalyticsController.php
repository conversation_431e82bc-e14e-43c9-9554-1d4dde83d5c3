<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    public function index(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        // Revenue Analytics
        $revenueData = $this->getRevenueAnalytics($startDate, $endDate);
        
        // Event Analytics
        $eventAnalytics = $this->getEventAnalytics($startDate, $endDate);
        
        // Sales Analytics
        $salesAnalytics = $this->getSalesAnalytics($startDate, $endDate);
        
        // Top Events
        $topEvents = $this->getTopEvents($startDate, $endDate);

        return view('pages.organizer.analytics.index', compact(
            'revenueData',
            'eventAnalytics',
            'salesAnalytics',
            'topEvents',
            'dateRange'
        ));
    }

    public function revenue(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $revenueData = $this->getRevenueAnalytics($startDate, $endDate);
        
        return response()->json($revenueData);
    }

    public function events(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $eventAnalytics = $this->getEventAnalytics($startDate, $endDate);
        
        return response()->json($eventAnalytics);
    }

    public function export(Request $request)
    {
        $dateRange = $request->get('range', 30);
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        $data = [
            'revenue' => $this->getRevenueAnalytics($startDate, $endDate),
            'events' => $this->getEventAnalytics($startDate, $endDate),
            'sales' => $this->getSalesAnalytics($startDate, $endDate),
            'top_events' => $this->getTopEvents($startDate, $endDate),
        ];

        $filename = 'organizer_analytics_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.json';
        
        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    private function getRevenueAnalytics($startDate, $endDate)
    {
        $totalRevenue = Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_amount');

        $dailyRevenue = Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $previousPeriodRevenue = Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate->copy()->subDays($endDate->diffInDays($startDate)), $startDate])
            ->sum('total_amount');

        $growthRate = $previousPeriodRevenue > 0 
            ? (($totalRevenue - $previousPeriodRevenue) / $previousPeriodRevenue) * 100 
            : 0;

        return [
            'total_revenue' => $totalRevenue,
            'daily_revenue' => $dailyRevenue,
            'growth_rate' => round($growthRate, 2),
            'previous_period_revenue' => $previousPeriodRevenue
        ];
    }

    private function getEventAnalytics($startDate, $endDate)
    {
        $totalEvents = Event::where('user_id', Auth::id())
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $publishedEvents = Event::where('user_id', Auth::id())
            ->where('status', 'published')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $upcomingEvents = Event::where('user_id', Auth::id())
            ->where('event_date', '>', Carbon::now())
            ->count();

        $pastEvents = Event::where('user_id', Auth::id())
            ->where('event_date', '<', Carbon::now())
            ->count();

        return [
            'total_events' => $totalEvents,
            'published_events' => $publishedEvents,
            'upcoming_events' => $upcomingEvents,
            'past_events' => $pastEvents
        ];
    }

    private function getSalesAnalytics($startDate, $endDate)
    {
        $totalTicketsSold = Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('quantity');

        $totalOrders = Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $completedOrders = Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $conversionRate = $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0;

        $dailySales = Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, SUM(quantity) as tickets_sold, COUNT(*) as orders_count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total_tickets_sold' => $totalTicketsSold,
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'conversion_rate' => round($conversionRate, 2),
            'daily_sales' => $dailySales
        ];
    }

    private function getTopEvents($startDate, $endDate)
    {
        return Event::where('user_id', Auth::id())
            ->withCount(['orders' => function($query) use ($startDate, $endDate) {
                $query->where('status', 'completed')
                      ->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->withSum(['orders' => function($query) use ($startDate, $endDate) {
                $query->where('status', 'completed')
                      ->whereBetween('created_at', [$startDate, $endDate]);
            }], 'total_amount')
            ->withSum(['orders' => function($query) use ($startDate, $endDate) {
                $query->where('status', 'completed')
                      ->whereBetween('created_at', [$startDate, $endDate]);
            }], 'quantity')
            ->orderBy('orders_count', 'desc')
            ->take(10)
            ->get();
    }
}
