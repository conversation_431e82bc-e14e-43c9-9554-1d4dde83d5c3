<?php

namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentGatewayService
{
    protected $xenditSecretKey;
    protected $midtransServerKey;
    protected $tripayApiKey;
    protected $tripayPrivateKey;
    protected $tripayMerchantCode;

    public function __construct()
    {
        $this->xenditSecretKey = config('services.xendit.secret_key');
        $this->midtransServerKey = config('services.midtrans.server_key');
        $this->tripayApiKey = config('services.tripay.api_key');
        $this->tripayPrivateKey = config('services.tripay.private_key');
        $this->tripayMerchantCode = config('services.tripay.merchant_code');
    }

    /**
     * Process payment based on selected method and gateway
     */
    public function processPayment(Order $order, string $paymentMethod, array $paymentDetails = [])
    {
        try {
            // Determine which gateway to use based on payment method
            $gateway = $this->determineGateway($paymentMethod);
            
            switch ($gateway) {
                case 'xendit':
                    return $this->processXenditPayment($order, $paymentMethod, $paymentDetails);
                    
                case 'midtrans':
                    return $this->processMidtransPayment($order, $paymentMethod, $paymentDetails);
                    
                case 'tripay':
                    return $this->processTripayPayment($order, $paymentMethod, $paymentDetails);
                    
                default:
                    return $this->processManualPayment($order, $paymentMethod, $paymentDetails);
            }
        } catch (Exception $e) {
            Log::error('Payment processing failed', [
                'order_id' => $order->id,
                'payment_method' => $paymentMethod,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan saat memproses pembayaran: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Determine which gateway to use for payment method
     */
    private function determineGateway(string $paymentMethod): string
    {
        $gatewayMapping = [
            'e_wallet' => 'xendit',      // Xendit for e-wallets (GoPay, OVO, DANA)
            'qris' => 'xendit',          // Xendit for QRIS
            'virtual_account' => 'xendit', // Xendit for Virtual Account
            'credit_card' => 'midtrans',  // Midtrans for Credit Card
            'bank_transfer' => 'tripay',  // Tripay for Bank Transfer
            'cash' => 'manual',          // Manual processing for cash
        ];

        return $gatewayMapping[$paymentMethod] ?? 'manual';
    }

    /**
     * Process payment via Xendit
     */
    private function processXenditPayment(Order $order, string $paymentMethod, array $paymentDetails)
    {
        $xenditUrl = config('services.xendit.environment') === 'live' 
            ? 'https://api.xendit.co' 
            : 'https://api.xendit.co';

        switch ($paymentMethod) {
            case 'e_wallet':
                return $this->createXenditEWallet($order, $paymentDetails);
                
            case 'qris':
                return $this->createXenditQRIS($order);
                
            case 'virtual_account':
                return $this->createXenditVirtualAccount($order, $paymentDetails);
                
            default:
                throw new Exception('Unsupported payment method for Xendit');
        }
    }

    /**
     * Create Xendit E-Wallet payment
     */
    private function createXenditEWallet(Order $order, array $paymentDetails)
    {
        $response = Http::withBasicAuth($this->xenditSecretKey, '')
            ->post('https://api.xendit.co/ewallets/charges', [
                'reference_id' => $order->order_number,
                'currency' => 'IDR',
                'amount' => $order->total_amount,
                'checkout_method' => 'ONE_TIME_PAYMENT',
                'channel_code' => strtoupper($paymentDetails['provider'] ?? 'GOPAY'),
                'channel_properties' => [
                    'success_redirect_url' => route('orders.success', $order),
                    'failure_redirect_url' => route('orders.payment', $order),
                ],
                'customer' => [
                    'reference_id' => 'customer_' . $order->user_id,
                    'type' => 'INDIVIDUAL',
                    'individual_detail' => [
                        'given_names' => $order->customer_name,
                    ]
                ],
                'metadata' => [
                    'order_id' => $order->id,
                    'event_title' => $order->event->title,
                ]
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return [
                'success' => true,
                'reference' => $data['id'],
                'message' => 'E-Wallet payment created successfully',
                'data' => [
                    'payment_url' => $data['actions']['desktop_web_checkout_url'] ?? $data['actions']['mobile_web_checkout_url'],
                    'qr_code' => $data['actions']['qr_checkout_string'] ?? null,
                    'gateway_response' => $data,
                ]
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create e-wallet payment: ' . $response->body(),
            'data' => null
        ];
    }

    /**
     * Create Xendit QRIS payment
     */
    private function createXenditQRIS(Order $order)
    {
        $response = Http::withBasicAuth($this->xenditSecretKey, '')
            ->post('https://api.xendit.co/qr_codes', [
                'external_id' => $order->order_number,
                'type' => 'DYNAMIC',
                'callback_url' => route('webhooks.xendit'),
                'amount' => $order->total_amount,
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return [
                'success' => true,
                'reference' => $data['id'],
                'message' => 'QRIS payment created successfully',
                'data' => [
                    'qr_string' => $data['qr_string'],
                    'qr_code_url' => $data['qr_code_url'],
                    'gateway_response' => $data,
                ]
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create QRIS payment: ' . $response->body(),
            'data' => null
        ];
    }

    /**
     * Create Xendit Virtual Account
     */
    private function createXenditVirtualAccount(Order $order, array $paymentDetails)
    {
        $bankCode = strtoupper($paymentDetails['va_bank'] ?? 'BCA');
        
        $response = Http::withBasicAuth($this->xenditSecretKey, '')
            ->post('https://api.xendit.co/virtual_accounts', [
                'external_id' => $order->order_number,
                'bank_code' => $bankCode,
                'name' => $order->customer_name,
                'expected_amount' => $order->total_amount,
                'expiration_date' => $order->expires_at->toISOString(),
                'is_closed' => true,
                'description' => 'Payment for ' . $order->event->title,
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return [
                'success' => true,
                'reference' => $data['id'],
                'message' => 'Virtual Account created successfully',
                'data' => [
                    'account_number' => $data['account_number'],
                    'bank_code' => $data['bank_code'],
                    'gateway_response' => $data,
                ]
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create virtual account: ' . $response->body(),
            'data' => null
        ];
    }

    /**
     * Process payment via Midtrans
     */
    private function processMidtransPayment(Order $order, string $paymentMethod, array $paymentDetails)
    {
        $midtransUrl = config('services.midtrans.environment') === 'production' 
            ? 'https://api.midtrans.com/v2' 
            : 'https://api.sandbox.midtrans.com/v2';

        $response = Http::withBasicAuth($this->midtransServerKey, '')
            ->post($midtransUrl . '/charge', [
                'payment_type' => 'credit_card',
                'transaction_details' => [
                    'order_id' => $order->order_number,
                    'gross_amount' => $order->total_amount,
                ],
                'credit_card' => [
                    'token_id' => $paymentDetails['token_id'] ?? null,
                    'authentication' => config('services.midtrans.3ds'),
                ],
                'customer_details' => [
                    'first_name' => $order->customer_name,
                    'email' => $order->customer_email,
                ],
                'item_details' => [
                    [
                        'id' => $order->event->id,
                        'price' => $order->subtotal,
                        'quantity' => $order->quantity,
                        'name' => $order->event->title,
                    ],
                    [
                        'id' => 'admin_fee',
                        'price' => $order->admin_fee,
                        'quantity' => 1,
                        'name' => 'Admin Fee',
                    ]
                ],
                'callbacks' => [
                    'finish' => route('orders.success', $order),
                ]
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return [
                'success' => $data['transaction_status'] === 'capture',
                'reference' => $data['transaction_id'],
                'message' => 'Credit card payment processed',
                'data' => [
                    'redirect_url' => $data['redirect_url'] ?? null,
                    'gateway_response' => $data,
                ]
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to process credit card payment: ' . $response->body(),
            'data' => null
        ];
    }

    /**
     * Process payment via Tripay
     */
    private function processTripayPayment(Order $order, string $paymentMethod, array $paymentDetails)
    {
        $tripayUrl = config('services.tripay.environment') === 'production' 
            ? 'https://tripay.co.id/api' 
            : 'https://tripay.co.id/api-sandbox';

        // Get available payment channels
        $channelCode = $this->getTripayChannelCode($paymentMethod, $paymentDetails);
        
        $data = [
            'method' => $channelCode,
            'merchant_ref' => $order->order_number,
            'amount' => $order->total_amount,
            'customer_name' => $order->customer_name,
            'customer_email' => $order->customer_email,
            'order_items' => [
                [
                    'sku' => $order->event->id,
                    'name' => $order->event->title,
                    'price' => $order->subtotal,
                    'quantity' => $order->quantity,
                ],
                [
                    'sku' => 'admin_fee',
                    'name' => 'Admin Fee',
                    'price' => $order->admin_fee,
                    'quantity' => 1,
                ]
            ],
            'return_url' => route('orders.success', $order),
            'expired_time' => $order->expires_at->timestamp,
            'signature' => $this->generateTripaySignature($order->order_number, $order->total_amount),
        ];

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->tripayApiKey,
        ])->post($tripayUrl . '/transaction/create', $data);

        if ($response->successful()) {
            $responseData = $response->json();
            if ($responseData['success']) {
                return [
                    'success' => true,
                    'reference' => $responseData['data']['reference'],
                    'message' => 'Bank transfer payment created successfully',
                    'data' => [
                        'payment_url' => $responseData['data']['checkout_url'],
                        'virtual_account' => $responseData['data']['pay_code'] ?? null,
                        'gateway_response' => $responseData['data'],
                    ]
                ];
            }
        }

        return [
            'success' => false,
            'message' => 'Failed to create bank transfer payment: ' . $response->body(),
            'data' => null
        ];
    }

    /**
     * Generate Tripay signature
     */
    private function generateTripaySignature(string $merchantRef, int $amount): string
    {
        $signature = hash_hmac('sha256', $this->tripayMerchantCode . $merchantRef . $amount, $this->tripayPrivateKey);
        return $signature;
    }

    /**
     * Get Tripay channel code based on payment method
     */
    private function getTripayChannelCode(string $paymentMethod, array $paymentDetails): string
    {
        $channelMapping = [
            'bank_transfer' => [
                'BCA' => 'BCAVA',
                'MANDIRI' => 'MANDIRIVA',
                'BNI' => 'BNIVA',
                'BRI' => 'BRIVA',
            ]
        ];

        if ($paymentMethod === 'bank_transfer') {
            $bank = $paymentDetails['bank'] ?? 'BCA';
            return $channelMapping['bank_transfer'][$bank] ?? 'BCAVA';
        }

        return 'BCAVA'; // Default
    }

    /**
     * Process manual payment (for cash payments)
     */
    private function processManualPayment(Order $order, string $paymentMethod, array $paymentDetails)
    {
        if ($paymentMethod === 'cash') {
            return [
                'success' => true,
                'reference' => 'CASH-' . $order->order_number,
                'message' => 'Cash payment registered. Please pay at the event venue.',
                'data' => [
                    'payment_method' => 'cash',
                    'instructions' => 'Bayar saat check-in di lokasi event',
                    'requirements' => [
                        'Datang 30 menit sebelum event',
                        'Bawa tiket digital dan identitas',
                        'Siapkan uang pas',
                        'Konfirmasi kehadiran H-1'
                    ]
                ]
            ];
        }

        return [
            'success' => false,
            'message' => 'Unsupported payment method',
            'data' => null
        ];
    }
}
