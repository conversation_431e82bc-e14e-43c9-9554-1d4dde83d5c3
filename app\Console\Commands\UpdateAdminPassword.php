<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UpdateAdminPassword extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:password {email?} {--password=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update admin user password';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->option('password');

        // If no email provided, update all admin users
        if (!$email) {
            return $this->updateAllAdmins($password);
        }

        // Update specific admin user
        return $this->updateSpecificAdmin($email, $password);
    }

    /**
     * Update all admin users
     */
    private function updateAllAdmins($password = null)
    {
        if (!$password) {
            $password = $this->secret('Enter new password for all admin users');
        }

        if (strlen($password) < 8) {
            $this->error('Password must be at least 8 characters long!');
            return 1;
        }

        $admins = User::where('role', User::ROLE_ADMIN)->get();

        if ($admins->isEmpty()) {
            $this->error('No admin users found!');
            return 1;
        }

        $this->info("Found {$admins->count()} admin user(s):");
        
        foreach ($admins as $admin) {
            $this->line("- {$admin->name} ({$admin->email})");
        }

        if (!$this->confirm('Do you want to update password for all these admin users?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $updated = 0;
        foreach ($admins as $admin) {
            $admin->update([
                'password' => Hash::make($password),
                'is_active' => true,
                'email_verified_at' => $admin->email_verified_at ?? now()
            ]);
            
            $this->info("✅ Updated: {$admin->name} ({$admin->email})");
            $updated++;
        }

        $this->info("\n🎉 Successfully updated {$updated} admin user(s)!");
        $this->warn("New password: {$password}");
        
        return 0;
    }

    /**
     * Update specific admin user
     */
    private function updateSpecificAdmin($email, $password = null)
    {
        $admin = User::where('email', $email)
                    ->where('role', User::ROLE_ADMIN)
                    ->first();

        if (!$admin) {
            $this->error("Admin user with email '{$email}' not found!");
            return 1;
        }

        if (!$password) {
            $password = $this->secret("Enter new password for {$admin->name} ({$email})");
        }

        if (strlen($password) < 8) {
            $this->error('Password must be at least 8 characters long!');
            return 1;
        }

        $admin->update([
            'password' => Hash::make($password),
            'is_active' => true,
            'email_verified_at' => $admin->email_verified_at ?? now()
        ]);

        $this->info("✅ Password updated successfully!");
        $this->info("User: {$admin->name} ({$admin->email})");
        $this->warn("New password: {$password}");

        return 0;
    }
}
