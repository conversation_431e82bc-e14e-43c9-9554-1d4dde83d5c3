<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_type',
        'plan_price',
        'start_date',
        'end_date',
        'status',
        'auto_renew',
        'plan_features',
        'payment_method',
        'payment_reference',
        'payment_data',
        'paid_at',
        'next_billing_date',
        'cancelled_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'plan_price' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'plan_features' => 'array',
        'payment_data' => 'array',
        'paid_at' => 'datetime',
        'next_billing_date' => 'datetime',
        'cancelled_at' => 'datetime',
        'auto_renew' => 'boolean',
    ];

    // Plan types
    const PLAN_MONTHLY = 'monthly';
    const PLAN_QUARTERLY = 'quarterly';
    const PLAN_SEMI_ANNUAL = 'semi_annual';
    const PLAN_ANNUAL = 'annual';

    // Subscription statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_EXPIRED = 'expired';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_PENDING = 'pending';

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if subscription is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE && 
               $this->end_date >= now()->toDateString();
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(): bool
    {
        return $this->end_date < now()->toDateString();
    }

    /**
     * Check if subscription is about to expire (within 7 days)
     */
    public function isExpiringSoon(): bool
    {
        return $this->end_date <= now()->addDays(7)->toDateString() && 
               $this->end_date >= now()->toDateString();
    }

    /**
     * Get days remaining
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }
        
        return now()->diffInDays($this->end_date);
    }

    /**
     * Get plan duration in months
     */
    public function getPlanDurationAttribute(): int
    {
        return match($this->plan_type) {
            self::PLAN_MONTHLY => 1,
            self::PLAN_QUARTERLY => 3,
            self::PLAN_SEMI_ANNUAL => 6,
            self::PLAN_ANNUAL => 12,
            default => 1
        };
    }

    /**
     * Get plan type label
     */
    public function getPlanTypeLabelAttribute(): string
    {
        return match($this->plan_type) {
            self::PLAN_MONTHLY => 'Bulanan',
            self::PLAN_QUARTERLY => '3 Bulan',
            self::PLAN_SEMI_ANNUAL => '6 Bulan',
            self::PLAN_ANNUAL => 'Tahunan',
            default => 'Unknown'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'Aktif',
            self::STATUS_EXPIRED => 'Kedaluwarsa',
            self::STATUS_CANCELLED => 'Dibatalkan',
            self::STATUS_PENDING => 'Menunggu',
            default => 'Unknown'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'success',
            self::STATUS_EXPIRED => 'danger',
            self::STATUS_CANCELLED => 'secondary',
            self::STATUS_PENDING => 'warning',
            default => 'secondary'
        };
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'Rp ' . number_format($this->plan_price, 0, ',', '.');
    }

    /**
     * Activate subscription
     */
    public function activate(): bool
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'paid_at' => now(),
            'next_billing_date' => $this->auto_renew ? 
                Carbon::parse($this->end_date)->addMonths($this->plan_duration) : null,
        ]);

        return true;
    }

    /**
     * Cancel subscription
     */
    public function cancel(string $reason = null): bool
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'auto_renew' => false,
            'next_billing_date' => null,
        ]);

        return true;
    }

    /**
     * Renew subscription
     */
    public function renew(): UserSubscription
    {
        $newStartDate = Carbon::parse($this->end_date)->addDay();
        $newEndDate = $newStartDate->copy()->addMonths($this->plan_duration);

        return self::create([
            'user_id' => $this->user_id,
            'plan_type' => $this->plan_type,
            'plan_price' => $this->plan_price,
            'start_date' => $newStartDate,
            'end_date' => $newEndDate,
            'status' => self::STATUS_PENDING,
            'auto_renew' => $this->auto_renew,
            'plan_features' => $this->plan_features,
        ]);
    }

    /**
     * Check if user has feature
     */
    public function hasFeature(string $feature): bool
    {
        if (!$this->isActive()) {
            return false;
        }

        return in_array($feature, $this->plan_features ?? []);
    }

    /**
     * Scope for active subscriptions
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->where('end_date', '>=', now()->toDateString());
    }

    /**
     * Scope for expiring subscriptions
     */
    public function scopeExpiring($query, int $days = 7)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->whereBetween('end_date', [
                        now()->toDateString(),
                        now()->addDays($days)->toDateString()
                    ]);
    }

    /**
     * Scope for expired subscriptions
     */
    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now()->toDateString());
    }

    /**
     * Get available subscription plans
     */
    public static function getAvailablePlans(): array
    {
        return [
            self::PLAN_MONTHLY => [
                'name' => 'Bulanan',
                'duration' => 1,
                'price' => 50000,
                'features' => ['no_transaction_fees', 'priority_support', 'analytics'],
            ],
            self::PLAN_QUARTERLY => [
                'name' => '3 Bulan',
                'duration' => 3,
                'price' => 135000, // 10% discount
                'features' => ['no_transaction_fees', 'priority_support', 'analytics', 'custom_branding'],
            ],
            self::PLAN_SEMI_ANNUAL => [
                'name' => '6 Bulan',
                'duration' => 6,
                'price' => 255000, // 15% discount
                'features' => ['no_transaction_fees', 'priority_support', 'analytics', 'custom_branding', 'advanced_reports'],
            ],
            self::PLAN_ANNUAL => [
                'name' => 'Tahunan',
                'duration' => 12,
                'price' => 480000, // 20% discount
                'features' => ['no_transaction_fees', 'priority_support', 'analytics', 'custom_branding', 'advanced_reports', 'api_access'],
            ],
        ];
    }
}
