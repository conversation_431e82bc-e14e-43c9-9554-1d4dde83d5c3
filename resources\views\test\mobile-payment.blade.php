<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Payment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ asset('css/mobile-payment.css') }}">
</head>
<body class="bg-gray-50">
    <!-- Mobile Test for Payment Summary -->
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-4 text-center">
            <h1 class="text-xl font-bold">Test Mobile Payment</h1>
            <p class="text-sm opacity-90">Order #TIX-2024-001</p>
        </div>

        <!-- Mobile Order Summary -->
        <div class="p-4">
            <div class="bg-white rounded-2xl shadow-lg p-4 mb-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4"><PERSON><PERSON><PERSON></h3>
                
                <!-- Event Info -->
                <div class="mb-4">
                    <h4 class="font-semibold text-gray-900 mb-2 text-sm">Konser Musik Jazz Festival 2024</h4>
                    <div class="text-xs text-gray-600 space-y-1">
                        <p>📅 25 Des 2024, 19:00 WIB</p>
                        <p>📍 Jakarta Convention Center, Jakarta</p>
                        <p>👤 John Doe</p>
                        <p>🎫 2 tiket</p>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="space-y-2 mb-4 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal</span>
                        <span>Rp 500.000</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Biaya admin</span>
                        <span>Rp 5.000</span>
                    </div>
                    <div class="border-t border-gray-200 pt-2">
                        <div class="flex justify-between items-center">
                            <span class="font-bold text-gray-900">Total</span>
                            <span class="font-bold text-blue-600 text-lg">Rp 505.000</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="bg-gray-50 rounded-lg p-3 text-sm">
                    <div class="text-gray-600 mb-1">Metode Pembayaran</div>
                    <div class="font-semibold text-gray-900">QRIS</div>
                </div>
            </div>

            <!-- QRIS Payment -->
            <div class="bg-white rounded-2xl shadow-lg p-4 mb-6">
                <h2 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                    <span class="text-sm">Scan QR Code QRIS</span>
                </h2>

                <div class="text-center">
                    <!-- Responsive QR Code Container -->
                    <div class="w-48 h-48 mx-auto bg-white border-2 border-gray-200 rounded-lg p-3 mb-4">
                        <div class="w-full h-full bg-gray-100 rounded flex items-center justify-center">
                            <span class="text-gray-500 text-sm">QR Code</span>
                        </div>
                    </div>

                    <!-- Mobile-friendly instructions -->
                    <div class="space-y-2 text-xs text-gray-700">
                        <p class="flex items-center justify-center flex-wrap">
                            <svg class="w-3 h-3 mr-1 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-center">Scan dengan aplikasi bank atau e-wallet apapun</span>
                        </p>
                        <p class="flex items-center justify-center flex-wrap">
                            <svg class="w-3 h-3 mr-1 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-center">Pembayaran akan dikonfirmasi otomatis</span>
                        </p>
                    </div>

                    <!-- Mobile payment apps suggestion -->
                    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                        <p class="text-xs text-blue-800 font-medium mb-2">Aplikasi yang mendukung QRIS:</p>
                        <div class="flex flex-wrap justify-center gap-2 text-xs text-blue-700">
                            <span class="bg-white px-2 py-1 rounded">GoPay</span>
                            <span class="bg-white px-2 py-1 rounded">OVO</span>
                            <span class="bg-white px-2 py-1 rounded">DANA</span>
                            <span class="bg-white px-2 py-1 rounded">ShopeePay</span>
                            <span class="bg-white px-2 py-1 rounded">LinkAja</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Status Check -->
            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-4">
                <h3 class="text-base font-bold text-yellow-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Status Pembayaran
                </h3>
                <div class="flex flex-col space-y-3">
                    <div class="flex-1">
                        <p class="text-yellow-800 text-sm">Menunggu pembayaran...</p>
                        <p class="text-xs text-yellow-700">Halaman akan otomatis refresh setelah pembayaran berhasil</p>
                    </div>
                    <button class="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-yellow-700 transition-colors w-full">
                        Cek Status
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Status Indicator (Fixed) -->
        <div class="status-indicator-mobile">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-gray-900">Menunggu pembayaran</span>
                </div>
                <span class="text-xs text-gray-600">15m 30s</span>
            </div>
        </div>
    </div>

    <script>
        // Test responsive behavior
        console.log('Mobile payment test loaded');
        console.log('Screen width:', window.innerWidth);
        console.log('Is mobile:', window.innerWidth <= 768);
    </script>
</body>
</html>
