@props([
    'title',
    'value',
    'icon',
    'iconColor' => 'blue',
    'change' => null,
    'changeType' => 'positive', // positive, negative, neutral
    'changeLabel' => null,
    'trend' => null, // up, down, flat
    'aos' => null,
    'aosDelay' => null
])

@php
    $iconColors = [
        'blue' => 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
        'green' => 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
        'yellow' => 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400',
        'red' => 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
        'purple' => 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400',
        'orange' => 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400',
        'pink' => 'bg-pink-100 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400',
        'indigo' => 'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400',
        'gray' => 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400',
    ];
    
    $changeColors = [
        'positive' => 'text-green-600 dark:text-green-400',
        'negative' => 'text-red-600 dark:text-red-400',
        'neutral' => 'text-gray-600 dark:text-gray-400'
    ];
    
    $trendIcons = [
        'up' => 'trending-up',
        'down' => 'trending-down',
        'flat' => 'minus'
    ];
    
    $aosAttributes = '';
    if ($aos) {
        $aosAttributes .= ' data-aos="' . $aos . '"';
        if ($aosDelay) {
            $aosAttributes .= ' data-aos-delay="' . $aosDelay . '"';
        }
    }
@endphp

<div {{ $attributes->merge(['class' => 'bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover-lift transition-all duration-200']) }}{!! $aosAttributes !!}>
    <div class="flex items-center justify-between">
        <div class="flex-1">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $title }}</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">{{ $value }}</p>
            
            @if($change || $changeLabel)
                <div class="flex items-center mt-2 space-x-1">
                    @if($trend && isset($trendIcons[$trend]))
                        <i data-lucide="{{ $trendIcons[$trend] }}" class="w-4 h-4 {{ $changeColors[$changeType] ?? $changeColors['neutral'] }}"></i>
                    @endif
                    
                    @if($change)
                        <span class="text-sm font-medium {{ $changeColors[$changeType] ?? $changeColors['neutral'] }}">
                            {{ $change }}
                        </span>
                    @endif
                    
                    @if($changeLabel)
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ $changeLabel }}</span>
                    @endif
                </div>
            @endif
        </div>
        
        <div class="w-12 h-12 {{ $iconColors[$iconColor] ?? $iconColors['blue'] }} rounded-lg flex items-center justify-center">
            <i data-lucide="{{ $icon }}" class="w-6 h-6"></i>
        </div>
    </div>
    
    @if(isset($footer))
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            {{ $footer }}
        </div>
    @endif
</div>
