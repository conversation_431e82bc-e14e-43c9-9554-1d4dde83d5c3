<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->json('attendee_data')->nullable()->after('customer_phone');
        });

        Schema::table('tickets', function (Blueprint $table) {
            $table->json('attendee_data')->nullable()->after('attendee_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('attendee_data');
        });

        Schema::table('tickets', function (Blueprint $table) {
            $table->dropColumn('attendee_data');
        });
    }
};
