<?php

namespace App\Traits;

use Carbon\Carbon;

trait HasUserLevel
{
    /**
     * Get user level configuration
     */
    public function getLevelConfig(): array
    {
        return config("user_levels.levels.{$this->user_level}", []);
    }

    /**
     * Get user level display name
     */
    public function getLevelDisplayName(): string
    {
        $config = $this->getLevelConfig();
        return $config['display_name'] ?? ucfirst($this->user_level);
    }

    /**
     * Get user level color
     */
    public function getLevelColor(): string
    {
        $config = $this->getLevelConfig();
        return $config['color'] ?? '#6B7280';
    }

    /**
     * Get user level background
     */
    public function getLevelBackground(): string
    {
        $config = $this->getLevelConfig();
        return $config['background'] ?? $this->getLevelColor();
    }

    /**
     * Get user level icon
     */
    public function getLevelIcon(): string
    {
        $config = $this->getLevelConfig();
        return $config['icon'] ?? 'fas fa-user';
    }

    /**
     * Get user level badge class
     */
    public function getLevelBadgeClass(): string
    {
        $config = $this->getLevelConfig();
        return $config['badge_class'] ?? 'badge-default';
    }

    /**
     * Get user level benefits
     */
    public function getLevelBenefits(): array
    {
        $config = $this->getLevelConfig();
        return $config['benefits'] ?? [];
    }

    /**
     * Get user level features
     */
    public function getLevelFeatures(): array
    {
        $config = $this->getLevelConfig();
        return $config['features'] ?? [];
    }

    /**
     * Check if user can create more events this month
     */
    public function canCreateEvent(): bool
    {
        $benefits = $this->getLevelBenefits();
        $maxEvents = $benefits['max_events_per_month'] ?? 5;
        
        if ($maxEvents >= 999) {
            return true; // Unlimited
        }

        $eventsThisMonth = $this->events()
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->count();

        return $eventsThisMonth < $maxEvents;
    }

    /**
     * Get remaining events for this month
     */
    public function getRemainingEvents(): int
    {
        $benefits = $this->getLevelBenefits();
        $maxEvents = $benefits['max_events_per_month'] ?? 5;
        
        if ($maxEvents >= 999) {
            return 999; // Unlimited
        }

        $eventsThisMonth = $this->events()
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->count();

        return max(0, $maxEvents - $eventsThisMonth);
    }

    /**
     * Get commission rate for this user level
     */
    public function getCommissionRate(): float
    {
        $benefits = $this->getLevelBenefits();
        return $benefits['commission_rate'] ?? 5.0;
    }

    /**
     * Check if user has priority support
     */
    public function hasPrioritySupport(): bool
    {
        $benefits = $this->getLevelBenefits();
        return $benefits['priority_support'] ?? false;
    }

    /**
     * Check if user can feature events
     */
    public function canFeatureEvents(): bool
    {
        $benefits = $this->getLevelBenefits();
        return $benefits['featured_events'] ?? false;
    }

    /**
     * Check if user has custom branding
     */
    public function hasCustomBranding(): bool
    {
        $benefits = $this->getLevelBenefits();
        return $benefits['custom_branding'] ?? false;
    }

    /**
     * Get analytics access level
     */
    public function getAnalyticsAccess(): string
    {
        $benefits = $this->getLevelBenefits();
        return $benefits['analytics_access'] ?? 'basic';
    }

    /**
     * Get promotional tools
     */
    public function getPromotionalTools(): array
    {
        $benefits = $this->getLevelBenefits();
        return $benefits['promotional_tools'] ?? ['basic_sharing'];
    }

    /**
     * Check if level is expired
     */
    public function isLevelExpired(): bool
    {
        if (!$this->level_expires_at) {
            return false;
        }

        return Carbon::parse($this->level_expires_at)->isPast();
    }

    /**
     * Get days until level expires
     */
    public function getDaysUntilExpiry(): ?int
    {
        if (!$this->level_expires_at) {
            return null;
        }

        return Carbon::parse($this->level_expires_at)->diffInDays(now());
    }

    /**
     * Calculate level score based on performance
     */
    public function calculateLevelScore(): float
    {
        $score = 0;

        // Events created (20% weight)
        $score += ($this->total_events_created * 10) * 0.2;

        // Tickets sold (30% weight)
        $score += ($this->total_tickets_sold * 2) * 0.3;

        // Revenue (40% weight)
        $score += ($this->total_revenue / 1000000) * 0.4;

        // User rating (10% weight) - if available
        if (method_exists($this, 'averageRating')) {
            $score += ($this->averageRating() * 20) * 0.1;
        }

        return round($score, 2);
    }

    /**
     * Check if user qualifies for level upgrade
     */
    public function qualifiesForUpgrade(string $targetLevel): bool
    {
        $requirements = config("user_levels.levels.{$targetLevel}.requirements", []);
        
        if (empty($requirements)) {
            return false;
        }

        // Check each requirement
        foreach ($requirements as $key => $value) {
            switch ($key) {
                case 'events_created':
                    if ($this->total_events_created < $value) return false;
                    break;
                case 'tickets_sold':
                    if ($this->total_tickets_sold < $value) return false;
                    break;
                case 'revenue':
                    if ($this->total_revenue < $value) return false;
                    break;
                case 'rating':
                    if (method_exists($this, 'averageRating')) {
                        if ($this->averageRating() < $value) return false;
                    }
                    break;
            }
        }

        return true;
    }

    /**
     * Get next available level
     */
    public function getNextLevel(): ?string
    {
        $levels = ['star', 'star_plus', 'premium', 'platinum'];
        $currentIndex = array_search($this->user_level, $levels);
        
        if ($currentIndex === false || $currentIndex >= count($levels) - 1) {
            return null;
        }

        return $levels[$currentIndex + 1];
    }

    /**
     * Get progress to next level
     */
    public function getProgressToNextLevel(): array
    {
        $nextLevel = $this->getNextLevel();
        
        if (!$nextLevel) {
            return ['progress' => 100, 'requirements' => []];
        }

        $requirements = config("user_levels.levels.{$nextLevel}.requirements", []);
        $progress = [];
        $totalProgress = 0;
        $completedRequirements = 0;

        foreach ($requirements as $key => $value) {
            $current = 0;
            $percentage = 0;

            switch ($key) {
                case 'events_created':
                    $current = $this->total_events_created;
                    $percentage = min(100, ($current / $value) * 100);
                    break;
                case 'tickets_sold':
                    $current = $this->total_tickets_sold;
                    $percentage = min(100, ($current / $value) * 100);
                    break;
                case 'revenue':
                    $current = $this->total_revenue;
                    $percentage = min(100, ($current / $value) * 100);
                    break;
                case 'rating':
                    if (method_exists($this, 'averageRating')) {
                        $current = $this->averageRating();
                        $percentage = min(100, ($current / $value) * 100);
                    }
                    break;
            }

            $progress[$key] = [
                'current' => $current,
                'required' => $value,
                'percentage' => round($percentage, 1),
                'completed' => $percentage >= 100
            ];

            $totalProgress += $percentage;
            if ($percentage >= 100) {
                $completedRequirements++;
            }
        }

        return [
            'next_level' => $nextLevel,
            'overall_progress' => round($totalProgress / count($requirements), 1),
            'completed_requirements' => $completedRequirements,
            'total_requirements' => count($requirements),
            'requirements' => $progress
        ];
    }
}
