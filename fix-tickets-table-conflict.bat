@echo off
echo Fixing Tickets Table Conflict for TiXara...

echo.
echo [1/7] Analyzing tickets table conflict...
php artisan tinker --execute="
try {
    echo 'Analyzing tickets table conflict...' . PHP_EOL;
    
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets')) {
        echo 'Tickets table exists' . PHP_EOL;
        
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
        echo 'Current columns:' . PHP_EOL;
        foreach (\$columns as \$column) {
            echo '  - ' . \$column . PHP_EOL;
        }
        
        // Check if it has event columns (wrong structure)
        \$eventColumns = ['title', 'description', 'venue_name', 'start_date', 'end_date'];
        \$ticketColumns = ['ticket_number', 'qr_code', 'attendee_name', 'buyer_id'];
        
        \$hasEventColumns = count(array_intersect(\$eventColumns, \$columns)) > 0;
        \$hasTicketColumns = count(array_intersect(\$ticketColumns, \$columns)) > 0;
        
        if (\$hasEventColumns) {
            echo 'PROBLEM: Table contains event columns (should be events table)' . PHP_EOL;
        } elseif (\$hasTicketColumns) {
            echo 'OK: Table has correct ticket structure' . PHP_EOL;
        } else {
            echo 'UNKNOWN: Table structure unclear' . PHP_EOL;
        }
        
        // Check data count
        \$count = \Illuminate\Support\Facades\DB::table('tickets')->count();
        echo 'Data count: ' . \$count . ' records' . PHP_EOL;
        
    } else {
        echo 'Tickets table does not exist' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Analysis error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [2/7] Backing up tickets data if needed...
php artisan tinker --execute="
try {
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets')) {
        \$count = \Illuminate\Support\Facades\DB::table('tickets')->count();
        
        if (\$count > 0) {
            echo 'Backing up ' . \$count . ' records from tickets table...' . PHP_EOL;
            
            // Create backup table
            \Illuminate\Support\Facades\DB::statement('CREATE TABLE tickets_backup AS SELECT * FROM tickets');
            echo 'Backup created as tickets_backup table' . PHP_EOL;
        } else {
            echo 'No data to backup' . PHP_EOL;
        }
    }
} catch (Exception \$e) {
    echo 'Backup error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [3/7] Disabling foreign key checks...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=0;');
    echo 'Foreign key checks disabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [4/7] Dropping conflicting tickets table...
php artisan tinker --execute="
try {
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets')) {
        echo 'Dropping existing tickets table...' . PHP_EOL;
        \Illuminate\Support\Facades\Schema::dropIfExists('tickets');
        echo 'Tickets table dropped successfully' . PHP_EOL;
    } else {
        echo 'No tickets table to drop' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Drop error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/7] Creating correct tickets table...
php artisan migrate --path=database/migrations/2024_01_01_000005_create_tickets_table.php --force

echo.
echo [6/7] Re-enabling foreign key checks...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    echo 'Foreign key checks re-enabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [7/7] Verifying new tickets table...
php artisan tinker --execute="
try {
    echo 'Verifying new tickets table...' . PHP_EOL;
    
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets')) {
        echo '✓ Tickets table created successfully' . PHP_EOL;
        
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
        echo 'New tickets table columns:' . PHP_EOL;
        foreach (\$columns as \$column) {
            echo '  - ' . \$column . PHP_EOL;
        }
        
        // Verify correct structure
        \$requiredColumns = ['ticket_number', 'qr_code', 'event_id', 'buyer_id', 'order_id'];
        \$missingColumns = array_diff(\$requiredColumns, \$columns);
        
        if (empty(\$missingColumns)) {
            echo '✓ All required columns present' . PHP_EOL;
        } else {
            echo '✗ Missing columns: ' . implode(', ', \$missingColumns) . PHP_EOL;
        }
        
        // Test model operations
        echo PHP_EOL . 'Testing model operations...' . PHP_EOL;
        
        \$ticket = new \App\Models\Ticket();
        echo '✓ Ticket model can be instantiated' . PHP_EOL;
        
        // Test relationships if other tables exist
        if (\Illuminate\Support\Facades\Schema::hasTable('events')) {
            echo '✓ Can access event relationship' . PHP_EOL;
        }
        
        if (\Illuminate\Support\Facades\Schema::hasTable('users')) {
            echo '✓ Can access buyer relationship' . PHP_EOL;
        }
        
        if (\Illuminate\Support\Facades\Schema::hasTable('orders')) {
            echo '✓ Can access order relationship' . PHP_EOL;
        }
        
    } else {
        echo '✗ Tickets table creation failed!' . PHP_EOL;
    }
    
    // Check if backup exists
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets_backup')) {
        \$backupCount = \Illuminate\Support\Facades\DB::table('tickets_backup')->count();
        echo PHP_EOL . 'Backup table available with ' . \$backupCount . ' records' . PHP_EOL;
        echo 'You can restore data later if needed' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Verification error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo ========================================
echo Tickets Table Fix Results
echo ========================================
echo.
echo ✓ COMPLETED TASKS:
echo   - Analyzed table conflict
echo   - Backed up existing data
echo   - Dropped conflicting table
echo   - Created correct tickets table
echo   - Verified new structure
echo.
echo ✓ NEW TICKETS TABLE STRUCTURE:
echo   - id (primary key)
echo   - ticket_number, qr_code
echo   - event_id (foreign key to events)
echo   - buyer_id (foreign key to users)
echo   - order_id (foreign key to orders)
echo   - attendee_name, attendee_email, attendee_phone
echo   - price, admin_fee, total_paid
echo   - status, used_at, validated_by
echo   - timestamps
echo.
echo ✓ BACKUP INFORMATION:
echo   - Original data backed up to tickets_backup table
echo   - Can be restored if needed
echo   - Safe to proceed with application
echo.
echo ✓ NEXT STEPS:
echo   - Run: php artisan migrate (complete remaining migrations)
echo   - Seed: php artisan db:seed (populate sample data)
echo   - Test: php artisan serve (start application)
echo.
echo Tickets table conflict should now be resolved!
echo.
pause
