/**
 * TiXara Theme Manager
 * Manages dynamic theme switching and persistence across the application
 */

class ThemeManager {
    constructor() {
        this.themes = {
            'green': {
                primary: '#A8D5BA',
                secondary: '#E8F5E8',
                accent: '#7FB069',
                name: 'Green Pasta',
                description: 'Calming pasta green theme'
            },
            'blue': {
                primary: '#93C5FD',
                secondary: '#DBEAFE',
                accent: '#3B82F6',
                name: 'Ocean Blue',
                description: 'Professional ocean blue theme'
            },
            'purple': {
                primary: '#C4B5FD',
                secondary: '#EDE9FE',
                accent: '#8B5CF6',
                name: 'Royal Purple',
                description: 'Elegant royal purple theme'
            },
            'pink': {
                primary: '#F9A8D4',
                secondary: '#FCE7F3',
                accent: '#EC4899',
                name: 'Sakura Pink',
                description: 'Soft sakura pink theme'
            },
            'orange': {
                primary: '#FDBA74',
                secondary: '#FED7AA',
                accent: '#F97316',
                name: 'Sunset Orange',
                description: 'Warm sunset orange theme'
            },
            'monochrome': {
                primary: '#6B7280',
                secondary: '#F3F4F6',
                accent: '#374151',
                name: 'Monochrome',
                description: 'Classic black and white theme'
            },
            'dark': {
                primary: '#1F2937',
                secondary: '#F9FAFB',
                accent: '#111827',
                name: 'Dark Mode',
                description: 'Pure dark theme'
            }
        };

        this.currentTheme = localStorage.getItem('themeColor') || 'green';
        this.darkMode = localStorage.getItem('darkMode') === 'true' ||
                       (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches);

        this.init();
    }

    init() {
        this.applyTheme();
        this.setupEventListeners();
        this.setupSystemThemeListener();
        this.initializeNotifications();
    }

    applyTheme() {
        const root = document.documentElement;
        const theme = this.themes[this.currentTheme];

        // Apply dark mode class
        if (this.darkMode || this.currentTheme === 'dark') {
            root.classList.add('dark');
        } else {
            root.classList.remove('dark');
        }

        // Special handling for monochrome and dark themes
        if (this.currentTheme === 'monochrome') {
            root.classList.add('monochrome-theme');
            this.applyMonochromeTheme(theme);
        } else {
            root.classList.remove('monochrome-theme');
        }

        if (this.currentTheme === 'dark') {
            root.classList.add('pure-dark-theme');
            this.applyDarkTheme(theme);
        } else {
            root.classList.remove('pure-dark-theme');
        }

        // Apply theme colors as CSS custom properties
        root.style.setProperty('--color-primary', theme.primary);
        root.style.setProperty('--color-secondary', theme.secondary);
        root.style.setProperty('--color-accent', theme.accent);
        root.style.setProperty('--theme-primary', theme.primary);
        root.style.setProperty('--theme-secondary', theme.secondary);
        root.style.setProperty('--theme-accent', theme.accent);

        // Convert hex to RGB for rgba usage
        const primaryRGB = this.hexToRgb(theme.primary);
        const secondaryRGB = this.hexToRgb(theme.secondary);
        const accentRGB = this.hexToRgb(theme.accent);

        if (primaryRGB) {
            root.style.setProperty('--theme-primary-rgb', `${primaryRGB.r}, ${primaryRGB.g}, ${primaryRGB.b}`);
        }
        if (secondaryRGB) {
            root.style.setProperty('--theme-secondary-rgb', `${secondaryRGB.r}, ${secondaryRGB.g}, ${secondaryRGB.b}`);
        }
        if (accentRGB) {
            root.style.setProperty('--theme-accent-rgb', `${accentRGB.r}, ${accentRGB.g}, ${accentRGB.b}`);
        }

        // Apply contrast adjustments for accessibility
        this.applyContrastAdjustments();

        // Update meta theme color for mobile browsers
        this.updateMetaThemeColor(theme.primary);

        // Dispatch theme change event
        this.dispatchThemeChangeEvent();

        // Save to localStorage
        localStorage.setItem('themeColor', this.currentTheme);
        localStorage.setItem('darkMode', this.darkMode);

        // Update notification badge color
        this.updateNotificationBadge();

        console.log(`Theme applied: ${theme.name} (${this.darkMode ? 'Dark' : 'Light'} mode)`);
    }

    toggleDarkMode() {
        this.darkMode = !this.darkMode;
        this.applyTheme();
        this.showThemeChangeNotification();
    }

    changeTheme(themeColor) {
        if (this.themes[themeColor]) {
            this.currentTheme = themeColor;
            this.applyTheme();
            this.showThemeChangeNotification();
        }
    }

    setupEventListeners() {
        // Listen for theme change events from Alpine.js components
        window.addEventListener('themeChanged', (event) => {
            const { darkMode, themeColor } = event.detail;
            this.darkMode = darkMode;
            this.currentTheme = themeColor;
            this.applyTheme();
        });

        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + Shift + D for dark mode toggle
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
                event.preventDefault();
                this.toggleDarkMode();
            }

            // Ctrl/Cmd + Shift + T for theme cycling
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
                event.preventDefault();
                this.cycleTheme();
            }
        });
    }

    setupSystemThemeListener() {
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('darkMode')) {
                this.darkMode = e.matches;
                this.applyTheme();
            }
        });
    }

    cycleTheme() {
        const themeKeys = Object.keys(this.themes);
        const currentIndex = themeKeys.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themeKeys.length;
        this.changeTheme(themeKeys[nextIndex]);
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    updateMetaThemeColor(color) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = color;
    }

    dispatchThemeChangeEvent() {
        window.dispatchEvent(new CustomEvent('themeApplied', {
            detail: {
                theme: this.themes[this.currentTheme],
                themeColor: this.currentTheme,
                darkMode: this.darkMode
            }
        }));
    }

    showThemeChangeNotification() {
        const theme = this.themes[this.currentTheme];
        const message = `Tema diubah ke ${theme.name} (${this.darkMode ? 'Mode Gelap' : 'Mode Terang'})`;

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `
            fixed top-20 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600
            rounded-lg shadow-lg p-4 transform translate-x-full transition-transform duration-300 ease-out
        `;
        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="w-4 h-4 rounded-full" style="background-color: ${theme.primary}"></div>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    initializeNotifications() {
        // Initialize notification system with theme colors
        this.updateNotificationBadge();

        // Setup notification sound
        this.notificationSound = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    }

    updateNotificationBadge() {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            const theme = this.themes[this.currentTheme];
            badge.style.background = `linear-gradient(135deg, ${theme.accent}, ${theme.primary})`;
        }
    }

    playNotificationSound() {
        if (this.notificationSound && !document.hidden) {
            this.notificationSound.currentTime = 0;
            this.notificationSound.play().catch(() => {
                // Ignore autoplay restrictions
            });
        }
    }

    // Public API methods
    getCurrentTheme() {
        return {
            color: this.currentTheme,
            theme: this.themes[this.currentTheme],
            darkMode: this.darkMode
        };
    }

    getAvailableThemes() {
        return this.themes;
    }

    isDarkMode() {
        return this.darkMode;
    }

    getThemeColor() {
        return this.currentTheme;
    }

    applyMonochromeTheme(theme) {
        const root = document.documentElement;

        // Enhanced monochrome color palette
        root.style.setProperty('--mono-black', '#000000');
        root.style.setProperty('--mono-dark-gray', '#1F2937');
        root.style.setProperty('--mono-gray', '#6B7280');
        root.style.setProperty('--mono-light-gray', '#D1D5DB');
        root.style.setProperty('--mono-off-white', '#F9FAFB');
        root.style.setProperty('--mono-white', '#FFFFFF');

        // Override theme colors for true monochrome
        if (this.darkMode) {
            root.style.setProperty('--bg-primary', '#111827');
            root.style.setProperty('--bg-secondary', '#1F2937');
            root.style.setProperty('--text-primary', '#F9FAFB');
            root.style.setProperty('--text-secondary', '#D1D5DB');
            root.style.setProperty('--border-color', '#374151');
        } else {
            root.style.setProperty('--bg-primary', '#FFFFFF');
            root.style.setProperty('--bg-secondary', '#F9FAFB');
            root.style.setProperty('--text-primary', '#111827');
            root.style.setProperty('--text-secondary', '#6B7280');
            root.style.setProperty('--border-color', '#E5E7EB');
        }

        // Add monochrome-specific styles
        root.style.setProperty('--mono-shadow', this.darkMode ?
            '0 4px 6px -1px rgba(0, 0, 0, 0.5)' :
            '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
        );
    }

    applyDarkTheme(theme) {
        const root = document.documentElement;

        // Pure dark theme overrides
        root.style.setProperty('--bg-primary', '#000000');
        root.style.setProperty('--bg-secondary', '#111827');
        root.style.setProperty('--text-primary', '#FFFFFF');
        root.style.setProperty('--text-secondary', '#D1D5DB');
        root.style.setProperty('--border-color', '#1F2937');

        // Dark theme specific colors
        root.style.setProperty('--dark-accent', '#374151');
        root.style.setProperty('--dark-hover', '#1F2937');
        root.style.setProperty('--dark-shadow', '0 4px 6px -1px rgba(0, 0, 0, 0.8)');
    }

    applyContrastAdjustments() {
        const root = document.documentElement;

        // High contrast mode detection
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            if (this.currentTheme === 'monochrome' || this.currentTheme === 'dark') {
                root.style.setProperty('--border-color', this.darkMode ? '#FFFFFF' : '#000000');
                root.style.setProperty('--text-secondary', this.darkMode ? '#FFFFFF' : '#000000');
            }
        }

        // Ensure sufficient contrast for accessibility
        if (this.currentTheme === 'monochrome') {
            const contrastRatio = this.calculateContrastRatio();
            if (contrastRatio < 4.5) {
                // Adjust colors for better contrast
                this.adjustColorsForContrast();
            }
        }
    }

    calculateContrastRatio() {
        // Simplified contrast ratio calculation
        // In a real implementation, you'd use proper color contrast algorithms
        return this.darkMode ? 15 : 12; // Placeholder values
    }

    adjustColorsForContrast() {
        const root = document.documentElement;

        if (this.darkMode) {
            root.style.setProperty('--text-primary', '#FFFFFF');
            root.style.setProperty('--bg-primary', '#000000');
        } else {
            root.style.setProperty('--text-primary', '#000000');
            root.style.setProperty('--bg-primary', '#FFFFFF');
        }
    }

    // Enhanced theme change notification with theme preview
    showThemeChangeNotification() {
        const theme = this.themes[this.currentTheme];
        const message = `Tema diubah ke ${theme.name} (${this.darkMode ? 'Mode Gelap' : 'Mode Terang'})`;

        // Create enhanced notification element
        const notification = document.createElement('div');
        notification.className = `
            fixed top-20 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600
            rounded-lg shadow-lg p-4 transform translate-x-full transition-transform duration-300 ease-out
            ${this.currentTheme === 'monochrome' ? 'monochrome-notification' : ''}
            ${this.currentTheme === 'dark' ? 'dark-notification' : ''}
        `;

        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="flex space-x-1">
                    <div class="w-3 h-3 rounded-full" style="background-color: ${theme.primary}"></div>
                    <div class="w-3 h-3 rounded-full" style="background-color: ${theme.secondary}"></div>
                    <div class="w-3 h-3 rounded-full" style="background-color: ${theme.accent}"></div>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">${theme.name}</span>
                    <p class="text-xs text-gray-600 dark:text-gray-400">${theme.description}</p>
                </div>
                ${this.darkMode ? '<i data-lucide="moon" class="w-4 h-4 text-gray-600"></i>' : '<i data-lucide="sun" class="w-4 h-4 text-yellow-500"></i>'}
            </div>
        `;

        document.body.appendChild(notification);

        // Initialize Lucide icons for the notification
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();

    // Make theme manager globally accessible
    window.TiXara = window.TiXara || {};
    window.TiXara.themeManager = window.themeManager;

    // Expose methods for Alpine.js components
    window.toggleDarkMode = () => window.themeManager.toggleDarkMode();
    window.changeTheme = (color) => window.themeManager.changeTheme(color);
    window.getCurrentTheme = () => window.themeManager.getCurrentTheme();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
