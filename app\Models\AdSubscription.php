<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AdSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_type',
        'plan_name',
        'plan_description',
        'monthly_price',
        'yearly_price',
        'billing_cycle',
        'max_ads',
        'max_impressions_per_day',
        'max_clicks_per_day',
        'features',
        'priority_placement',
        'analytics_access',
        'custom_targeting',
        'starts_at',
        'expires_at',
        'status',
        'amount_paid',
        'payment_method',
        'payment_reference',
        'last_payment_at',
        'next_payment_at',
        'auto_renewal',
        'cancellation_reason',
        'cancelled_at',
    ];

    protected $casts = [
        'features' => 'array',
        'monthly_price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'priority_placement' => 'boolean',
        'analytics_access' => 'boolean',
        'custom_targeting' => 'boolean',
        'auto_renewal' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'last_payment_at' => 'datetime',
        'next_payment_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Get the user that owns the subscription
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for active subscriptions
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope for expired subscriptions
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now())
                    ->whereIn('status', ['active', 'expired']);
    }

    /**
     * Check if subscription is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->expires_at > now();
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Check if subscription needs renewal
     */
    public function needsRenewal(): bool
    {
        return $this->auto_renewal && 
               $this->status === 'active' && 
               $this->expires_at <= now()->addDays(7); // 7 days before expiry
    }

    /**
     * Get remaining days
     */
    public function getRemainingDaysAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInDays($this->expires_at);
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentageAttribute(): float
    {
        $totalDays = $this->starts_at->diffInDays($this->expires_at);
        $usedDays = $this->starts_at->diffInDays(now());
        
        if ($totalDays == 0) {
            return 100;
        }

        return min(100, round(($usedDays / $totalDays) * 100, 2));
    }

    /**
     * Renew subscription
     */
    public function renew(): bool
    {
        if (!$this->auto_renewal) {
            return false;
        }

        $price = $this->billing_cycle === 'yearly' ? $this->yearly_price : $this->monthly_price;
        $duration = $this->billing_cycle === 'yearly' ? 12 : 1;

        $this->update([
            'starts_at' => $this->expires_at,
            'expires_at' => $this->expires_at->addMonths($duration),
            'last_payment_at' => now(),
            'next_payment_at' => now()->addMonths($duration),
            'amount_paid' => $this->amount_paid + $price,
            'status' => 'active',
        ]);

        return true;
    }

    /**
     * Cancel subscription
     */
    public function cancel($reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'auto_renewal' => false,
            'cancellation_reason' => $reason,
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Suspend subscription
     */
    public function suspend(): void
    {
        $this->update([
            'status' => 'suspended',
            'auto_renewal' => false,
        ]);
    }

    /**
     * Reactivate subscription
     */
    public function reactivate(): void
    {
        if ($this->isExpired()) {
            // Extend expiry if expired
            $duration = $this->billing_cycle === 'yearly' ? 12 : 1;
            $this->update([
                'expires_at' => now()->addMonths($duration),
                'next_payment_at' => now()->addMonths($duration),
            ]);
        }

        $this->update([
            'status' => 'active',
            'auto_renewal' => true,
        ]);
    }

    /**
     * Get subscription plans
     */
    public static function getPlans(): array
    {
        return [
            'basic' => [
                'name' => 'Basic Plan',
                'description' => 'Perfect for small businesses',
                'monthly_price' => 99000,
                'yearly_price' => 990000,
                'max_ads' => 5,
                'max_impressions_per_day' => 1000,
                'max_clicks_per_day' => 100,
                'features' => [
                    'Basic analytics',
                    'Standard support',
                    'Banner ads',
                    'Event promotion'
                ],
                'priority_placement' => false,
                'analytics_access' => true,
                'custom_targeting' => false,
            ],
            'premium' => [
                'name' => 'Premium Plan',
                'description' => 'Great for growing businesses',
                'monthly_price' => 199000,
                'yearly_price' => 1990000,
                'max_ads' => 15,
                'max_impressions_per_day' => 5000,
                'max_clicks_per_day' => 500,
                'features' => [
                    'Advanced analytics',
                    'Priority support',
                    'All ad types',
                    'Custom targeting',
                    'Priority placement'
                ],
                'priority_placement' => true,
                'analytics_access' => true,
                'custom_targeting' => true,
            ],
            'enterprise' => [
                'name' => 'Enterprise Plan',
                'description' => 'For large organizations',
                'monthly_price' => 499000,
                'yearly_price' => 4990000,
                'max_ads' => 50,
                'max_impressions_per_day' => 20000,
                'max_clicks_per_day' => 2000,
                'features' => [
                    'Full analytics suite',
                    'Dedicated support',
                    'All ad types',
                    'Advanced targeting',
                    'Top priority placement',
                    'Custom integrations'
                ],
                'priority_placement' => true,
                'analytics_access' => true,
                'custom_targeting' => true,
            ],
        ];
    }
}
