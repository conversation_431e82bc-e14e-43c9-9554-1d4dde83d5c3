@extends('layouts.main')

@section('content')
<div class="min-h-screen">
    <!-- Hero Section -->
    <div class="relative min-h-screen overflow-hidden bg-gradient-to-br from-primary via-primary-600 to-secondary">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <!-- Floating Shapes -->
            <div class="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-float"></div>
            <div class="absolute top-40 right-20 w-24 h-24 bg-accent/20 rounded-full blur-lg animate-float-delayed"></div>
            <div class="absolute bottom-40 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-2xl animate-float-slow"></div>
            <div class="absolute bottom-20 right-1/3 w-28 h-28 bg-accent/15 rounded-full blur-xl animate-float"></div>

            <!-- Grid Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="h-full w-full" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 50px 50px;"></div>
            </div>

            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <!-- Decorative SVG Wave -->
        <div class="absolute bottom-0 left-0 w-full">
            <svg class="w-full h-24 md:h-32 text-white fill-current" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"></path>
                <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"></path>
                <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
            </svg>
        </div>

        <!-- Main Content -->
        <div class="relative z-10 flex items-center min-h-screen">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <!-- Left Content -->
                    <div class="text-white space-y-8">
                        <!-- Badge -->
                        <div class="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium" data-aos="fade-up">
                            <span class="w-2 h-2 bg-accent rounded-full mr-2 animate-pulse"></span>
                            Platform Tiket Event Terpercaya #1 di Indonesia
                        </div>

                        <!-- Main Heading -->
                        <div data-aos="fade-up" data-aos-delay="100">
                            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
                                Temukan
                                <span class="block bg-gradient-to-r from-accent via-yellow-300 to-orange-300 bg-clip-text text-transparent animate-gradient">
                                    Event Impian
                                </span>
                                <span class="block">Anda</span>
                            </h1>
                        </div>

                        <!-- Subtitle -->
                        <p class="text-xl md:text-2xl text-white/90 max-w-2xl leading-relaxed" data-aos="fade-up" data-aos-delay="200">
                            Jelajahi ribuan event menarik dari konser musik, seminar bisnis, workshop kreatif, hingga festival kuliner.
                            <span class="text-accent font-semibold">Semua dalam satu platform!</span>
                        </p>

                        <!-- CTA Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4" data-aos="fade-up" data-aos-delay="300">
                            <button onclick="document.getElementById('heroSearch').focus()"
                                    class="group px-8 py-4 bg-white text-primary rounded-2xl font-bold text-lg hover:bg-gray-50 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                                <span class="flex items-center justify-center">
                                    <i data-lucide="search" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Cari Event Sekarang
                                </span>
                            </button>
                            <a href="{{ route('categories.index') }}"
                               class="group px-8 py-4 border-2 border-white/30 text-white rounded-2xl font-bold text-lg hover:bg-white/10 backdrop-blur-sm transition-all duration-300 hover:border-white/50">
                                <span class="flex items-center justify-center">
                                    <i data-lucide="grid-3x3" class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    Jelajahi Kategori
                                </span>
                            </a>
                        </div>

                        <!-- UangTix Shortcut -->
                        @auth
                        <div class="mt-6" data-aos="fade-up" data-aos-delay="350">
                            <a href="{{ route('uangtix.index') }}"
                               class="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-yellow-400 via-yellow-500 to-orange-500 text-white rounded-2xl font-bold text-base hover:from-yellow-500 hover:via-yellow-600 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                <span class="flex items-center">
                                    <i class="fas fa-coins w-5 h-5 mr-2 group-hover:scale-110 transition-transform"></i>
                                    UangTix - Dompet Digital
                                    <span class="ml-2 px-2 py-1 bg-white/20 rounded-full text-xs font-bold">
                                        UTX {{ number_format(auth()->user()->getUangTixBalanceAmount(), 0) }}
                                    </span>
                                </span>
                            </a>
                            <p class="text-white/70 text-sm mt-2">
                                <i class="fas fa-info-circle mr-1"></i>
                                1 UangTix = 1 Rupiah • Kelola saldo digital untuk transaksi mudah
                            </p>
                        </div>
                        @endauth

                        <!-- Trust Indicators -->
                        <div class="flex items-center space-x-6 pt-4" data-aos="fade-up" data-aos-delay="400">
                            <div class="flex items-center space-x-2">
                                <div class="flex -space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full border-2 border-white"></div>
                                    <div class="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full border-2 border-white"></div>
                                    <div class="w-8 h-8 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full border-2 border-white"></div>
                                    <div class="w-8 h-8 bg-gradient-to-r from-pink-400 to-pink-600 rounded-full border-2 border-white flex items-center justify-center text-xs font-bold text-white">
                                        +
                                    </div>
                                </div>
                                <span class="text-white/80 text-sm">50K+ pengguna aktif</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="flex space-x-1">
                                    <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                    <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                </div>
                                <span class="text-white/80 text-sm ml-2">4.9/5 rating</span>
                            </div>
                        </div>
                    </div>

                    <!-- Right Content - Search & Stats -->
                    <div class="space-y-8">
                        <!-- Enhanced Search Bar -->
                        <div class="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20" data-aos="fade-up" data-aos-delay="300">
                            <h3 class="text-white text-xl font-bold mb-6 text-center">🔍 Cari Event Favoritmu</h3>

                            <!-- Search Input -->
                            <div class="relative mb-6">
                                <input type="text"
                                       id="heroSearch"
                                       class="w-full px-6 py-4 pl-14 pr-16 rounded-2xl bg-white/95 backdrop-blur-sm shadow-xl focus:outline-none focus:ring-4 focus:ring-accent/30 text-gray-800 text-lg placeholder-gray-500 transition-all duration-300"
                                       placeholder="Konser, seminar, workshop...">
                                <div class="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
                                    <i data-lucide="search" class="w-5 h-5 text-gray-400"></i>
                                </div>
                                <button id="heroSearchBtn" class="absolute right-2 top-1/2 -translate-y-1/2 bg-gradient-to-r from-primary to-secondary text-white p-3 rounded-xl hover:shadow-lg transition-all duration-300 group">
                                    <i data-lucide="arrow-right" class="w-5 h-5 group-hover:translate-x-1 transition-transform"></i>
                                </button>
                            </div>

                            <!-- Quick Search Tags -->
                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="text-white/70 text-sm">Populer:</span>
                                <button class="px-3 py-1 bg-white/20 text-white text-sm rounded-full hover:bg-white/30 transition-colors">Konser</button>
                                <button class="px-3 py-1 bg-white/20 text-white text-sm rounded-full hover:bg-white/30 transition-colors">Workshop</button>
                                <button class="px-3 py-1 bg-white/20 text-white text-sm rounded-full hover:bg-white/30 transition-colors">Seminar</button>
                            </div>

                            <!-- Search Suggestions -->
                            <div id="searchSuggestions" class="hidden space-y-2">
                                <!-- Dynamic suggestions will be loaded here -->
                            </div>
                        </div>

                        <!-- Stats Grid -->
                        <div class="grid grid-cols-2 gap-4" data-aos="fade-up" data-aos-delay="400">
                            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 text-center group hover:bg-white/15 transition-all duration-300">
                                <div class="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform" data-counter="1000">0</div>
                                <div class="text-white/80 text-sm">Event Tersedia</div>
                            </div>
                            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 text-center group hover:bg-white/15 transition-all duration-300">
                                <div class="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform" data-counter="50000">0</div>
                                <div class="text-white/80 text-sm">Tiket Terjual</div>
                            </div>
                            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 text-center group hover:bg-white/15 transition-all duration-300">
                                <div class="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform" data-counter="25">0</div>
                                <div class="text-white/80 text-sm">Kota</div>
                            </div>
                            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 text-center group hover:bg-white/15 transition-all duration-300">
                                <div class="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform" data-counter="4.9">0</div>
                                <div class="text-white/80 text-sm">Rating</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce" data-aos="fade-up" data-aos-delay="600">
            <div class="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </div>

    <!-- Trending Categories -->
    <x-trending-categories :categories="$trendingCategories" />

    <!-- All Categories -->
    <x-category-grid
        :categories="$categories"
        title="Jelajahi Kategori Event"
        subtitle="Temukan event sesuai minat dan passion Anda dari berbagai kategori menarik"
        :show-all="true" />

    <!-- Featured Events Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    ⭐ Event Unggulan
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Event pilihan terbaik yang direkomendasikan khusus untuk Anda
                </p>
            </div>

            <div id="featured-tickets" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Featured tickets will be loaded here via AJAX -->
            </div>
        </div>
    </section>

    <!-- Latest Events Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    🆕 Event Terbaru
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Event-event terbaru yang baru saja ditambahkan ke platform kami
                </p>
            </div>

            <div id="latest-tickets" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Latest tickets will be loaded here via AJAX -->
            </div>
        </div>
    </section>

    <!-- Popular Events Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    🔥 Event Populer
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="100">
                    Event dengan penjualan tiket terbanyak dan rating tertinggi
                </p>
            </div>

            <div id="popular-tickets" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Popular tickets will be loaded here via AJAX -->
            </div>
        </div>
    </section>
</div>

<!-- Install PWA Prompt -->
<div id="installPrompt" class="hidden fixed bottom-20 left-4 right-4 md:left-auto md:right-4 md:bottom-4 md:w-96 bg-white rounded-2xl shadow-2xl p-6">
    <h4 class="text-lg font-bold mb-2">Install TiXara App</h4>
    <p class="text-gray-600 mb-4">Install aplikasi TiXara untuk pengalaman yang lebih baik dan akses tiket offline.</p>
    <div class="flex justify-end space-x-4">
        <button onclick="this.parentElement.parentElement.classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
            Nanti
        </button>
        <button id="installButton" class="px-6 py-2 bg-primary text-white rounded-full hover:bg-primary/90 transition">
            Install
        </button>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Custom Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(-180deg); }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(90deg); }
}

@keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
}

.animate-float-slow {
    animation: float-slow 10s ease-in-out infinite;
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
}

/* Glassmorphism effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Typing animation */
@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    50% { border-color: transparent; }
}

.typing-animation {
    overflow: hidden;
    border-right: 2px solid;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink 0.75s step-end infinite;
}

/* Particle effect */
.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
    animation: particle-float 15s linear infinite;
}

@keyframes particle-float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Search suggestions */
.search-suggestion {
    transition: all 0.2s ease;
}

.search-suggestion:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

/* Counter animation */
.counter {
    transition: all 0.3s ease;
}

/* Pulse effect for CTA buttons */
@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.3); }
    50% { box-shadow: 0 0 40px rgba(255, 255, 255, 0.6); }
}

.pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize hero animations
    initHeroAnimations();

    // Load all ticket sections
    loadfeaturedTickets();
    loadLatestTickets();
    loadPopularTickets();

    // Setup enhanced search functionality
    setupEnhancedSearch();

    // Setup PWA install prompt
    setupPWAInstall();

    // Initialize particle effect
    initParticleEffect();

    // Initialize counter animations
    initCounterAnimations();
});

async function loadfeaturedTickets() {
    try {
        const response = await fetch('/tickets/featured');
        const tickets = await response.json();

        const container = document.querySelector('#featured-tickets') ||
                         document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');

        if (container) {
            if (tickets.length > 0) {
                container.innerHTML = tickets.map((event, index) => createEventCard(event, index * 100)).join('');
                // Reinitialize AOS for new elements
                AOS.refresh();
            } else {
                container.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 12h.01"/>
                        </svg>
                        <p class="text-gray-500 text-lg">Belum ada event yang tersedia</p>
                    </div>
                `;
            }
        }
    } catch (error) {
        console.error('Error loading featured tickets:', error);
        const container = document.querySelector('#featured-tickets') ||
                         document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        if (container) {
            container.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <p class="text-red-500">Gagal memuat event. Silakan refresh halaman.</p>
                </div>
            `;
        }
    }
}

async function loadLatestTickets() {
    try {
        const response = await fetch('/api/latest-tickets');
        const tickets = await response.json();

        const container = document.querySelector('#latest-tickets');

        if (container) {
            if (tickets.length > 0) {
                container.innerHTML = tickets.map((event, index) => createEventCard(event, index * 100)).join('');
                AOS.refresh();
            } else {
                container.innerHTML = getEmptyState('Belum ada event terbaru');
            }
        }
    } catch (error) {
        console.error('Error loading latest tickets:', error);
        const container = document.querySelector('#latest-tickets');
        if (container) {
            container.innerHTML = getErrorState();
        }
    }
}

async function loadPopularTickets() {
    try {
        const response = await fetch('/api/popular-tickets');
        const tickets = await response.json();

        const container = document.querySelector('#popular-tickets');

        if (container) {
            if (tickets.length > 0) {
                container.innerHTML = tickets.map((event, index) => createEventCard(event, index * 100)).join('');
                AOS.refresh();
            } else {
                container.innerHTML = getEmptyState('Belum ada event populer');
            }
        }
    } catch (error) {
        console.error('Error loading popular tickets:', error);
        const container = document.querySelector('#popular-tickets');
        if (container) {
            container.innerHTML = getErrorState();
        }
    }
}

function getEmptyState(message) {
    return `
        <div class="col-span-full text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 12h.01"/>
            </svg>
            <p class="text-gray-500 text-lg">${message}</p>
        </div>
    `;
}

function getErrorState() {
    return `
        <div class="col-span-full text-center py-12">
            <p class="text-red-500">Gagal memuat event. Silakan refresh halaman.</p>
        </div>
    `;
}

function createEventCard(event, delay = 0) {
    const statusBadge = getStatusBadge(event);

    return `
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow card-hover"
             data-aos="fade-up" data-aos-delay="${delay}">
            <div class="relative">
                <img src="${event.poster}" alt="${event.title}" class="w-full h-48 object-cover">
                ${statusBadge}
                <div class="absolute top-4 right-4">
                    <button onclick="toggleWishlist(${event.id})"
                            class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors duration-200 wishlist-btn"
                            data-event-id="${event.id}">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">${event.category}</span>
                    <span class="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm">${event.start_date}</span>
                </div>
                <h3 class="text-xl font-bold mb-2 line-clamp-2">${event.title}</h3>
                <p class="text-gray-600 mb-4 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    ${event.venue_name}, ${event.city}
                </p>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-bold text-primary">${event.price}</span>
                    <a href="${event.url}"
                       class="px-6 py-2 bg-gradient-to-r from-primary to-secondary text-white rounded-lg hover:shadow-lg transition-all duration-300 font-semibold">
                        Detail
                    </a>
                </div>
            </div>
        </div>
    `;
}

function getStatusBadge(event) {
    if (event.is_free) {
        return `
            <div class="absolute top-4 left-4">
                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Gratis
                </span>
            </div>
        `;
    }

    if (event.available_tickets <= 10) {
        return `
            <div class="absolute top-4 left-4">
                <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Terbatas
                </span>
            </div>
        `;
    }

    return '';
}

// Initialize hero animations
function initHeroAnimations() {
    // Add typing animation to main heading
    const heading = document.querySelector('h1');
    if (heading) {
        const spans = heading.querySelectorAll('span');
        spans.forEach((span, index) => {
            span.style.animationDelay = `${index * 0.5}s`;
        });
    }

    // Add pulse glow to CTA buttons after delay
    setTimeout(() => {
        const ctaButtons = document.querySelectorAll('.group');
        ctaButtons.forEach(button => {
            button.classList.add('pulse-glow');
        });
    }, 2000);
}

// Initialize particle effect
function initParticleEffect() {
    const heroSection = document.querySelector('.min-h-screen.overflow-hidden');
    if (!heroSection) return;

    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';

        const size = Math.random() * 4 + 2;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.animationDuration = `${Math.random() * 10 + 10}s`;
        particle.style.animationDelay = `${Math.random() * 5}s`;

        heroSection.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 20000);
    }

    // Create particles periodically
    setInterval(createParticle, 2000);
}

// Initialize counter animations
function initCounterAnimations() {
    const counters = document.querySelectorAll('[data-counter]');

    const animateCounter = (counter) => {
        const target = parseInt(counter.dataset.counter);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            if (target === 4.9) {
                counter.textContent = current.toFixed(1);
            } else if (target >= 1000) {
                counter.textContent = Math.floor(current).toLocaleString() + (target >= 50000 ? 'K+' : '+');
            } else {
                counter.textContent = Math.floor(current) + '+';
            }
        }, 16);
    };

    // Use Intersection Observer to trigger animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => observer.observe(counter));
}

// Enhanced search functionality
function setupEnhancedSearch() {
    const searchInput = document.getElementById('heroSearch');
    const searchBtn = document.getElementById('heroSearchBtn');
    const suggestionsContainer = document.getElementById('searchSuggestions');
    const quickTags = document.querySelectorAll('.bg-white\\/20');

    let debounceTimer;

    // Search input functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                if (this.value.length > 2) {
                    fetchSearchSuggestions(this.value);
                } else {
                    hideSuggestions();
                }
            }, 300);
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        searchInput.addEventListener('focus', function() {
            this.parentElement.classList.add('ring-4', 'ring-accent/30');
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.classList.remove('ring-4', 'ring-accent/30');
            setTimeout(hideSuggestions, 200);
        });
    }

    // Search button functionality
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }

    // Quick tag functionality
    quickTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const query = this.textContent.trim();
            searchInput.value = query;
            performSearch();
        });
    });

    async function fetchSearchSuggestions(query) {
        try {
            const response = await fetch(`/api/search-suggestions?q=${encodeURIComponent(query)}`);
            const suggestions = await response.json();
            showSuggestions(suggestions);
        } catch (error) {
            console.error('Error fetching suggestions:', error);
        }
    }

    function showSuggestions(suggestions) {
        if (!suggestions || suggestions.length === 0) {
            hideSuggestions();
            return;
        }

        suggestionsContainer.innerHTML = suggestions.map(suggestion => `
            <div class="search-suggestion p-3 rounded-lg cursor-pointer text-white/90 hover:text-white"
                 onclick="selectSuggestion('${suggestion.title}')">
                <div class="flex items-center space-x-3">
                    <i data-lucide="search" class="w-4 h-4"></i>
                    <span>${suggestion.title}</span>
                    <span class="text-xs text-white/60">${suggestion.category}</span>
                </div>
            </div>
        `).join('');

        suggestionsContainer.classList.remove('hidden');

        // Re-initialize Lucide icons
        if (window.lucide) {
            window.lucide.createIcons();
        }
    }

    function hideSuggestions() {
        suggestionsContainer.classList.add('hidden');
    }

    window.selectSuggestion = function(title) {
        searchInput.value = title;
        hideSuggestions();
        performSearch();
    };
}

function setupSearch() {
    // This function is kept for backward compatibility
    setupEnhancedSearch();
}

function performSearch() {
    const searchInput = document.querySelector('input[placeholder*="Cari event"]');
    const query = searchInput?.value.trim();

    if (query) {
        window.location.href = `/tickets?search=${encodeURIComponent(query)}`;
    }
}

async function toggleWishlist(eventId) {
    if (!@json(auth()->check())) {
        window.showNotification('Silakan login terlebih dahulu', 'warning');
        return;
    }

    const button = document.querySelector(`[data-event-id="${eventId}"]`);
    const icon = button?.querySelector('svg');

    try {
        const response = await fetch(`/tickets${eventId}/wishlist`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (response.ok) {
            icon?.classList.toggle('text-red-500');
            icon?.classList.toggle('fill-current');
            window.showNotification(data.message, 'success');
        } else {
            window.showNotification(data.error || 'Terjadi kesalahan', 'error');
        }
    } catch (error) {
        window.showNotification('Terjadi kesalahan jaringan', 'error');
    }
}

function setupPWAInstall() {
    let deferredPrompt;
    const installPrompt = document.getElementById('installPrompt');
    const installButton = document.getElementById('installButton');

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show install prompt after 5 seconds
        setTimeout(() => {
            if (installPrompt) {
                installPrompt.classList.remove('hidden');
            }
        }, 5000);
    });

    if (installButton) {
        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;

                if (outcome === 'accepted') {
                    window.showNotification('Aplikasi berhasil diinstall!', 'success');
                }

                deferredPrompt = null;
                installPrompt?.classList.add('hidden');
            }
        });
    }
}
</script>
@endpush