@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">Detail Pembayaran</h1>
                <p class="text-gray-600">Order #{{ $order->order_number }}</p>
            </div>
            <div class="flex space-x-3">
                @if($order->status === 'pending')
                <form action="{{ route('admin.payments.approve', $order) }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                            onclick="return confirm('Approve payment ini?')">
                        Approve Payment
                    </button>
                </form>
                <form action="{{ route('admin.payments.reject', $order) }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                            onclick="return confirm('Reject payment ini?')">
                        Reject Payment
                    </button>
                </form>
                @endif
                <a href="{{ route('admin.payments.index') }}" 
                   class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Payment Information -->
        <div class="space-y-6">
            <!-- Order Details -->
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up">
                <h3 class="text-lg font-semibold mb-4">Informasi Order</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Order Number:</span>
                        <span class="font-medium">#{{ $order->order_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Status:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $order->status === 'completed' ? 'bg-green-100 text-green-800' : 
                               ($order->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                            {{ ucfirst($order->status) }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Tanggal Order:</span>
                        <span class="font-medium">{{ $order->created_at->format('d M Y, H:i') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Amount:</span>
                        <span class="font-bold text-lg">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</span>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="100">
                <h3 class="text-lg font-semibold mb-4">Detail Pembayaran</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Payment Method:</span>
                        <span class="font-medium">{{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Payment Status:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $order->payment_status === 'paid' ? 'bg-green-100 text-green-800' : 
                               ($order->payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </div>
                    @if($order->payment_reference)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Payment Reference:</span>
                        <span class="font-medium">{{ $order->payment_reference }}</span>
                    </div>
                    @endif
                    @if($order->payment_date)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Payment Date:</span>
                        <span class="font-medium">{{ $order->payment_date->format('d M Y, H:i') }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Customer Information -->
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="200">
                <h3 class="text-lg font-semibold mb-4">Informasi Pembeli</h3>
                <div class="flex items-center space-x-4 mb-4">
                    <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <span class="text-lg font-medium text-gray-600">
                            {{ substr($order->user->name, 0, 1) }}
                        </span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">{{ $order->user->name }}</h4>
                        <p class="text-sm text-gray-600">{{ $order->user->email }}</p>
                    </div>
                </div>
                <div class="space-y-3">
                    @if($order->user->phone)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Phone:</span>
                        <span class="font-medium">{{ $order->user->phone }}</span>
                    </div>
                    @endif
                    <div class="flex justify-between">
                        <span class="text-gray-600">Member Since:</span>
                        <span class="font-medium">{{ $order->user->created_at->format('d M Y') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event & Tickets Information -->
        <div class="space-y-6">
            <!-- Event Details -->
            @if($order->event)
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
                <h3 class="text-lg font-semibold mb-4">Informasi Event</h3>
                <div class="flex items-start space-x-4 mb-4">
                    @if($order->event->poster)
                    <img src="{{ $order->event->poster_url }}" 
                         alt="{{ $order->event->title }}" 
                         class="w-16 h-16 rounded-lg object-cover">
                    @else
                    <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    @endif
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900">{{ $order->event->title }}</h4>
                        <p class="text-sm text-gray-600">{{ $order->event->category->name ?? 'No Category' }}</p>
                        <p class="text-sm text-gray-600">{{ $order->event->venue_name }}</p>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Event Date:</span>
                        <span class="font-medium">{{ $order->event->start_date ? $order->event->start_date->format('d M Y, H:i') : 'TBA' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Ticket Price:</span>
                        <span class="font-medium">Rp {{ number_format($order->event->price, 0, ',', '.') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Organizer:</span>
                        <span class="font-medium">{{ $order->event->organizer->name ?? 'Unknown' }}</span>
                    </div>
                </div>
            </div>
            @endif

            <!-- Tickets -->
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="400">
                <h3 class="text-lg font-semibold mb-4">Tiket ({{ $order->tickets->count() }})</h3>
                <div class="space-y-3">
                    @forelse($order->tickets as $ticket)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex justify-between items-start">
                            <div>
                                <h5 class="font-medium text-gray-900">Ticket #{{ $ticket->ticket_number }}</h5>
                                <p class="text-sm text-gray-600">{{ $ticket->attendee_name }}</p>
                                <p class="text-xs text-gray-500">{{ $ticket->attendee_email }}</p>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $ticket->status === 'active' ? 'bg-green-100 text-green-800' : 
                                       ($ticket->status === 'used' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                                    {{ ucfirst($ticket->status) }}
                                </span>
                                <p class="text-sm font-medium mt-1">Rp {{ number_format($ticket->price, 0, ',', '.') }}</p>
                            </div>
                        </div>
                        @if($ticket->used_at)
                        <div class="mt-2 pt-2 border-t border-gray-100">
                            <p class="text-xs text-gray-500">Used at: {{ $ticket->used_at->format('d M Y, H:i') }}</p>
                        </div>
                        @endif
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <p class="text-gray-500">No tickets found</p>
                    </div>
                    @endforelse
                </div>
            </div>

            <!-- Payment History/Actions -->
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="500">
                <h3 class="text-lg font-semibold mb-4">Payment Actions</h3>
                <div class="space-y-3">
                    @if($order->status === 'pending')
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-yellow-800">Payment Pending</h4>
                                <p class="text-sm text-yellow-700 mt-1">This payment is waiting for approval. Review the details and approve or reject the payment.</p>
                            </div>
                        </div>
                    </div>
                    @elseif($order->status === 'completed')
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-green-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-green-800">Payment Completed</h4>
                                <p class="text-sm text-green-700 mt-1">This payment has been successfully processed and tickets have been issued.</p>
                            </div>
                        </div>
                    </div>
                    @else
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-red-800">Payment Failed</h4>
                                <p class="text-sm text-red-700 mt-1">This payment has been rejected or failed to process.</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
