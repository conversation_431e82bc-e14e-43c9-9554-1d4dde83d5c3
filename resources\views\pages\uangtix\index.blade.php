@extends('layouts.main')

@section('title', 'UangTix - Dompet Digital')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="wallet" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">UangTix</h1>
                            <p class="text-gray-600 dark:text-gray-400">Dompet digital untuk transaksi di TiXara</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="{{ route('uangtix.balance') }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="trending-up" class="w-4 h-4 mr-2"></i>
                        Detail Saldo
                    </a>

                    @if($exchangeRate->deposits_enabled)
                    <button type="button"
                            onclick="showDepositModal()"
                            class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Deposit
                    </button>
                    @endif

                    @if($exchangeRate->withdrawals_enabled && $balance->balance > 0)
                    <button type="button"
                            onclick="showWithdrawModal()"
                            class="inline-flex items-center px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="minus" class="w-4 h-4 mr-2"></i>
                        Tarik
                    </button>
                    @endif

                    @if($exchangeRate->transfers_enabled && $balance->balance > 0)
                    <button type="button"
                            onclick="showTransferModal()"
                            class="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="arrow-right-left" class="w-4 h-4 mr-2"></i>
                        Transfer
                    </button>
                    @endif
                </div>
            </div>
        </div>

        <!-- Balance Card -->
        <div class="bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-2xl shadow-xl p-8 mb-8 text-white relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 bg-black bg-opacity-10"></div>
            <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -ml-12 -mb-12"></div>

            <div class="relative z-10">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="mb-4 sm:mb-0">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                <i data-lucide="wallet" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <p class="text-white/80 text-sm font-medium">Saldo UangTix Anda</p>
                                <h2 class="text-3xl sm:text-4xl font-bold text-white">{{ $balance->formatted_balance }}</h2>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                            <p class="text-white/90 text-sm">
                                Setara dengan <span class="font-semibold">{{ $balance->formatted_balance_idr }}</span>
                            </p>
                        </div>
                    </div>

                    <div class="flex items-center justify-center sm:justify-end">
                        <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center">
                            <i data-lucide="coins" class="w-10 h-10 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Earned -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-600 dark:text-green-400 uppercase tracking-wide">Total Earned</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_earned'], 0, ',', '.') }} <span class="text-lg text-gray-500">UTX</span>
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="trending-up" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Total Spent -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-red-600 dark:text-red-400 uppercase tracking-wide">Total Spent</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_spent'], 0, ',', '.') }} <span class="text-lg text-gray-500">UTX</span>
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="trending-down" class="w-6 h-6 text-red-600 dark:text-red-400"></i>
                    </div>
                </div>
            </div>

            <!-- Monthly Transactions -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">Transaksi Bulan Ini</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['transactions_this_month'], 0, ',', '.') }}
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="calendar" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>

            <!-- Account Status -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">Status Akun</p>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $balance->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' }}">
                                <div class="w-1.5 h-1.5 rounded-full {{ $balance->is_active ? 'bg-green-400' : 'bg-red-400' }} mr-1"></div>
                                {{ $balance->is_active ? 'Aktif' : 'Nonaktif' }}
                            </span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="user-check" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
            </div>
        </div>

    <!-- Exchange Rate Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-left-warning shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-warning font-weight-bold">Kurs Saat Ini</h6>
                            <div class="small">
                                <div>{{ $exchangeRate->formatted_rates['idr_to_uangtix'] }}</div>
                                <div>{{ $exchangeRate->formatted_rates['uangtix_to_idr'] }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-info font-weight-bold">Batas Transaksi</h6>
                            <div class="small">
                                <div>Min Deposit: {{ $exchangeRate->formatted_limits['min_deposit'] }}</div>
                                <div>Max Deposit: {{ $exchangeRate->formatted_limits['max_deposit'] }}</div>
                                <div>Min Penarikan: {{ $exchangeRate->formatted_limits['min_withdrawal'] }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-success font-weight-bold">Biaya Transaksi</h6>
                            <div class="small">
                                <div>Biaya Deposit: {{ $exchangeRate->formatted_fees['deposit_fee'] }}</div>
                                <div>Biaya Penarikan: {{ $exchangeRate->formatted_fees['withdrawal_fee'] }}</div>
                                <div>Biaya Transfer: 0%</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-primary font-weight-bold">Status Layanan</h6>
                            <div class="small">
                                <div>
                                    <span class="badge badge-{{ $exchangeRate->deposits_enabled ? 'success' : 'danger' }}">
                                        Deposit {{ $exchangeRate->deposits_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-{{ $exchangeRate->withdrawals_enabled ? 'success' : 'danger' }}">
                                        Penarikan {{ $exchangeRate->withdrawals_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-{{ $exchangeRate->transfers_enabled ? 'success' : 'danger' }}">
                                        Transfer {{ $exchangeRate->transfers_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests -->
    @if($pendingRequests->count() > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock me-2"></i>
                        Permintaan Pending
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>No. Permintaan</th>
                                    <th>Jenis</th>
                                    <th>Jumlah</th>
                                    <th>Status</th>
                                    <th>Tanggal</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($pendingRequests as $request)
                                <tr>
                                    <td>{{ $request->request_number }}</td>
                                    <td>
                                        <span class="badge badge-{{ $request->type == 'deposit' ? 'success' : 'warning' }}">
                                            {{ $request->type_label }}
                                        </span>
                                    </td>
                                    <td>{{ $request->formatted_amount }}</td>
                                    <td>
                                        <span class="badge badge-{{ $request->status_color }}">
                                            {{ $request->status_label }}
                                        </span>
                                    </td>
                                    <td>{{ $request->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Recent Transactions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        Transaksi Terbaru
                    </h6>
                    <a href="{{ route('uangtix.transactions') }}" class="btn btn-sm btn-outline-primary">
                        Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    @if($recentTransactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>Jenis</th>
                                        <th>Deskripsi</th>
                                        <th>Jumlah</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentTransactions as $transaction)
                                    <tr>
                                        <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
                                        <td>
                                            <span class="badge badge-{{ $transaction->type_color }}">
                                                <i class="{{ $transaction->type_icon }} me-1"></i>
                                                {{ $transaction->type_label }}
                                            </span>
                                        </td>
                                        <td>{{ $transaction->description }}</td>
                                        <td>
                                            <span class="font-weight-bold text-{{ $transaction->amount > 0 ? 'success' : 'danger' }}">
                                                {{ $transaction->formatted_amount }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-{{ $transaction->status_color }}">
                                                {{ $transaction->status_label }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">Belum ada transaksi UangTix</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deposit Modal -->
<div class="modal fade" id="depositModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus text-success me-2"></i>
                    Deposit UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="depositForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="depositAmount" class="form-label">Jumlah Deposit (IDR)</label>
                        <input type="number" class="form-control" id="depositAmount" name="amount_idr"
                               min="10000" max="10000000" required>
                        <div class="form-text">
                            Minimum: Rp 10,000 | Maksimum: Rp 10,000,000
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">Metode Pembayaran</label>
                        <select class="form-control" id="paymentMethod" name="payment_method" required>
                            <option value="">Pilih metode pembayaran</option>
                            <option value="bank_transfer">Transfer Bank</option>
                            <option value="e_wallet">E-Wallet</option>
                            <option value="virtual_account">Virtual Account</option>
                        </select>
                    </div>
                    <div id="depositCalculation" class="alert alert-info" style="display: none;">
                        <h6>Rincian Deposit:</h6>
                        <div id="calculationDetails"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Buat Permintaan Deposit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-minus text-warning me-2"></i>
                    Tarik UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="withdrawForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="withdrawAmount" class="form-label">Jumlah Penarikan (UTX)</label>
                        <input type="number" class="form-control" id="withdrawAmount" name="amount_uangtix"
                               min="10" max="{{ $balance->balance }}" required>
                        <div class="form-text">
                            Saldo tersedia: {{ $balance->formatted_balance }}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="bankName" class="form-label">Nama Bank</label>
                        <input type="text" class="form-control" id="bankName" name="bank_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="bankAccountNumber" class="form-label">Nomor Rekening</label>
                        <input type="text" class="form-control" id="bankAccountNumber" name="bank_account_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="bankAccountName" class="form-label">Nama Pemilik Rekening</label>
                        <input type="text" class="form-control" id="bankAccountName" name="bank_account_name" required>
                    </div>
                    <div id="withdrawCalculation" class="alert alert-warning" style="display: none;">
                        <h6>Rincian Penarikan:</h6>
                        <div id="withdrawCalculationDetails"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Buat Permintaan Penarikan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exchange-alt text-info me-2"></i>
                    Transfer UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="transferForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="toUserEmail" class="form-label">Email Penerima</label>
                        <input type="email" class="form-control" id="toUserEmail" name="to_user_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="transferAmount" class="form-label">Jumlah Transfer (UTX)</label>
                        <input type="number" class="form-control" id="transferAmount" name="amount"
                               min="1" max="{{ $balance->balance }}" required>
                        <div class="form-text">
                            Saldo tersedia: {{ $balance->formatted_balance }}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="transferDescription" class="form-label">Keterangan (Opsional)</label>
                        <textarea class="form-control" id="transferDescription" name="description"
                                  rows="3" placeholder="Keterangan transfer..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-info">Transfer UangTix</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Deposit form handling
document.getElementById('depositForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    try {
        const response = await fetch('/uangtix/deposit', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('depositModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

// Withdraw form handling
document.getElementById('withdrawForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    try {
        const response = await fetch('/uangtix/withdraw', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('withdrawModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

// Transfer form handling
document.getElementById('transferForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    try {
        const response = await fetch('/uangtix/transfer', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

// Real-time amount calculation for deposit
document.getElementById('depositAmount').addEventListener('input', function() {
    const amount = parseFloat(this.value) || 0;
    const exchangeRate = {{ $exchangeRate->rate_idr_to_uangtix }};
    const feePercentage = {{ $exchangeRate->deposit_fee_percentage }};

    if (amount >= {{ $exchangeRate->min_deposit_idr }}) {
        const fee = amount * (feePercentage / 100);
        const netAmount = amount - fee;
        const uangTixAmount = netAmount * exchangeRate;

        document.getElementById('depositCalculation').style.display = 'block';
        document.getElementById('calculationDetails').innerHTML = `
            <div class="row">
                <div class="col-6">Jumlah Deposit:</div>
                <div class="col-6 text-end">Rp ${amount.toLocaleString('id-ID')}</div>
            </div>
            <div class="row">
                <div class="col-6">Biaya Admin (${feePercentage}%):</div>
                <div class="col-6 text-end text-danger">-Rp ${fee.toLocaleString('id-ID')}</div>
            </div>
            <hr>
            <div class="row font-weight-bold">
                <div class="col-6">UangTix yang diterima:</div>
                <div class="col-6 text-end text-success">${Math.floor(uangTixAmount).toLocaleString('id-ID')} UTX</div>
            </div>
        `;
    } else {
        document.getElementById('depositCalculation').style.display = 'none';
    }
});

// Real-time amount calculation for withdrawal
document.getElementById('withdrawAmount').addEventListener('input', function() {
    const amount = parseFloat(this.value) || 0;
    const exchangeRate = {{ $exchangeRate->rate_uangtix_to_idr }};
    const feePercentage = {{ $exchangeRate->withdrawal_fee_percentage }};

    if (amount >= {{ $exchangeRate->min_withdrawal_uangtix }}) {
        const grossIdr = amount * exchangeRate;
        const fee = grossIdr * (feePercentage / 100);
        const netIdr = grossIdr - fee;

        document.getElementById('withdrawCalculation').style.display = 'block';
        document.getElementById('withdrawCalculationDetails').innerHTML = `
            <div class="row">
                <div class="col-6">UangTix ditarik:</div>
                <div class="col-6 text-end">${amount.toLocaleString('id-ID')} UTX</div>
            </div>
            <div class="row">
                <div class="col-6">Nilai dalam IDR:</div>
                <div class="col-6 text-end">Rp ${grossIdr.toLocaleString('id-ID')}</div>
            </div>
            <div class="row">
                <div class="col-6">Biaya Admin (${feePercentage}%):</div>
                <div class="col-6 text-end text-danger">-Rp ${fee.toLocaleString('id-ID')}</div>
            </div>
            <hr>
            <div class="row font-weight-bold">
                <div class="col-6">Yang diterima:</div>
                <div class="col-6 text-end text-success">Rp ${Math.floor(netIdr).toLocaleString('id-ID')}</div>
            </div>
        `;
    } else {
        document.getElementById('withdrawCalculation').style.display = 'none';
    }
});

function showAlert(type, message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>
@endpush
