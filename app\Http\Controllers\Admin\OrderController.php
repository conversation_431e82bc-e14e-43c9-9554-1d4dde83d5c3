<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of orders
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'event', 'tickets']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('event', function($eventQuery) use ($search) {
                      $eventQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Payment status filter
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Amount range filter
        if ($request->filled('amount_min')) {
            $query->where('total_amount', '>=', $request->amount_min);
        }
        if ($request->filled('amount_max')) {
            $query->where('total_amount', '<=', $request->amount_max);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        
        $allowedSorts = ['created_at', 'total_amount', 'status', 'payment_status'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $orders = $query->paginate(15)->withQueryString();

        // Statistics
        $stats = [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'completed_orders' => Order::where('status', 'completed')->count(),
            'cancelled_orders' => Order::where('status', 'cancelled')->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'pending_payments' => Order::where('payment_status', 'pending')->count(),
            'failed_payments' => Order::where('payment_status', 'failed')->count(),
            'today_orders' => Order::whereDate('created_at', today())->count(),
            'today_revenue' => Order::whereDate('created_at', today())
                                   ->where('payment_status', 'paid')
                                   ->sum('total_amount'),
        ];

        return view('pages.admin.orders.index', compact('orders', 'stats'));
    }

    /**
     * Display the specified order
     */
    public function show(Order $order)
    {
        $order->load(['user', 'event', 'tickets.buyer']);

        // Order timeline/activities
        $activities = [
            [
                'type' => 'created',
                'title' => 'Order Created',
                'description' => 'Order was created by ' . $order->user->name,
                'timestamp' => $order->created_at,
                'icon' => 'plus-circle',
                'color' => 'blue'
            ]
        ];

        if ($order->payment_status === 'paid') {
            $activities[] = [
                'type' => 'payment',
                'title' => 'Payment Received',
                'description' => 'Payment of Rp ' . number_format($order->total_amount, 0, ',', '.') . ' was received',
                'timestamp' => $order->updated_at,
                'icon' => 'credit-card',
                'color' => 'green'
            ];
        }

        if ($order->status === 'completed') {
            $activities[] = [
                'type' => 'completed',
                'title' => 'Order Completed',
                'description' => 'All tickets have been issued',
                'timestamp' => $order->updated_at,
                'icon' => 'check-circle',
                'color' => 'green'
            ];
        }

        if ($order->status === 'cancelled') {
            $activities[] = [
                'type' => 'cancelled',
                'title' => 'Order Cancelled',
                'description' => 'Order was cancelled',
                'timestamp' => $order->updated_at,
                'icon' => 'x-circle',
                'color' => 'red'
            ];
        }

        // Sort activities by timestamp
        usort($activities, function($a, $b) {
            return $a['timestamp']->timestamp - $b['timestamp']->timestamp;
        });

        return view('pages.admin.orders.show', compact('order', 'activities'));
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,processing,completed,cancelled',
            'notes' => 'nullable|string|max:500'
        ]);

        $oldStatus = $order->status;
        $order->update([
            'status' => $request->status,
            'admin_notes' => $request->notes
        ]);

        // If status changed to completed, update tickets
        if ($request->status === 'completed' && $oldStatus !== 'completed') {
            $order->tickets()->update(['status' => 'active']);
        }

        // If status changed to cancelled, update tickets
        if ($request->status === 'cancelled' && $oldStatus !== 'cancelled') {
            $order->tickets()->update(['status' => 'cancelled']);
            
            // Return capacity to event
            $order->event->increment('available_capacity', $order->quantity);
        }

        return redirect()->back()->with('success', 'Order status updated successfully');
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(Request $request, Order $order)
    {
        $request->validate([
            'payment_status' => 'required|in:pending,paid,failed,refunded',
            'payment_notes' => 'nullable|string|max:500'
        ]);

        $order->update([
            'payment_status' => $request->payment_status,
            'payment_notes' => $request->payment_notes
        ]);

        // If payment is confirmed, auto-complete the order
        if ($request->payment_status === 'paid' && $order->status === 'pending') {
            $order->update(['status' => 'completed']);
            $order->tickets()->update(['status' => 'active']);
        }

        return redirect()->back()->with('success', 'Payment status updated successfully');
    }

    /**
     * Bulk actions for orders
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:update_status,update_payment_status,delete',
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id',
            'bulk_status' => 'required_if:action,update_status|in:pending,processing,completed,cancelled',
            'bulk_payment_status' => 'required_if:action,update_payment_status|in:pending,paid,failed,refunded'
        ]);

        $orders = Order::whereIn('id', $request->order_ids);

        switch ($request->action) {
            case 'update_status':
                $orders->update(['status' => $request->bulk_status]);
                $message = 'Order status updated for selected orders';
                break;

            case 'update_payment_status':
                $orders->update(['payment_status' => $request->bulk_payment_status]);
                $message = 'Payment status updated for selected orders';
                break;

            case 'delete':
                // Only allow deletion of cancelled orders
                $orders->where('status', 'cancelled')->delete();
                $message = 'Selected cancelled orders have been deleted';
                break;
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Export orders data
     */
    public function export(Request $request)
    {
        $query = Order::with(['user', 'event']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->get();

        $filename = 'orders_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');
            
            // CSV Headers
            fputcsv($file, [
                'Order Number',
                'Customer Name',
                'Customer Email',
                'Event Title',
                'Quantity',
                'Total Amount',
                'Status',
                'Payment Status',
                'Order Date',
                'Payment Date'
            ]);

            // CSV Data
            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_number,
                    $order->user->name,
                    $order->user->email,
                    $order->event->title,
                    $order->quantity,
                    $order->total_amount,
                    ucfirst($order->status),
                    ucfirst($order->payment_status),
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->payment_status === 'paid' ? $order->updated_at->format('Y-m-d H:i:s') : '-'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
