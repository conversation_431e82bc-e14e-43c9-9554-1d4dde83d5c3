{{--
/**
 * Developer Page
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: <PERSON><PERSON><PERSON>zu<PERSON> P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */
--}}

@extends('layouts.app')

@section('title', 'Developer - TiXara')

@push('styles')
<style>
/* Developer Page Styles */
.developer-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
    position: relative;
    overflow: hidden;
}

.developer-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="code" width="20" height="20" patternUnits="userSpaceOnUse"><text x="2" y="15" font-family="monospace" font-size="12" fill="rgba(255,255,255,0.1)">&lt;/&gt;</text></pattern></defs><rect width="100" height="100" fill="url(%23code)"/></svg>');
    opacity: 0.3;
}

.tech-card {
    background: white;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.tech-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3a8a, #3b82f6, #06b6d4);
}

.tech-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.tech-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    position: relative;
}

.tech-icon.frontend {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.tech-icon.backend {
    background: linear-gradient(135deg, #4834d4, #686de0);
}

.tech-icon.database {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
}

.tech-icon.tools {
    background: linear-gradient(135deg, #5f27cd, #a55eea);
}

.developer-profile {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24px;
    padding: 40px;
    color: white;
    position: relative;
    overflow: hidden;
}

.developer-profile::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
}

.social-link.github {
    background: #333;
}

.social-link.linkedin {
    background: #0077b5;
}

.social-link.email {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 32px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 8px;
    width: 16px;
    height: 16px;
    background: #3b82f6;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 0 0 2px #3b82f6;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 32px;
    width: 2px;
    height: calc(100% - 16px);
    background: #e5e7eb;
}

.timeline-item:last-child::after {
    display: none;
}

@media (max-width: 768px) {
    .tech-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
</style>
@endpush

@section('content')
<!-- Hero Section -->
<div class="developer-hero text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 backdrop-blur-sm">
                <i data-lucide="code" class="w-10 h-10 text-white"></i>
            </div>
            <h1 class="text-5xl md:text-6xl font-bold mb-6">
                Developer
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                Meet the Mind Behind TiXara
            </p>
            <p class="text-lg mb-12 max-w-2xl mx-auto opacity-80">
                Mengenal lebih dekat developer yang membangun platform TiXara dengan teknologi modern dan inovasi terdepan.
            </p>
        </div>
    </div>
</div>

<!-- Developer Profile -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="developer-profile">
            <div class="relative z-10">
                <div class="flex flex-col md:flex-row items-center gap-8">
                    <div class="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <i data-lucide="user" class="w-16 h-16 text-white"></i>
                    </div>
                    <div class="text-center md:text-left flex-1">
                        <h2 class="text-3xl font-bold mb-2">Dhafa Nazula P</h2>
                        <p class="text-xl opacity-90 mb-4">Full Stack Developer</p>
                        <p class="text-lg opacity-80 mb-6">
                            Passionate developer dengan pengalaman dalam membangun aplikasi web modern menggunakan Laravel, Vue.js, dan teknologi terkini lainnya.
                        </p>
                        <div class="flex justify-center md:justify-start space-x-4">
                            <a href="https://instagram.com/seehai.dhafa" target="_blank" class="social-link instagram">
                                <i data-lucide="instagram" class="w-6 h-6 text-white"></i>
                            </a>
                            <a href="https://github.com/dhafanazula" target="_blank" class="social-link github">
                                <i data-lucide="github" class="w-6 h-6 text-white"></i>
                            </a>
                            <a href="https://linkedin.com/in/dhafanazula" target="_blank" class="social-link linkedin">
                                <i data-lucide="linkedin" class="w-6 h-6 text-white"></i>
                            </a>
                            <a href="mailto:<EMAIL>" class="social-link email">
                                <i data-lucide="mail" class="w-6 h-6 text-white"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tech Stack -->
<div class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Technology Stack
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Teknologi modern yang digunakan untuk membangun TiXara
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 tech-grid">
            <!-- Frontend -->
            <div class="tech-card">
                <div class="tech-icon frontend">
                    <i data-lucide="monitor" class="w-10 h-10 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Frontend</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Laravel Blade
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Tailwind CSS
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Alpine.js
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Lucide Icons
                    </li>
                </ul>
            </div>

            <!-- Backend -->
            <div class="tech-card">
                <div class="tech-icon backend">
                    <i data-lucide="server" class="w-10 h-10 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Backend</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Laravel 10
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        PHP 8.2
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        RESTful API
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Laravel Sanctum
                    </li>
                </ul>
            </div>

            <!-- Database -->
            <div class="tech-card">
                <div class="tech-icon database">
                    <i data-lucide="database" class="w-10 h-10 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Database</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        MySQL 8.0
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Redis Cache
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Eloquent ORM
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Database Migration
                    </li>
                </ul>
            </div>

            <!-- Tools -->
            <div class="tech-card">
                <div class="tech-icon tools">
                    <i data-lucide="wrench" class="w-10 h-10 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Tools & DevOps</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Git & GitHub
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Docker
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Composer
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        NPM/Yarn
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Development Timeline -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Development Journey
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">
                Perjalanan pengembangan platform TiXara
            </p>
        </div>

        <div class="space-y-8">
            <div class="timeline-item">
                <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Project Initiation
                    </h3>
                    <p class="text-blue-600 dark:text-blue-400 text-sm font-medium mb-2">Q1 2024</p>
                    <p class="text-gray-600 dark:text-gray-300">
                        Memulai pengembangan TiXara dengan konsep platform ticketing yang user-friendly dan modern.
                    </p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Core Features Development
                    </h3>
                    <p class="text-blue-600 dark:text-blue-400 text-sm font-medium mb-2">Q2 2024</p>
                    <p class="text-gray-600 dark:text-gray-300">
                        Mengembangkan fitur inti seperti user management, event creation, ticket booking, dan payment system.
                    </p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        Advanced Features
                    </h3>
                    <p class="text-blue-600 dark:text-blue-400 text-sm font-medium mb-2">Q3 2024</p>
                    <p class="text-gray-600 dark:text-gray-300">
                        Menambahkan fitur lanjutan seperti analytics dashboard, notification system, dan multi-role management.
                    </p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        ArtPosure Marketing Suite
                    </h3>
                    <p class="text-blue-600 dark:text-blue-400 text-sm font-medium mb-2">Q4 2024</p>
                    <p class="text-gray-600 dark:text-gray-300">
                        Meluncurkan ArtPosure - layanan marketing terintegrasi untuk event organizer dengan berbagai paket dan add-on.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Company Information -->
<div class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-8">
            About BintangCode
        </h2>
        
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <div class="max-w-2xl mx-auto">
                <h3 class="text-2xl font-bold mb-4">CV Bintang Gumilang Group</h3>
                <p class="text-lg opacity-90 mb-6">
                    BintangCode adalah sub holding dari CV Bintang Gumilang Group yang fokus pada pengembangan solusi teknologi inovatif untuk berbagai industri.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                    <div>
                        <div class="text-3xl font-bold">5+</div>
                        <div class="text-sm opacity-80">Years Experience</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold">50+</div>
                        <div class="text-sm opacity-80">Projects Completed</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold">100+</div>
                        <div class="text-sm opacity-80">Happy Clients</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contact Developer -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
            Get In Touch
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Tertarik untuk berkolaborasi atau punya pertanyaan teknis?
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" 
               class="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i data-lucide="mail" class="w-5 h-5 mr-2"></i>
                Email Developer
            </a>
            
            <a href="https://instagram.com/seehai.dhafa" target="_blank"
               class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-xl hover:from-pink-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i data-lucide="instagram" class="w-5 h-5 mr-2"></i>
                Follow Instagram
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
/*
 * Developer Page JavaScript
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Animate timeline items on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe timeline items
    document.querySelectorAll('.timeline-item').forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(item);
    });
});
</script>
@endpush
