<?php

namespace App\Services;

use App\Models\Voucher;
use App\Models\VoucherUsage;
use App\Models\Event;
use App\Models\User;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class VoucherService
{
    /**
     * Validate and apply voucher to order
     */
    public function validateAndApplyVoucher(string $voucherCode, Event $event, User $user, float $orderAmount, int $ticketQuantity = 1): array
    {
        try {
            // Find voucher
            $voucher = Voucher::where('code', strtoupper($voucherCode))->first();
            
            if (!$voucher) {
                return [
                    'success' => false,
                    'message' => 'Kode voucher tidak ditemukan.',
                    'voucher' => null,
                    'discount' => 0,
                    'final_amount' => $orderAmount
                ];
            }

            // Validate voucher
            $validation = $this->validateVoucher($voucher, $event, $user, $orderAmount, $ticketQuantity);
            
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message'],
                    'voucher' => $voucher,
                    'discount' => 0,
                    'final_amount' => $orderAmount
                ];
            }

            // Calculate discount
            $discountResult = $voucher->calculateDiscount($orderAmount, $ticketQuantity);
            
            if (!$discountResult['eligible']) {
                return [
                    'success' => false,
                    'message' => $discountResult['reason'],
                    'voucher' => $voucher,
                    'discount' => 0,
                    'final_amount' => $orderAmount
                ];
            }

            return [
                'success' => true,
                'message' => 'Voucher berhasil diterapkan!',
                'voucher' => $voucher,
                'discount' => $discountResult['discount'],
                'final_amount' => $discountResult['final_amount'],
                'savings_percentage' => $discountResult['savings_percentage']
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan saat memvalidasi voucher.',
                'voucher' => null,
                'discount' => 0,
                'final_amount' => $orderAmount
            ];
        }
    }

    /**
     * Validate voucher eligibility
     */
    public function validateVoucher(Voucher $voucher, Event $event, User $user, float $orderAmount, int $ticketQuantity = 1): array
    {
        // Check if voucher is valid
        if (!$voucher->isValid()) {
            if (!$voucher->is_active) {
                return ['valid' => false, 'message' => 'Voucher tidak aktif.'];
            }
            
            if ($voucher->starts_at > now()) {
                return ['valid' => false, 'message' => 'Voucher belum dapat digunakan.'];
            }
            
            if ($voucher->expires_at < now()) {
                return ['valid' => false, 'message' => 'Voucher sudah kedaluwarsa.'];
            }
        }

        // Check if voucher is available
        if (!$voucher->isAvailable()) {
            return ['valid' => false, 'message' => 'Voucher sudah habis digunakan.'];
        }

        // Check user eligibility
        if (!$voucher->canBeUsedBy($user)) {
            $userUsageCount = $voucher->usages()->where('user_id', $user->id)->count();
            if ($userUsageCount >= $voucher->usage_limit_per_user) {
                return ['valid' => false, 'message' => 'Anda sudah mencapai batas penggunaan voucher ini.'];
            }
            
            if (!empty($voucher->applicable_user_roles) && !in_array($user->role, $voucher->applicable_user_roles)) {
                return ['valid' => false, 'message' => 'Voucher tidak berlaku untuk role Anda.'];
            }
        }

        // Check event eligibility
        if (!$voucher->canBeAppliedToEvent($event)) {
            if (!empty($voucher->applicable_events) && !in_array($event->id, $voucher->applicable_events)) {
                return ['valid' => false, 'message' => 'Voucher tidak berlaku untuk event ini.'];
            }
            
            if (!empty($voucher->applicable_categories) && !in_array($event->category_id, $voucher->applicable_categories)) {
                return ['valid' => false, 'message' => 'Voucher tidak berlaku untuk kategori event ini.'];
            }
        }

        return ['valid' => true, 'message' => 'Voucher valid.'];
    }

    /**
     * Apply voucher to order and create usage record
     */
    public function applyVoucherToOrder(Order $order, Voucher $voucher, float $discountAmount): void
    {
        DB::transaction(function () use ($order, $voucher, $discountAmount) {
            // Update order with voucher information
            $order->update([
                'voucher_id' => $voucher->id,
                'voucher_code' => $voucher->code,
                'voucher_discount' => $discountAmount,
                'total_amount' => $order->subtotal + $order->admin_fee - $discountAmount - $order->discount_amount,
            ]);

            // Create voucher usage record
            VoucherUsage::create([
                'voucher_id' => $voucher->id,
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'event_id' => $order->event_id,
                'original_amount' => $order->subtotal + $order->admin_fee,
                'discount_amount' => $discountAmount,
                'final_amount' => $order->total_amount,
                'tickets_quantity' => $order->quantity,
                'voucher_snapshot' => $voucher->toArray(),
                'applied_via' => 'manual',
                'used_at' => now(),
            ]);

            // Increment voucher usage count
            $voucher->incrementUsage();
        });
    }

    /**
     * Remove voucher from order
     */
    public function removeVoucherFromOrder(Order $order): void
    {
        if (!$order->voucher_id) {
            return;
        }

        DB::transaction(function () use ($order) {
            $voucher = $order->voucher;
            $voucherDiscount = $order->voucher_discount;

            // Update order
            $order->update([
                'voucher_id' => null,
                'voucher_code' => null,
                'voucher_discount' => 0,
                'total_amount' => $order->subtotal + $order->admin_fee - $order->discount_amount,
            ]);

            // Remove usage record
            VoucherUsage::where('order_id', $order->id)->delete();

            // Decrement voucher usage count
            if ($voucher) {
                $voucher->decrement('used_count');
            }
        });
    }

    /**
     * Get available vouchers for user and event
     */
    public function getAvailableVouchers(Event $event, User $user, float $orderAmount = 0, int $ticketQuantity = 1): array
    {
        $vouchers = Voucher::available()
            ->public()
            ->where(function ($query) use ($event) {
                $query->whereNull('applicable_events')
                      ->orWhereJsonContains('applicable_events', $event->id);
            })
            ->where(function ($query) use ($event) {
                $query->whereNull('applicable_categories')
                      ->orWhereJsonContains('applicable_categories', $event->category_id);
            })
            ->where(function ($query) use ($user) {
                $query->whereNull('applicable_user_roles')
                      ->orWhereJsonContains('applicable_user_roles', $user->role);
            })
            ->where('min_order_amount', '<=', $orderAmount)
            ->where('min_tickets', '<=', $ticketQuantity)
            ->where(function ($query) use ($ticketQuantity) {
                $query->whereNull('max_tickets')
                      ->orWhere('max_tickets', '>=', $ticketQuantity);
            })
            ->get();

        $availableVouchers = [];

        foreach ($vouchers as $voucher) {
            if ($voucher->canBeUsedBy($user)) {
                $discountResult = $voucher->calculateDiscount($orderAmount, $ticketQuantity);
                
                if ($discountResult['eligible']) {
                    $availableVouchers[] = [
                        'voucher' => $voucher,
                        'discount' => $discountResult['discount'],
                        'final_amount' => $discountResult['final_amount'],
                        'savings_percentage' => $discountResult['savings_percentage']
                    ];
                }
            }
        }

        // Sort by discount amount (highest first)
        usort($availableVouchers, function ($a, $b) {
            return $b['discount'] <=> $a['discount'];
        });

        return $availableVouchers;
    }

    /**
     * Get voucher statistics
     */
    public function getVoucherStatistics(Voucher $voucher): array
    {
        $usages = $voucher->usages();

        return [
            'total_usage' => $usages->count(),
            'total_savings' => $usages->sum('discount_amount'),
            'unique_users' => $usages->distinct('user_id')->count(),
            'avg_discount' => $usages->avg('discount_amount'),
            'usage_percentage' => $voucher->usage_percentage,
            'remaining_usage' => $voucher->remaining_usage,
            'days_until_expiry' => $voucher->expires_at->diffInDays(now()),
            'is_trending' => $usages->where('used_at', '>=', now()->subDays(7))->count() > 5,
        ];
    }

    /**
     * Auto-apply best voucher for order
     */
    public function autoApplyBestVoucher(Event $event, User $user, float $orderAmount, int $ticketQuantity = 1): ?array
    {
        $availableVouchers = $this->getAvailableVouchers($event, $user, $orderAmount, $ticketQuantity);
        
        if (empty($availableVouchers)) {
            return null;
        }

        // Return the voucher with highest discount
        return $availableVouchers[0];
    }
}
