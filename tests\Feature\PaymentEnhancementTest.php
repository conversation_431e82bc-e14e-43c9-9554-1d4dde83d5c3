<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Event;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PaymentEnhancementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $event;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->event = Event::factory()->create([
            'price' => 100000,
            'available_capacity' => 100,
            'max_purchase' => 5,
        ]);
    }

    /** @test */
    public function user_can_access_purchase_page_with_enhanced_form()
    {
        $response = $this->actingAs($this->user)
            ->get(route('tickets.purchase', $this->event));

        $response->assertStatus(200);
        $response->assertSee('Jenis Kelamin');
        $response->assertSee('Tanggal Lahir');
        $response->assertSee('<PERSON><PERSON><PERSON>jaan');
        $response->assertSee('Kontak Darurat');
        $response->assertSee('ticketPurchase()'); // Alpine.js function
    }

    /** @test */
    public function purchase_form_validates_required_fields()
    {
        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), [
                'quantity' => 1,
                'payment_method' => 'bank_transfer',
                'terms_accepted' => '1',
                // Missing required fields
            ]);

        $response->assertSessionHasErrors([
            'attendee_name',
            'attendee_email',
            'attendee_phone',
            'attendee_gender',
            'attendee_birth_date',
            'identity_type',
            'identity_number',
        ]);
    }

    /** @test */
    public function purchase_form_validates_phone_number_format()
    {
        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), [
                'quantity' => 1,
                'attendee_name' => 'John Doe',
                'attendee_email' => '<EMAIL>',
                'attendee_phone' => '*********', // Invalid format
                'attendee_gender' => 'male',
                'attendee_birth_date' => '1990-01-01',
                'identity_type' => 'ktp',
                'identity_number' => '*********0123456',
                'payment_method' => 'bank_transfer',
                'terms_accepted' => '1',
            ]);

        $response->assertSessionHasErrors(['attendee_phone']);
    }

    /** @test */
    public function purchase_form_validates_identity_number_format()
    {
        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), [
                'quantity' => 1,
                'attendee_name' => 'John Doe',
                'attendee_email' => '<EMAIL>',
                'attendee_phone' => '08*********0',
                'attendee_gender' => 'male',
                'attendee_birth_date' => '1990-01-01',
                'identity_type' => 'ktp',
                'identity_number' => '123', // Invalid KTP format
                'payment_method' => 'bank_transfer',
                'terms_accepted' => '1',
            ]);

        $response->assertSessionHasErrors(['identity_number']);
    }

    /** @test */
    public function successful_purchase_creates_order_and_redirects_to_payment()
    {
        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), [
                'quantity' => 2,
                'attendee_name' => 'John Doe',
                'attendee_email' => '<EMAIL>',
                'attendee_phone' => '08*********0',
                'attendee_gender' => 'male',
                'attendee_birth_date' => '1990-01-01',
                'attendee_occupation' => 'Software Engineer',
                'identity_type' => 'ktp',
                'identity_number' => '*********0123456',
                'emergency_contact_name' => 'Jane Doe',
                'emergency_contact_phone' => '************',
                'payment_method' => 'bank_transfer',
                'terms_accepted' => '1',
            ]);

        $this->assertDatabaseHas('orders', [
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'quantity' => 2,
            'payment_method' => 'bank_transfer',
            'payment_status' => 'pending',
        ]);

        $order = Order::where('user_id', $this->user->id)->first();
        $response->assertRedirect(route('orders.payment', $order));
    }

    /** @test */
    public function payment_status_check_returns_correct_data()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'payment_status' => 'pending',
            'expires_at' => now()->addMinutes(30),
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('orders.check-status', $order));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'order_status',
            'expires_at',
            'total_amount',
            'order_number',
            'payment_method',
            'message',
            'remaining_seconds',
            'remaining_minutes',
        ]);
    }

    /** @test */
    public function payment_status_check_handles_paid_orders()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'payment_status' => 'paid',
            'paid_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('orders.check-status', $order));

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'paid',
            'message' => 'Pembayaran berhasil! Tiket Anda sudah aktif.',
        ]);
        $response->assertJsonHas('success_url');
        $response->assertJsonHas('tickets_count');
    }

    /** @test */
    public function payment_status_check_handles_expired_orders()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'payment_status' => 'pending',
            'expires_at' => now()->subMinutes(5), // Expired
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('orders.check-status', $order));

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'expired',
            'order_status' => 'cancelled',
            'message' => 'Pesanan telah kedaluwarsa.',
        ]);

        // Check that order was marked as expired
        $order->refresh();
        $this->assertEquals('failed', $order->payment_status);
        $this->assertEquals('cancelled', $order->status);
    }

    /** @test */
    public function unauthorized_user_cannot_check_payment_status()
    {
        $otherUser = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $otherUser->id,
            'event_id' => $this->event->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('orders.check-status', $order));

        $response->assertStatus(403);
    }

    /** @test */
    public function success_page_displays_order_details()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'payment_status' => 'paid',
            'paid_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('orders.success', $order));

        $response->assertStatus(200);
        $response->assertSee('Pembayaran Berhasil!');
        $response->assertSee($order->order_number);
        $response->assertSee('playSuccessSound'); // Success sound function
        $response->assertSee('createConfetti'); // Confetti function
    }

    /** @test */
    public function success_page_redirects_unpaid_orders()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'payment_status' => 'pending',
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('orders.success', $order));

        $response->assertRedirect(route('orders.payment', $order));
    }

    /** @test */
    public function payment_details_page_includes_monitoring_script()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'event_id' => $this->event->id,
            'payment_status' => 'pending',
            'expires_at' => now()->addMinutes(30),
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('orders.payment-details', $order));

        $response->assertStatus(200);
        $response->assertSee('payment-monitoring.js');
        $response->assertSee('PaymentMonitor');
        $response->assertSee('startMonitoring');
    }

    /** @test */
    public function webhook_handlers_are_accessible()
    {
        // Test Xendit webhook endpoint
        $response = $this->post(route('webhooks.xendit'), [
            'event' => 'payment.paid',
            'external_id' => 'test-order',
        ]);
        $response->assertStatus(401); // Should fail due to missing token

        // Test Midtrans webhook endpoint
        $response = $this->post(route('webhooks.midtrans'), [
            'transaction_status' => 'settlement',
            'order_id' => 'test-order',
        ]);
        $response->assertStatus(401); // Should fail due to missing signature

        // Test Tripay webhook endpoint
        $response = $this->post(route('webhooks.tripay'), [
            'status' => 'PAID',
            'merchant_ref' => 'test-order',
        ]);
        $response->assertStatus(401); // Should fail due to missing signature
    }
}
