<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdAnalytic extends Model
{
    use HasFactory;

    protected $fillable = [
        'ad_id',
        'date',
        'impressions',
        'clicks',
        'cost',
        'ctr',
        'cpc',
        'cpm',
        'device_type',
        'browser',
        'location',
        'referrer',
    ];

    protected $casts = [
        'date' => 'date',
        'cost' => 'decimal:2',
        'ctr' => 'decimal:2',
        'cpc' => 'decimal:2',
        'cpm' => 'decimal:2',
    ];

    /**
     * Get the ad that owns the analytic
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope for specific ad
     */
    public function scopeForAd($query, $adId)
    {
        return $query->where('ad_id', $adId);
    }

    /**
     * Get analytics summary for an ad
     */
    public static function getSummaryForAd($adId, $startDate = null, $endDate = null)
    {
        $query = static::forAd($adId);

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        $analytics = $query->get();

        return [
            'total_impressions' => $analytics->sum('impressions'),
            'total_clicks' => $analytics->sum('clicks'),
            'total_cost' => $analytics->sum('cost'),
            'average_ctr' => $analytics->avg('ctr') ?: 0,
            'average_cpc' => $analytics->avg('cpc') ?: 0,
            'average_cpm' => $analytics->avg('cpm') ?: 0,
            'daily_data' => $analytics->map(function ($item) {
                return [
                    'date' => $item->date->format('Y-m-d'),
                    'impressions' => $item->impressions,
                    'clicks' => $item->clicks,
                    'cost' => $item->cost,
                    'ctr' => $item->ctr,
                    'cpc' => $item->cpc,
                    'cpm' => $item->cpm,
                ];
            }),
        ];
    }

    /**
     * Get top performing ads
     */
    public static function getTopPerformingAds($metric = 'clicks', $limit = 10, $startDate = null, $endDate = null)
    {
        $query = static::query();

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return $query->selectRaw('ad_id, SUM(impressions) as total_impressions, SUM(clicks) as total_clicks, SUM(cost) as total_cost, AVG(ctr) as avg_ctr')
                    ->groupBy('ad_id')
                    ->orderBy("total_{$metric}", 'desc')
                    ->limit($limit)
                    ->with('ad')
                    ->get();
    }
}
