@props([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'iconColor' => 'blue',
    'padding' => 'p-6',
    'hover' => true,
    'border' => true,
    'shadow' => 'shadow-sm',
    'rounded' => 'rounded-xl',
    'background' => 'bg-white dark:bg-gray-800',
    'headerClass' => '',
    'bodyClass' => '',
    'footerClass' => '',
    'aos' => null,
    'aosDelay' => null
])

@php
    $iconColors = [
        'blue' => 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
        'green' => 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
        'yellow' => 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400',
        'red' => 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
        'purple' => 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400',
        'orange' => 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400',
        'pink' => 'bg-pink-100 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400',
        'indigo' => 'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400',
        'gray' => 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400',
    ];
    
    $classes = collect([
        $background,
        $rounded,
        $shadow,
        $padding,
        $border ? 'border border-gray-200 dark:border-gray-700' : '',
        $hover ? 'hover-lift' : '',
        'transition-all duration-200'
    ])->filter()->implode(' ');
    
    $aosAttributes = '';
    if ($aos) {
        $aosAttributes .= ' data-aos="' . $aos . '"';
        if ($aosDelay) {
            $aosAttributes .= ' data-aos-delay="' . $aosDelay . '"';
        }
    }
@endphp

<div {{ $attributes->merge(['class' => $classes]) }}{!! $aosAttributes !!}>
    @if($title || $subtitle || $icon || isset($header))
        <div class="flex items-center justify-between mb-6 {{ $headerClass }}">
            <div class="flex items-center space-x-3">
                @if($icon)
                    <div class="w-12 h-12 {{ $iconColors[$iconColor] ?? $iconColors['blue'] }} rounded-lg flex items-center justify-center">
                        <i data-lucide="{{ $icon }}" class="w-6 h-6"></i>
                    </div>
                @endif
                
                @if($title || $subtitle)
                    <div>
                        @if($title)
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $title }}</h3>
                        @endif
                        @if($subtitle)
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $subtitle }}</p>
                        @endif
                    </div>
                @endif
            </div>
            
            @isset($header)
                {{ $header }}
            @endisset
        </div>
    @endif

    @if(isset($body) || $slot->isNotEmpty())
        <div class="{{ $bodyClass }}">
            @isset($body)
                {{ $body }}
            @else
                {{ $slot }}
            @endisset
        </div>
    @endif

    @isset($footer)
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 {{ $footerClass }}">
            {{ $footer }}
        </div>
    @endisset
</div>
