<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserBalance extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'balance',
        'pending_balance',
        'total_earned',
        'total_deposited',
        'total_withdrawn',
        'total_fees_paid',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'total_deposited' => 'decimal:2',
        'total_withdrawn' => 'decimal:2',
        'total_fees_paid' => 'decimal:2',
    ];

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Balance transactions relationship
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(BalanceTransaction::class, 'user_id', 'user_id');
    }

    /**
     * Get available balance (balance - pending_balance)
     */
    public function getAvailableBalanceAttribute(): float
    {
        return $this->balance - $this->pending_balance;
    }

    /**
     * Check if user can withdraw amount
     */
    public function canWithdraw(float $amount): bool
    {
        return $this->available_balance >= $amount && $amount > 0;
    }

    /**
     * Add balance
     */
    public function addBalance(float $amount, string $type = 'earning', array $metadata = []): BalanceTransaction
    {
        $balanceBefore = $this->balance;
        $this->increment('balance', $amount);
        
        if ($type === 'earning') {
            $this->increment('total_earned', $amount);
        } elseif ($type === 'deposit') {
            $this->increment('total_deposited', $amount);
        }

        return $this->createTransaction([
            'type' => $type,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'status' => 'completed',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Deduct balance
     */
    public function deductBalance(float $amount, string $type = 'fee', array $metadata = []): BalanceTransaction
    {
        if (!$this->canWithdraw($amount)) {
            throw new \Exception('Insufficient balance');
        }

        $balanceBefore = $this->balance;
        $this->decrement('balance', $amount);
        
        if ($type === 'withdrawal') {
            $this->increment('total_withdrawn', $amount);
        } elseif ($type === 'fee') {
            $this->increment('total_fees_paid', $amount);
        }

        return $this->createTransaction([
            'type' => $type,
            'amount' => -$amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'status' => 'completed',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Create pending withdrawal
     */
    public function createPendingWithdrawal(float $amount, array $data = []): BalanceTransaction
    {
        if (!$this->canWithdraw($amount)) {
            throw new \Exception('Insufficient balance');
        }

        // Add to pending balance
        $this->increment('pending_balance', $amount);

        return $this->createTransaction([
            'type' => 'withdrawal',
            'amount' => -$amount,
            'balance_before' => $this->balance,
            'balance_after' => $this->balance, // Balance doesn't change until approved
            'status' => 'pending',
            'payment_method' => $data['payment_method'] ?? null,
            'payment_data' => $data['payment_data'] ?? null,
            'metadata' => $data['metadata'] ?? [],
        ]);
    }

    /**
     * Create transaction record
     */
    private function createTransaction(array $data): BalanceTransaction
    {
        return BalanceTransaction::create(array_merge([
            'transaction_number' => 'TXN-' . strtoupper(uniqid()),
            'user_id' => $this->user_id,
            'description' => $this->getTransactionDescription($data['type'], $data),
        ], $data));
    }

    /**
     * Get transaction description
     */
    private function getTransactionDescription(string $type, array $data): string
    {
        return match($type) {
            'deposit' => 'Deposit saldo',
            'withdrawal' => 'Penarikan saldo',
            'earning' => 'Pendapatan dari penjualan tiket',
            'fee' => 'Biaya platform',
            'refund' => 'Refund pembayaran',
            'adjustment' => 'Penyesuaian saldo',
            default => 'Transaksi saldo'
        };
    }

    /**
     * Get formatted balance
     */
    public function getFormattedBalanceAttribute(): string
    {
        return 'Rp ' . number_format($this->balance, 0, ',', '.');
    }

    /**
     * Get formatted available balance
     */
    public function getFormattedAvailableBalanceAttribute(): string
    {
        return 'Rp ' . number_format($this->available_balance, 0, ',', '.');
    }
}
