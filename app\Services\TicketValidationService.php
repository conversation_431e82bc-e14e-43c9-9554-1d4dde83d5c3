<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\User;
use App\Models\Event;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TicketValidationService
{
    /**
     * Validate ticket by ticket number
     */
    public function validateByTicketNumber(string $ticketNumber, ?User $validator = null): array
    {
        try {
            $ticket = Ticket::where('ticket_number', $ticketNumber)
                ->with(['event', 'buyer', 'order'])
                ->first();

            if (!$ticket) {
                return $this->validationResponse(false, 'Tiket tidak ditemukan', null);
            }

            return $this->performValidation($ticket, $validator);

        } catch (\Exception $e) {
            Log::error('Ticket validation error: ' . $e->getMessage());
            return $this->validationResponse(false, 'Terjadi kesalahan sistem', null);
        }
    }

    /**
     * Validate ticket by QR code
     */
    public function validateByQRCode(string $qrCode, ?User $validator = null): array
    {
        try {
            $ticket = Ticket::where('qr_code', $qrCode)
                ->with(['event', 'buyer', 'order'])
                ->first();

            if (!$ticket) {
                return $this->validationResponse(false, 'QR Code tidak valid', null);
            }

            return $this->performValidation($ticket, $validator);

        } catch (\Exception $e) {
            Log::error('QR Code validation error: ' . $e->getMessage());
            return $this->validationResponse(false, 'Terjadi kesalahan sistem', null);
        }
    }

    /**
     * Perform actual ticket validation
     */
    private function performValidation(Ticket $ticket, ?User $validator = null): array
    {
        // Check ticket status
        if ($ticket->status === 'used') {
            return $this->validationResponse(
                false, 
                'Tiket sudah digunakan pada ' . $ticket->used_at->format('d M Y H:i'),
                $ticket,
                'already_used'
            );
        }

        if ($ticket->status === 'cancelled') {
            return $this->validationResponse(
                false, 
                'Tiket telah dibatalkan',
                $ticket,
                'cancelled'
            );
        }

        if ($ticket->status === 'refunded') {
            return $this->validationResponse(
                false, 
                'Tiket telah di-refund',
                $ticket,
                'refunded'
            );
        }

        // Check event validity
        $eventValidation = $this->validateEvent($ticket->event);
        if (!$eventValidation['valid']) {
            return $this->validationResponse(
                false,
                $eventValidation['message'],
                $ticket,
                $eventValidation['code']
            );
        }

        // All validations passed, mark ticket as used
        $ticket->update([
            'status' => 'used',
            'used_at' => now(),
            'validated_by' => $validator?->id,
            'validation_notes' => 'Validated via ' . ($validator ? 'staff' : 'system'),
        ]);

        // Log validation
        $this->logValidation($ticket, $validator);

        return $this->validationResponse(
            true,
            'Tiket berhasil divalidasi',
            $ticket->fresh(),
            'success'
        );
    }

    /**
     * Validate event conditions
     */
    private function validateEvent(Event $event): array
    {
        $now = now();

        // Check if event is published
        if ($event->status !== 'published') {
            return [
                'valid' => false,
                'message' => 'Event tidak aktif',
                'code' => 'event_inactive'
            ];
        }

        // Check if event has started (allow entry 1 hour before start)
        $allowedEntryTime = $event->start_date->subHour();
        if ($now < $allowedEntryTime) {
            return [
                'valid' => false,
                'message' => 'Event belum dimulai. Pintu masuk dibuka 1 jam sebelum acara.',
                'code' => 'event_not_started'
            ];
        }

        // Check if event has ended (allow entry until 2 hours after start)
        $lateEntryLimit = $event->start_date->addHours(2);
        if ($now > $event->end_date || $now > $lateEntryLimit) {
            return [
                'valid' => false,
                'message' => 'Event sudah berakhir atau waktu masuk sudah habis',
                'code' => 'event_ended'
            ];
        }

        return [
            'valid' => true,
            'message' => 'Event valid',
            'code' => 'valid'
        ];
    }

    /**
     * Create validation response
     */
    private function validationResponse(bool $success, string $message, ?Ticket $ticket = null, string $code = null): array
    {
        $response = [
            'success' => $success,
            'message' => $message,
            'ticket' => $ticket,
            'timestamp' => now()->toISOString(),
        ];

        if ($code) {
            $response['code'] = $code;
        }

        if ($ticket) {
            $response['event'] = $ticket->event;
            $response['buyer'] = $ticket->buyer;
        }

        return $response;
    }

    /**
     * Log validation activity
     */
    private function logValidation(Ticket $ticket, ?User $validator = null): void
    {
        Log::info('Ticket validated', [
            'ticket_id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'event_id' => $ticket->event_id,
            'event_title' => $ticket->event->title,
            'buyer_id' => $ticket->buyer_id,
            'buyer_name' => $ticket->buyer->name,
            'validator_id' => $validator?->id,
            'validator_name' => $validator?->name,
            'validated_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get validation statistics for an event
     */
    public function getEventValidationStats(Event $event): array
    {
        $tickets = $event->tickets();

        return [
            'total_tickets' => $tickets->count(),
            'validated_tickets' => $tickets->where('status', 'used')->count(),
            'pending_tickets' => $tickets->where('status', 'active')->count(),
            'cancelled_tickets' => $tickets->where('status', 'cancelled')->count(),
            'validation_rate' => $tickets->count() > 0 ? 
                ($tickets->where('status', 'used')->count() / $tickets->count()) * 100 : 0,
            'last_validation' => $tickets->where('status', 'used')
                ->orderBy('used_at', 'desc')
                ->first()?->used_at,
        ];
    }

    /**
     * Get validator statistics
     */
    public function getValidatorStats(User $validator, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $query = Ticket::where('validated_by', $validator->id);

        if ($startDate) {
            $query->where('used_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('used_at', '<=', $endDate);
        }

        $tickets = $query->with(['event'])->get();

        return [
            'total_validations' => $tickets->count(),
            'events_validated' => $tickets->pluck('event_id')->unique()->count(),
            'validation_period' => [
                'start' => $startDate?->toDateString(),
                'end' => $endDate?->toDateString(),
            ],
            'validations_by_event' => $tickets->groupBy('event.title')
                ->map(function ($eventTickets) {
                    return $eventTickets->count();
                }),
        ];
    }

    /**
     * Bulk validate tickets (for testing or admin purposes)
     */
    public function bulkValidate(array $ticketNumbers, User $validator): array
    {
        $results = [];

        foreach ($ticketNumbers as $ticketNumber) {
            $results[$ticketNumber] = $this->validateByTicketNumber($ticketNumber, $validator);
        }

        return [
            'total_processed' => count($ticketNumbers),
            'successful_validations' => collect($results)->where('success', true)->count(),
            'failed_validations' => collect($results)->where('success', false)->count(),
            'results' => $results,
        ];
    }
}
