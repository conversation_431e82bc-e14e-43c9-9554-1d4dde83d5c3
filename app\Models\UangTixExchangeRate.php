<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class UangTixExchangeRate extends Model
{
    use HasFactory;

    protected $table = 'uangtix_exchange_rates';

    protected $fillable = [
        'rate_idr_to_uangtix',
        'rate_uangtix_to_idr',
        'min_deposit_idr',
        'max_deposit_idr',
        'min_withdrawal_uangtix',
        'deposit_fee_percentage',
        'withdrawal_fee_percentage',
        'deposits_enabled',
        'withdrawals_enabled',
        'transfers_enabled',
        'terms_and_conditions',
    ];

    protected $casts = [
        'rate_idr_to_uangtix' => 'decimal:4',
        'rate_uangtix_to_idr' => 'decimal:4',
        'min_deposit_idr' => 'decimal:2',
        'max_deposit_idr' => 'decimal:2',
        'min_withdrawal_uangtix' => 'decimal:2',
        'deposit_fee_percentage' => 'decimal:2',
        'withdrawal_fee_percentage' => 'decimal:2',
        'deposits_enabled' => 'boolean',
        'withdrawals_enabled' => 'boolean',
        'transfers_enabled' => 'boolean',
    ];

    /**
     * Get current exchange rate (singleton pattern)
     */
    public static function current(): self
    {
        return Cache::remember('uangtix_exchange_rate', 3600, function () {
            return self::first() ?? self::create([
                'rate_idr_to_uangtix' => 1.0000,
                'rate_uangtix_to_idr' => 1.0000,
                'min_deposit_idr' => 10000,
                'max_deposit_idr' => 10000000,
                'min_withdrawal_uangtix' => 10,
                'deposit_fee_percentage' => 0,
                'withdrawal_fee_percentage' => 2.5,
                'deposits_enabled' => true,
                'withdrawals_enabled' => true,
                'transfers_enabled' => true,
            ]);
        });
    }

    /**
     * Convert IDR to UangTix
     */
    public function convertIdrToUangTix(float $idrAmount): float
    {
        return round($idrAmount * $this->rate_idr_to_uangtix, 2);
    }

    /**
     * Convert UangTix to IDR
     */
    public function convertUangTixToIdr(float $uangTixAmount): float
    {
        return round($uangTixAmount * $this->rate_uangtix_to_idr, 2);
    }

    /**
     * Calculate deposit fee
     */
    public function calculateDepositFee(float $idrAmount): float
    {
        return round(($idrAmount * $this->deposit_fee_percentage) / 100, 2);
    }

    /**
     * Calculate withdrawal fee
     */
    public function calculateWithdrawalFee(float $uangTixAmount): float
    {
        return round(($uangTixAmount * $this->withdrawal_fee_percentage) / 100, 2);
    }

    /**
     * Get net UangTix amount after deposit fee
     */
    public function getNetDepositAmount(float $idrAmount): array
    {
        $fee = $this->calculateDepositFee($idrAmount);
        $netIdr = $idrAmount - $fee;
        $uangTixAmount = $this->convertIdrToUangTix($netIdr);

        return [
            'gross_idr' => $idrAmount,
            'fee_idr' => $fee,
            'net_idr' => $netIdr,
            'uangtix_amount' => $uangTixAmount,
        ];
    }

    /**
     * Get net IDR amount after withdrawal fee
     */
    public function getNetWithdrawalAmount(float $uangTixAmount): array
    {
        $fee = $this->calculateWithdrawalFee($uangTixAmount);
        $netUangTix = $uangTixAmount - $fee;
        $idrAmount = $this->convertUangTixToIdr($netUangTix);

        return [
            'gross_uangtix' => $uangTixAmount,
            'fee_uangtix' => $fee,
            'net_uangtix' => $netUangTix,
            'idr_amount' => $idrAmount,
        ];
    }

    /**
     * Validate deposit amount
     */
    public function validateDepositAmount(float $idrAmount): array
    {
        $errors = [];

        if (!$this->deposits_enabled) {
            $errors[] = 'Deposit UangTix sedang tidak tersedia';
        }

        if ($idrAmount < $this->min_deposit_idr) {
            $errors[] = "Minimum deposit Rp " . number_format($this->min_deposit_idr, 0, ',', '.');
        }

        if ($idrAmount > $this->max_deposit_idr) {
            $errors[] = "Maksimum deposit Rp " . number_format($this->max_deposit_idr, 0, ',', '.');
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Validate withdrawal amount
     */
    public function validateWithdrawalAmount(float $uangTixAmount): array
    {
        $errors = [];

        if (!$this->withdrawals_enabled) {
            $errors[] = 'Penarikan UangTix sedang tidak tersedia';
        }

        if ($uangTixAmount < $this->min_withdrawal_uangtix) {
            $errors[] = "Minimum penarikan " . number_format($this->min_withdrawal_uangtix, 0, ',', '.') . " UTX";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Get formatted rates
     */
    public function getFormattedRatesAttribute(): array
    {
        return [
            'idr_to_uangtix' => "1 IDR = " . number_format($this->rate_idr_to_uangtix, 4) . " UTX",
            'uangtix_to_idr' => "1 UTX = Rp " . number_format($this->rate_uangtix_to_idr, 0, ',', '.'),
        ];
    }

    /**
     * Get formatted limits
     */
    public function getFormattedLimitsAttribute(): array
    {
        return [
            'min_deposit' => "Rp " . number_format($this->min_deposit_idr, 0, ',', '.'),
            'max_deposit' => "Rp " . number_format($this->max_deposit_idr, 0, ',', '.'),
            'min_withdrawal' => "UTX " . number_format($this->min_withdrawal_uangtix, 0, ',', '.'),
        ];
    }

    /**
     * Get formatted fees
     */
    public function getFormattedFeesAttribute(): array
    {
        return [
            'deposit_fee' => $this->deposit_fee_percentage . "%",
            'withdrawal_fee' => $this->withdrawal_fee_percentage . "%",
        ];
    }

    /**
     * Update exchange rate
     */
    public static function updateRate(array $data): self
    {
        $rate = self::first();

        if (!$rate) {
            $rate = self::create($data);
        } else {
            $rate->update($data);
        }

        // Clear cache
        Cache::forget('uangtix_exchange_rate');

        return $rate;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            Cache::forget('uangtix_exchange_rate');
        });

        static::deleted(function () {
            Cache::forget('uangtix_exchange_rate');
        });
    }
}
