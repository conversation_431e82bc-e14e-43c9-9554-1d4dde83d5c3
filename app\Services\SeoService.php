<?php

namespace App\Services;

use App\Models\PlatformSetting;
use Illuminate\Support\Facades\Cache;

class SeoService
{
    /**
     * Get SEO meta tags for a page
     */
    public static function getMetaTags(array $options = []): array
    {
        $seoSettings = PlatformSetting::getGroup('seo');
        
        $title = $options['title'] ?? $seoSettings['seo_site_title'] ?? 'TiXara';
        $description = $options['description'] ?? $seoSettings['seo_site_description'] ?? '';
        $keywords = $options['keywords'] ?? $seoSettings['seo_site_keywords'] ?? '';
        $image = $options['image'] ?? $seoSettings['seo_og_image'] ?? '/images/og-image.jpg';
        $url = $options['url'] ?? url()->current();
        $type = $options['type'] ?? 'website';
        
        return [
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'image' => $image,
            'url' => $url,
            'type' => $type,
            'twitter_handle' => $seoSettings['seo_twitter_handle'] ?? '@tixara_id',
        ];
    }

    /**
     * Generate structured data for events
     */
    public static function generateEventStructuredData($event): array
    {
        $seoSettings = PlatformSetting::getGroup('seo');
        
        if (!($seoSettings['seo_enable_structured_data'] ?? true)) {
            return [];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Event',
            'name' => $event->title,
            'description' => strip_tags($event->description),
            'startDate' => $event->start_date->toISOString(),
            'endDate' => $event->end_date->toISOString(),
            'eventStatus' => 'https://schema.org/EventScheduled',
            'eventAttendanceMode' => 'https://schema.org/OfflineEventAttendanceMode',
            'location' => [
                '@type' => 'Place',
                'name' => $event->venue,
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => $event->city,
                    'addressCountry' => 'ID'
                ]
            ],
            'image' => $event->image ? asset('storage/' . $event->image) : asset('images/default-event.jpg'),
            'organizer' => [
                '@type' => 'Organization',
                'name' => $event->organizer->name ?? 'TiXara',
                'url' => url('/')
            ],
            'offers' => [
                '@type' => 'Offer',
                'price' => $event->price,
                'priceCurrency' => 'IDR',
                'availability' => $event->available_capacity > 0 ? 'https://schema.org/InStock' : 'https://schema.org/SoldOut',
                'url' => route('events.show', $event->slug)
            ]
        ];
    }

    /**
     * Generate structured data for organization
     */
    public static function generateOrganizationStructuredData(): array
    {
        $seoSettings = PlatformSetting::getGroup('seo');
        
        if (!($seoSettings['seo_enable_structured_data'] ?? true)) {
            return [];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'TiXara',
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'description' => $seoSettings['seo_site_description'] ?? '',
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '+62-21-1234567',
                'contactType' => 'customer service',
                'email' => $seoSettings['support_email'] ?? '<EMAIL>'
            ],
            'sameAs' => [
                'https://www.facebook.com/tixara',
                'https://www.instagram.com/tixara_id',
                'https://twitter.com/tixara_id'
            ]
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public static function generateBreadcrumbStructuredData(array $breadcrumbs): array
    {
        $seoSettings = PlatformSetting::getGroup('seo');
        
        if (!($seoSettings['seo_enable_structured_data'] ?? true)) {
            return [];
        }

        $itemList = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $itemList[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $itemList
        ];
    }

    /**
     * Get tracking codes
     */
    public static function getTrackingCodes(): array
    {
        $seoSettings = PlatformSetting::getGroup('seo');
        
        return [
            'google_analytics' => $seoSettings['seo_google_analytics_id'] ?? '',
            'google_tag_manager' => $seoSettings['seo_google_tag_manager_id'] ?? '',
            'facebook_pixel' => $seoSettings['seo_facebook_pixel_id'] ?? '',
        ];
    }

    /**
     * Get site verification codes
     */
    public static function getVerificationCodes(): array
    {
        $seoSettings = PlatformSetting::getGroup('seo');
        
        return [
            'google' => $seoSettings['seo_google_site_verification'] ?? '',
            'bing' => $seoSettings['seo_bing_site_verification'] ?? '',
        ];
    }

    /**
     * Generate robots.txt content
     */
    public static function getRobotsTxt(): string
    {
        $seoSettings = PlatformSetting::getGroup('seo');
        
        return $seoSettings['seo_robots_txt'] ?? "User-agent: *\nDisallow: /admin/\nDisallow: /api/\nSitemap: " . url('/sitemap.xml');
    }

    /**
     * Generate sitemap URLs
     */
    public static function generateSitemapUrls(): array
    {
        return Cache::remember('sitemap_urls', 3600, function () {
            $urls = [];
            
            // Add static pages
            $urls[] = [
                'url' => url('/'),
                'lastmod' => now()->toISOString(),
                'changefreq' => 'daily',
                'priority' => '1.0'
            ];
            
            $urls[] = [
                'url' => route('events.index'),
                'lastmod' => now()->toISOString(),
                'changefreq' => 'daily',
                'priority' => '0.9'
            ];
            
            // Add events
            $events = \App\Models\Event::where('status', 'published')
                ->where('start_date', '>', now())
                ->get();
                
            foreach ($events as $event) {
                $urls[] = [
                    'url' => route('events.show', $event->slug),
                    'lastmod' => $event->updated_at->toISOString(),
                    'changefreq' => 'weekly',
                    'priority' => '0.8'
                ];
            }
            
            return $urls;
        });
    }

    /**
     * Clear SEO cache
     */
    public static function clearCache(): void
    {
        Cache::forget('sitemap_urls');
        Cache::forget('platform_settings_seo');
    }
}
