<!-- Logo -->
<div class="flex items-center flex-shrink-0 px-4 py-6 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center w-full">
        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <span class="text-white font-bold text-lg">T</span>
        </div>
        <div class="ml-3">
            <h1 class="text-xl font-bold text-gray-900 dark:text-white">TiXara</h1>
            <p class="text-sm text-gray-500 dark:text-gray-400">Admin Panel</p>
        </div>
        <button @click="showMobileMenu = false" class="ml-auto p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 lg:hidden">
            <i data-lucide="x" class="w-4 h-4 text-gray-500 dark:text-gray-400"></i>
        </button>
    </div>
</div>

<!-- Navigation -->
<nav class="flex-1 px-3 py-4 space-y-2 overflow-y-auto scrollbar-thin">
    <!-- Dashboard -->
    <a href="<?php echo e(route('admin.dashboard')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.dashboard') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Dashboard</span>
        <?php if(request()->routeIs('admin.dashboard')): ?>
            <div class="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Events Management -->
    <a href="<?php echo e(route('admin.tickets.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.tickets.*') ? 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.tickets.*') ? 'text-purple-600 dark:text-purple-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="calendar" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Events</span>
        <?php if(request()->routeIs('admin.tickets.*')): ?>
            <div class="ml-auto w-2 h-2 bg-purple-600 dark:bg-purple-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Users Management -->
    <a href="<?php echo e(route('admin.users.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.users.*') ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.users.*') ? 'text-green-600 dark:text-green-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="users" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Users</span>
        <?php if(request()->routeIs('admin.users.*')): ?>
            <div class="ml-auto w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Orders Management -->
    <a href="<?php echo e(route('admin.orders.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.orders.*') ? 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.orders.*') ? 'text-orange-600 dark:text-orange-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="shopping-cart" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Orders</span>
        <?php if(request()->routeIs('admin.orders.*')): ?>
            <div class="ml-auto w-2 h-2 bg-orange-600 dark:bg-orange-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- UangTix Management -->
    <a href="<?php echo e(route('admin.uangtix.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.uangtix.*') ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.uangtix.*') ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="coins" class="w-5 h-5"></i>
        </div>
        <span class="truncate">UangTix</span>
        <?php if(request()->routeIs('admin.uangtix.*')): ?>
            <div class="ml-auto w-2 h-2 bg-yellow-600 dark:bg-yellow-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Payments -->
    <a href="<?php echo e(route('admin.payments.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.payments.*') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.payments.*') ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="credit-card" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Payments</span>
        <?php if(request()->routeIs('admin.payments.*')): ?>
            <div class="ml-auto w-2 h-2 bg-indigo-600 dark:bg-indigo-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Vouchers -->
    <a href="<?php echo e(route('admin.vouchers.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.vouchers.*') ? 'bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 border border-emerald-200 dark:border-emerald-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.vouchers.*') ? 'text-emerald-600 dark:text-emerald-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="tag" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Vouchers</span>
        <?php if(request()->routeIs('admin.vouchers.*')): ?>
            <div class="ml-auto w-2 h-2 bg-emerald-600 dark:bg-emerald-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Categories -->
    <a href="<?php echo e(route('admin.categories.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.categories.*') ? 'bg-cyan-50 dark:bg-cyan-900/20 text-cyan-700 dark:text-cyan-300 border border-cyan-200 dark:border-cyan-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.categories.*') ? 'text-cyan-600 dark:text-cyan-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="folder" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Categories</span>
        <?php if(request()->routeIs('admin.categories.*')): ?>
            <div class="ml-auto w-2 h-2 bg-cyan-600 dark:bg-cyan-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Notifications -->
    <a href="<?php echo e(route('admin.notifications.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.notifications.*') ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.notifications.*') ? 'text-red-600 dark:text-red-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="bell" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Notifications</span>
        <?php if(request()->routeIs('admin.notifications.*')): ?>
            <div class="ml-auto w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Organizers -->
    <a href="<?php echo e(route('admin.organizers.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.organizers.*') ? 'bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300 border border-teal-200 dark:border-teal-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.organizers.*') ? 'text-teal-600 dark:text-teal-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="building" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Organizers</span>
        <?php if(request()->routeIs('admin.organizers.*')): ?>
            <div class="ml-auto w-2 h-2 bg-teal-600 dark:bg-teal-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Divider -->
    <div class="my-4 border-t border-gray-200 dark:border-gray-700"></div>

    <!-- Analytics -->
    <a href="<?php echo e(route('admin.analytics.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.analytics.*') ? 'bg-pink-50 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300 border border-pink-200 dark:border-pink-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.analytics.*') ? 'text-pink-600 dark:text-pink-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Analytics</span>
        <?php if(request()->routeIs('admin.analytics.*')): ?>
            <div class="ml-auto w-2 h-2 bg-pink-600 dark:bg-pink-400 rounded-full"></div>
        <?php endif; ?>
    </a>

    <!-- Settings -->
    <a href="<?php echo e(route('admin.settings.index')); ?>"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?php echo e(request()->routeIs('admin.settings.*') ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white'); ?>">
        <div class="flex items-center justify-center w-6 h-6 mr-3 <?php echo e(request()->routeIs('admin.settings.*') ? 'text-gray-700 dark:text-gray-300' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300'); ?>">
            <i data-lucide="settings" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Settings</span>
        <?php if(request()->routeIs('admin.settings.*')): ?>
            <div class="ml-auto w-2 h-2 bg-gray-600 dark:bg-gray-400 rounded-full"></div>
        <?php endif; ?>
    </a>
</nav>

<!-- User Info -->
<div class="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4">
    <div class="flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0">
            <span class="text-white text-sm font-bold">
                <?php echo e(substr(auth()->user()->name ?? 'A', 0, 1)); ?>

            </span>
        </div>
        <div class="flex-1 min-w-0">
            <p class="text-sm font-semibold text-gray-900 dark:text-white truncate">
                <?php echo e(auth()->user()->name ?? 'Admin'); ?>

            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                <?php echo e(ucfirst(auth()->user()->role ?? 'admin')); ?>

            </p>
        </div>
        <div class="flex items-center space-x-2">
            <button class="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                    onclick="document.getElementById('logout-form-mobile').submit()"
                    title="Logout">
                <i data-lucide="log-out" class="w-4 h-4 text-gray-500 dark:text-gray-400"></i>
            </button>
            <form id="logout-form-mobile" action="<?php echo e(route('logout')); ?>" method="POST" class="hidden">
                <?php echo csrf_field(); ?>
            </form>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/layouts/partials/admin-sidebar.blade.php ENDPATH**/ ?>