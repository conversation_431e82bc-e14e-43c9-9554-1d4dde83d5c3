<?php $__env->startSection('title', $title); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?php echo e($title); ?></h1>
            <p class="text-muted">Kelola voucher diskon untuk meningkatkan penjualan tiket</p>
        </div>
        <a href="<?php echo e(route('admin.vouchers.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Tambah Voucher
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Voucher</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['total'])); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Voucher Aktif</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['active'])); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Penggunaan</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['total_usage'])); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Penghematan</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">Rp <?php echo e(number_format($stats['total_savings'], 0, ',', '.')); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter & Pencarian</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.vouchers.index')); ?>">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Pencarian</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e($filters['search'] ?? ''); ?>" placeholder="Kode atau nama voucher">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">Semua Status</option>
                                <option value="active" <?php echo e(($filters['status'] ?? '') === 'active' ? 'selected' : ''); ?>>Aktif</option>
                                <option value="inactive" <?php echo e(($filters['status'] ?? '') === 'inactive' ? 'selected' : ''); ?>>Tidak Aktif</option>
                                <option value="expired" <?php echo e(($filters['status'] ?? '') === 'expired' ? 'selected' : ''); ?>>Kedaluwarsa</option>
                                <option value="scheduled" <?php echo e(($filters['status'] ?? '') === 'scheduled' ? 'selected' : ''); ?>>Terjadwal</option>
                                <option value="exhausted" <?php echo e(($filters['status'] ?? '') === 'exhausted' ? 'selected' : ''); ?>>Habis</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">Tipe</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">Semua Tipe</option>
                                <option value="percentage" <?php echo e(($filters['type'] ?? '') === 'percentage' ? 'selected' : ''); ?>>Persentase</option>
                                <option value="fixed" <?php echo e(($filters['type'] ?? '') === 'fixed' ? 'selected' : ''); ?>>Nominal Tetap</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="sort">Urutkan</label>
                            <select class="form-control" id="sort" name="sort">
                                <option value="created_at" <?php echo e(($filters['sort'] ?? '') === 'created_at' ? 'selected' : ''); ?>>Tanggal Dibuat</option>
                                <option value="code" <?php echo e(($filters['sort'] ?? '') === 'code' ? 'selected' : ''); ?>>Kode</option>
                                <option value="name" <?php echo e(($filters['sort'] ?? '') === 'name' ? 'selected' : ''); ?>>Nama</option>
                                <option value="used_count" <?php echo e(($filters['sort'] ?? '') === 'used_count' ? 'selected' : ''); ?>>Penggunaan</option>
                                <option value="expires_at" <?php echo e(($filters['sort'] ?? '') === 'expires_at' ? 'selected' : ''); ?>>Kedaluwarsa</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="direction">Arah</label>
                            <select class="form-control" id="direction" name="direction">
                                <option value="desc" <?php echo e(($filters['direction'] ?? '') === 'desc' ? 'selected' : ''); ?>>Menurun</option>
                                <option value="asc" <?php echo e(($filters['direction'] ?? '') === 'asc' ? 'selected' : ''); ?>>Menaik</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">Filter</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Vouchers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Voucher</h6>
        </div>
        <div class="card-body">
            <?php if($vouchers->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Kode</th>
                                <th>Nama</th>
                                <th>Tipe</th>
                                <th>Nilai</th>
                                <th>Penggunaan</th>
                                <th>Status</th>
                                <th>Kedaluwarsa</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $vouchers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $voucher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-secondary"><?php echo e($voucher->code); ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo e($voucher->name); ?></strong>
                                        <?php if($voucher->description): ?>
                                            <br><small class="text-muted"><?php echo e(Str::limit($voucher->description, 50)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($voucher->type === 'percentage'): ?>
                                            <span class="badge badge-info">Persentase</span>
                                        <?php else: ?>
                                            <span class="badge badge-success">Nominal</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e($voucher->formatted_value); ?></strong>
                                        <?php if($voucher->min_order_amount > 0): ?>
                                            <br><small class="text-muted">Min: Rp <?php echo e(number_format($voucher->min_order_amount, 0, ',', '.')); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="mr-2">
                                                <small class="text-muted"><?php echo e($voucher->used_count); ?></small>
                                                <?php if($voucher->usage_limit): ?>
                                                    <small class="text-muted">/ <?php echo e($voucher->usage_limit); ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <?php if($voucher->usage_limit): ?>
                                                <div class="progress flex-grow-1" style="height: 6px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: <?php echo e($voucher->usage_percentage); ?>%"
                                                         aria-valuenow="<?php echo e($voucher->usage_percentage); ?>" 
                                                         aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                            $status = $voucher->status;
                                            $statusClass = match($status) {
                                                'active' => 'success',
                                                'inactive' => 'secondary',
                                                'expired' => 'danger',
                                                'scheduled' => 'warning',
                                                'exhausted' => 'dark',
                                                default => 'secondary'
                                            };
                                            $statusText = match($status) {
                                                'active' => 'Aktif',
                                                'inactive' => 'Tidak Aktif',
                                                'expired' => 'Kedaluwarsa',
                                                'scheduled' => 'Terjadwal',
                                                'exhausted' => 'Habis',
                                                default => 'Unknown'
                                            };
                                        ?>
                                        <span class="badge badge-<?php echo e($statusClass); ?>"><?php echo e($statusText); ?></span>
                                    </td>
                                    <td>
                                        <small><?php echo e($voucher->expires_at->format('d/m/Y H:i')); ?></small>
                                        <?php if($voucher->expires_at->isFuture()): ?>
                                            <br><small class="text-muted"><?php echo e($voucher->expires_at->diffForHumans()); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.vouchers.show', $voucher)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.vouchers.edit', $voucher)); ?>" 
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.vouchers.toggle-status', $voucher)); ?>" 
                                                  method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" 
                                                        class="btn btn-sm btn-outline-<?php echo e($voucher->is_active ? 'danger' : 'success'); ?>" 
                                                        title="<?php echo e($voucher->is_active ? 'Nonaktifkan' : 'Aktifkan'); ?>">
                                                    <i class="fas fa-<?php echo e($voucher->is_active ? 'times' : 'check'); ?>"></i>
                                                </button>
                                            </form>
                                            <?php if($voucher->usages()->count() === 0): ?>
                                                <form action="<?php echo e(route('admin.vouchers.destroy', $voucher)); ?>" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Yakin ingin menghapus voucher ini?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Hapus">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <small class="text-muted">
                            Menampilkan <?php echo e($vouchers->firstItem()); ?> - <?php echo e($vouchers->lastItem()); ?> 
                            dari <?php echo e($vouchers->total()); ?> voucher
                        </small>
                    </div>
                    <div>
                        <?php echo e($vouchers->links()); ?>

                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">Belum ada voucher</h5>
                    <p class="text-muted">Mulai buat voucher pertama untuk meningkatkan penjualan tiket.</p>
                    <a href="<?php echo e(route('admin.vouchers.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tambah Voucher
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.progress {
    background-color: #e9ecef;
}
.badge {
    font-size: 0.75em;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/vouchers/index.blade.php ENDPATH**/ ?>