<?php $__env->startSection('title', 'Kelola Voucher'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Custom styles for Voucher Management */
.voucher-card {
    transition: all 0.3s ease;
}

.voucher-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.voucher-type-badge {
    transition: all 0.2s ease;
}

.voucher-discount {
    position: relative;
}

.voucher-discount::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    border-radius: 2px;
}

.discount-percentage::before {
    background: #10b981;
}

.discount-fixed::before {
    background: #3b82f6;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .filter-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .voucher-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="tag" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Kelola Voucher</h1>
                            <p class="text-gray-600 dark:text-gray-400">Kelola voucher dan kupon diskon</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="<?php echo e(route('admin.vouchers.create')); ?>"
                       class="inline-flex items-center px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Buat Voucher
                    </a>

                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-sm">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Export
                            <i data-lucide="chevron-down" class="w-4 h-4 ml-2"></i>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                            <div class="py-1">
                                <a href="#" onclick="exportVouchers('csv')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Export CSV
                                </a>
                                <a href="#" onclick="exportVouchers('excel')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-spreadsheet" class="w-4 h-4 mr-3"></i>
                                    Export Excel
                                </a>
                            </div>
                        </div>
                    </div>

                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                       class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Voucher Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stats-grid">
            <!-- Total Vouchers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Voucher</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            <?php echo e(number_format($stats['total'] ?? 0, 0, ',', '.')); ?>

                        </p>
                        <p class="text-xs text-emerald-600 dark:text-emerald-400 mt-1">
                            <i data-lucide="tag" class="w-3 h-3 inline mr-1"></i>
                            Semua voucher
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="tag" class="w-6 h-6 text-emerald-600 dark:text-emerald-400"></i>
                    </div>
                </div>
            </div>

            <!-- Active Vouchers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Voucher Aktif</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            <?php echo e(number_format($stats['active'] ?? 0, 0, ',', '.')); ?>

                        </p>
                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                            <i data-lucide="check-circle" class="w-3 h-3 inline mr-1"></i>
                            Dapat digunakan
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="check-circle" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Used Vouchers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Penggunaan</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            <?php echo e(number_format($stats['total_usage'] ?? 0, 0, ',', '.')); ?>

                        </p>
                        <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            <i data-lucide="shopping-cart" class="w-3 h-3 inline mr-1"></i>
                            Voucher terpakai
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="shopping-cart" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>

            <!-- Total Discount -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Penghematan</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            Rp <?php echo e(number_format($stats['total_savings'] ?? 0, 0, ',', '.')); ?>

                        </p>
                        <p class="text-xs text-purple-600 dark:text-purple-400 mt-1">
                            <i data-lucide="percent" class="w-3 h-3 inline mr-1"></i>
                            Diskon diberikan
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="percent" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters & Search -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4 flex-1">
                    <!-- Search -->
                    <div class="relative flex-1 max-w-md">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-5 h-5 text-gray-400"></i>
                        </div>
                        <input type="text"
                               id="searchInput"
                               placeholder="Cari voucher..."
                               value="<?php echo e($filters['search'] ?? ''); ?>"
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200">
                    </div>

                    <!-- Type Filter -->
                    <select id="typeFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200">
                        <option value="">Semua Tipe</option>
                        <option value="percentage" <?php echo e(($filters['type'] ?? '') === 'percentage' ? 'selected' : ''); ?>>Persentase</option>
                        <option value="fixed" <?php echo e(($filters['type'] ?? '') === 'fixed' ? 'selected' : ''); ?>>Nominal Tetap</option>
                    </select>

                    <!-- Status Filter -->
                    <select id="statusFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200">
                        <option value="">Semua Status</option>
                        <option value="active" <?php echo e(($filters['status'] ?? '') === 'active' ? 'selected' : ''); ?>>Aktif</option>
                        <option value="inactive" <?php echo e(($filters['status'] ?? '') === 'inactive' ? 'selected' : ''); ?>>Tidak Aktif</option>
                        <option value="expired" <?php echo e(($filters['status'] ?? '') === 'expired' ? 'selected' : ''); ?>>Kadaluarsa</option>
                        <option value="scheduled" <?php echo e(($filters['status'] ?? '') === 'scheduled' ? 'selected' : ''); ?>>Terjadwal</option>
                        <option value="exhausted" <?php echo e(($filters['status'] ?? '') === 'exhausted' ? 'selected' : ''); ?>>Habis</option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                    <button onclick="applyFilters()"
                            class="inline-flex items-center px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors duration-200">
                        <i data-lucide="filter" class="w-4 h-4 mr-2"></i>
                        Filter
                    </button>
                    <button onclick="refreshData()"
                            class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

    <!-- Vouchers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Voucher</h6>
        </div>
        <div class="card-body">
            <?php if($vouchers->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Kode</th>
                                <th>Nama</th>
                                <th>Tipe</th>
                                <th>Nilai</th>
                                <th>Penggunaan</th>
                                <th>Status</th>
                                <th>Kedaluwarsa</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $vouchers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $voucher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-secondary"><?php echo e($voucher->code); ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo e($voucher->name); ?></strong>
                                        <?php if($voucher->description): ?>
                                            <br><small class="text-muted"><?php echo e(Str::limit($voucher->description, 50)); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($voucher->type === 'percentage'): ?>
                                            <span class="badge badge-info">Persentase</span>
                                        <?php else: ?>
                                            <span class="badge badge-success">Nominal</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e($voucher->formatted_value); ?></strong>
                                        <?php if($voucher->min_order_amount > 0): ?>
                                            <br><small class="text-muted">Min: Rp <?php echo e(number_format($voucher->min_order_amount, 0, ',', '.')); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="mr-2">
                                                <small class="text-muted"><?php echo e($voucher->used_count); ?></small>
                                                <?php if($voucher->usage_limit): ?>
                                                    <small class="text-muted">/ <?php echo e($voucher->usage_limit); ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <?php if($voucher->usage_limit): ?>
                                                <div class="progress flex-grow-1" style="height: 6px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: <?php echo e($voucher->usage_percentage); ?>%"
                                                         aria-valuenow="<?php echo e($voucher->usage_percentage); ?>" 
                                                         aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                            $status = $voucher->status;
                                            $statusClass = match($status) {
                                                'active' => 'success',
                                                'inactive' => 'secondary',
                                                'expired' => 'danger',
                                                'scheduled' => 'warning',
                                                'exhausted' => 'dark',
                                                default => 'secondary'
                                            };
                                            $statusText = match($status) {
                                                'active' => 'Aktif',
                                                'inactive' => 'Tidak Aktif',
                                                'expired' => 'Kedaluwarsa',
                                                'scheduled' => 'Terjadwal',
                                                'exhausted' => 'Habis',
                                                default => 'Unknown'
                                            };
                                        ?>
                                        <span class="badge badge-<?php echo e($statusClass); ?>"><?php echo e($statusText); ?></span>
                                    </td>
                                    <td>
                                        <small><?php echo e($voucher->expires_at->format('d/m/Y H:i')); ?></small>
                                        <?php if($voucher->expires_at->isFuture()): ?>
                                            <br><small class="text-muted"><?php echo e($voucher->expires_at->diffForHumans()); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.vouchers.show', $voucher)); ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.vouchers.edit', $voucher)); ?>" 
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.vouchers.toggle-status', $voucher)); ?>" 
                                                  method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" 
                                                        class="btn btn-sm btn-outline-<?php echo e($voucher->is_active ? 'danger' : 'success'); ?>" 
                                                        title="<?php echo e($voucher->is_active ? 'Nonaktifkan' : 'Aktifkan'); ?>">
                                                    <i class="fas fa-<?php echo e($voucher->is_active ? 'times' : 'check'); ?>"></i>
                                                </button>
                                            </form>
                                            <?php if($voucher->usages()->count() === 0): ?>
                                                <form action="<?php echo e(route('admin.vouchers.destroy', $voucher)); ?>" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Yakin ingin menghapus voucher ini?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Hapus">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <small class="text-muted">
                            Menampilkan <?php echo e($vouchers->firstItem()); ?> - <?php echo e($vouchers->lastItem()); ?> 
                            dari <?php echo e($vouchers->total()); ?> voucher
                        </small>
                    </div>
                    <div>
                        <?php echo e($vouchers->links()); ?>

                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">Belum ada voucher</h5>
                    <p class="text-muted">Mulai buat voucher pertama untuk meningkatkan penjualan tiket.</p>
                    <a href="<?php echo e(route('admin.vouchers.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tambah Voucher
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Voucher Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                applyFilters();
            }, 300);
        });
    }

    // Filter change handlers
    const typeFilter = document.getElementById('typeFilter');
    const statusFilter = document.getElementById('statusFilter');

    if (typeFilter) {
        typeFilter.addEventListener('change', applyFilters);
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
});

// Apply filters
function applyFilters() {
    const search = document.getElementById('searchInput').value;
    const type = document.getElementById('typeFilter').value;
    const status = document.getElementById('statusFilter').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (type) params.append('type', type);
    if (status) params.append('status', status);

    const url = new URL(window.location);
    url.search = params.toString();

    showNotification('Filtering', 'Menerapkan filter...', 'info');
    window.location.href = url.toString();
}

// Export functions
function exportVouchers(format = 'csv') {
    showNotification('Export Started', `Preparing ${format.toUpperCase()} export...`, 'info');

    // Create download link
    const link = document.createElement('a');
    link.href = `<?php echo e(route('admin.vouchers.index')); ?>?export=${format}`;
    link.download = `vouchers_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Toggle voucher status
function toggleVoucherStatus(id, currentStatus) {
    const action = currentStatus === 'active' ? 'nonaktifkan' : 'aktifkan';

    if (confirm(`Yakin ingin ${action} voucher ini?`)) {
        showNotification('Processing', `${action.charAt(0).toUpperCase() + action.slice(1)} voucher...`, 'info');

        // Submit form
        const form = document.querySelector(`form[action*="vouchers/${id}/toggle-status"]`);
        if (form) {
            form.submit();
        }
    }
}

// Delete voucher
function deleteVoucher(id) {
    if (confirm('Yakin ingin menghapus voucher ini? Tindakan ini tidak dapat dibatalkan.')) {
        showNotification('Deleting', 'Menghapus voucher...', 'info');

        // Submit delete form
        const form = document.querySelector(`form[action*="vouchers/${id}"][method="POST"]`);
        if (form) {
            form.submit();
        }
    }
}

// Refresh data
function refreshData() {
    showNotification('Refreshing', 'Memperbarui data voucher...', 'info');
    setTimeout(() => window.location.reload(), 500);
}

// Notification system
function showNotification(title, message, type = 'info') {
    // Use the global notification system from admin layout
    if (typeof showToast !== 'undefined') {
        showToast(type, title, message);
    } else {
        alert(`${title}: ${message}`);
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/vouchers/index.blade.php ENDPATH**/ ?>