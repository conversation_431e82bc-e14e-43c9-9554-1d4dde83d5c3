<?php

/**
 * Clear Logs Command
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: <PERSON><PERSON><PERSON> Nazula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ClearLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:clear {--keep-backup : Keep a backup of the current log file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all log files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $logPath = storage_path('logs');
        
        if (!File::exists($logPath)) {
            $this->error('Log directory does not exist.');
            return 1;
        }

        $logFiles = File::glob($logPath . '/*.log');
        
        if (empty($logFiles)) {
            $this->info('No log files found.');
            return 0;
        }

        $keepBackup = $this->option('keep-backup');
        
        foreach ($logFiles as $logFile) {
            $fileName = basename($logFile);
            
            try {
                if ($keepBackup && File::size($logFile) > 0) {
                    $backupName = pathinfo($fileName, PATHINFO_FILENAME) . '_backup_' . date('Y_m_d_H_i_s') . '.log';
                    $backupPath = $logPath . '/' . $backupName;
                    
                    if (File::copy($logFile, $backupPath)) {
                        $this->info("Backup created: {$backupName}");
                    }
                }
                
                // Clear the log file
                File::put($logFile, '');
                $this->info("Cleared: {$fileName}");
                
            } catch (\Exception $e) {
                $this->error("Failed to clear {$fileName}: " . $e->getMessage());
            }
        }

        $this->info('Log clearing completed.');
        return 0;
    }
}
