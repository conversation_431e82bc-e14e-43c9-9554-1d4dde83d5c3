@props([
    'type' => 'info', // success, error, warning, info
    'title' => null,
    'message',
    'dismissible' => true,
    'duration' => 5000,
    'position' => 'top-right', // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center
    'id' => null
])

@php
    $toastId = $id ?? 'toast-' . uniqid();
    
    $types = [
        'success' => [
            'bg' => 'bg-green-500',
            'icon' => 'check-circle',
            'border' => 'border-green-200 dark:border-green-800'
        ],
        'error' => [
            'bg' => 'bg-red-500',
            'icon' => 'x-circle',
            'border' => 'border-red-200 dark:border-red-800'
        ],
        'warning' => [
            'bg' => 'bg-yellow-500',
            'icon' => 'alert-triangle',
            'border' => 'border-yellow-200 dark:border-yellow-800'
        ],
        'info' => [
            'bg' => 'bg-blue-500',
            'icon' => 'info',
            'border' => 'border-blue-200 dark:border-blue-800'
        ]
    ];
    
    $positions = [
        'top-right' => 'top-4 right-4',
        'top-left' => 'top-4 left-4',
        'bottom-right' => 'bottom-4 right-4',
        'bottom-left' => 'bottom-4 left-4',
        'top-center' => 'top-4 left-1/2 transform -translate-x-1/2',
        'bottom-center' => 'bottom-4 left-1/2 transform -translate-x-1/2'
    ];
    
    $typeConfig = $types[$type] ?? $types['info'];
@endphp

<div id="{{ $toastId }}" 
     class="fixed {{ $positions[$position] ?? $positions['top-right'] }} z-50 max-w-sm w-full transform transition-all duration-300 ease-in-out translate-x-full opacity-0"
     x-data="{ show: false }"
     x-init="
        setTimeout(() => { show = true; $el.classList.remove('translate-x-full', 'opacity-0'); $el.classList.add('translate-x-0', 'opacity-100'); }, 100);
        @if($duration > 0)
            setTimeout(() => { 
                $el.classList.remove('translate-x-0', 'opacity-100'); 
                $el.classList.add('translate-x-full', 'opacity-0'); 
                setTimeout(() => $el.remove(), 300);
            }, {{ $duration }});
        @endif
     ">
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 border {{ $typeConfig['border'] }} overflow-hidden">
        <div class="p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <div class="w-6 h-6 {{ $typeConfig['bg'] }} rounded-full flex items-center justify-center">
                        <i data-lucide="{{ $typeConfig['icon'] }}" class="w-4 h-4 text-white"></i>
                    </div>
                </div>
                <div class="ml-3 w-0 flex-1 pt-0.5">
                    @if($title)
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $title }}</p>
                    @endif
                    <p class="text-sm text-gray-600 dark:text-gray-400 {{ $title ? 'mt-1' : '' }}">{{ $message }}</p>
                </div>
                @if($dismissible)
                    <div class="ml-4 flex-shrink-0 flex">
                        <button @click="
                            $el.closest('[id^=toast-]').classList.remove('translate-x-0', 'opacity-100'); 
                            $el.closest('[id^=toast-]').classList.add('translate-x-full', 'opacity-0'); 
                            setTimeout(() => $el.closest('[id^=toast-]').remove(), 300);
                        " class="rounded-md inline-flex text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                            <span class="sr-only">Close</span>
                            <i data-lucide="x" class="w-5 h-5"></i>
                        </button>
                    </div>
                @endif
            </div>
        </div>
        
        @if($duration > 0)
            <!-- Progress bar -->
            <div class="h-1 bg-gray-200 dark:bg-gray-700">
                <div class="{{ $typeConfig['bg'] }} h-full transition-all ease-linear" 
                     style="animation: toast-progress {{ $duration }}ms linear forwards;"></div>
            </div>
        @endif
    </div>
</div>

@once
    @push('styles')
    <style>
        @keyframes toast-progress {
            from { width: 100%; }
            to { width: 0%; }
        }
    </style>
    @endpush
@endonce

@once
    @push('scripts')
    <script>
        // Toast notification helper function
        window.showToast = function(type, title, message, duration = 5000) {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();
            const toastId = 'toast-' + Date.now();
            
            const types = {
                success: { bg: 'bg-green-500', icon: 'check-circle', border: 'border-green-200 dark:border-green-800' },
                error: { bg: 'bg-red-500', icon: 'x-circle', border: 'border-red-200 dark:border-red-800' },
                warning: { bg: 'bg-yellow-500', icon: 'alert-triangle', border: 'border-yellow-200 dark:border-yellow-800' },
                info: { bg: 'bg-blue-500', icon: 'info', border: 'border-blue-200 dark:border-blue-800' }
            };
            
            const typeConfig = types[type] || types.info;
            
            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = 'transform transition-all duration-300 ease-in-out translate-x-full opacity-0 mb-4';
            toast.innerHTML = `
                <div class="max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 border ${typeConfig.border} overflow-hidden">
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-6 h-6 ${typeConfig.bg} rounded-full flex items-center justify-center">
                                    <i data-lucide="${typeConfig.icon}" class="w-4 h-4 text-white"></i>
                                </div>
                            </div>
                            <div class="ml-3 w-0 flex-1 pt-0.5">
                                ${title ? `<p class="text-sm font-medium text-gray-900 dark:text-white">${title}</p>` : ''}
                                <p class="text-sm text-gray-600 dark:text-gray-400 ${title ? 'mt-1' : ''}">${message}</p>
                            </div>
                            <div class="ml-4 flex-shrink-0 flex">
                                <button onclick="removeToast('${toastId}')" class="rounded-md inline-flex text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none transition-colors duration-200">
                                    <i data-lucide="x" class="w-5 h-5"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    ${duration > 0 ? `
                        <div class="h-1 bg-gray-200 dark:bg-gray-700">
                            <div class="${typeConfig.bg} h-full transition-all ease-linear" style="animation: toast-progress ${duration}ms linear forwards;"></div>
                        </div>
                    ` : ''}
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            // Initialize Lucide icons for the new toast
            if (window.lucide) {
                lucide.createIcons();
            }
            
            // Animate in
            setTimeout(() => {
                toast.className = 'transform transition-all duration-300 ease-in-out translate-x-0 opacity-100 mb-4';
            }, 100);
            
            // Auto remove
            if (duration > 0) {
                setTimeout(() => {
                    removeToast(toastId);
                }, duration);
            }
        };
        
        window.removeToast = function(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.className = 'transform transition-all duration-300 ease-in-out translate-x-full opacity-0 mb-4';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        };
        
        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(container);
            return container;
        }
    </script>
    @endpush
@endonce
