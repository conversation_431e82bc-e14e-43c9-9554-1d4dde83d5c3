@extends('layouts.main')

@section('title', 'Detail Event - ' . $event->title)

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6" data-aos="fade-up">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Detail Event</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $event->title }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.tickets.edit', $event) }}"
               class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Edit Event
            </a>
            <a href="{{ route('admin.tickets.index') }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Kembali
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Event Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Informasi Event</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Judul</label>
                        <p class="text-gray-900 dark:text-gray-100 font-medium">{{ $event->title }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Kategori</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $event->category->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Organizer</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $event->organizer->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @if($event->status === 'published') bg-green-100 text-green-800
                            @elseif($event->status === 'draft') bg-yellow-100 text-yellow-800
                            @else bg-red-100 text-red-800 @endif">
                            {{ ucfirst($event->status) }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Harga</label>
                        <p class="text-gray-900 dark:text-gray-100 font-medium">
                            @if($event->price > 0)
                                Rp {{ number_format($event->price, 0, ',', '.') }}
                            @else
                                Gratis
                            @endif
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Featured</label>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            @if($event->is_featured) bg-blue-100 text-blue-800
                            @else bg-gray-100 text-gray-800 @endif">
                            {{ $event->is_featured ? 'Ya' : 'Tidak' }}
                        </span>
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Deskripsi</label>
                    <p class="text-gray-900 dark:text-gray-100 mt-1">{{ $event->description }}</p>
                </div>
            </div>

            <!-- Venue Information -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Informasi Venue</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Nama Venue</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $event->venue_name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Kota</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $event->city }}</p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Alamat</label>
                    <p class="text-gray-900 dark:text-gray-100">{{ $event->venue_address }}</p>
                </div>
            </div>

            <!-- Date and Capacity -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="400">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Jadwal & Kapasitas</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Tanggal Mulai</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $event->start_date->format('d M Y, H:i') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Tanggal Selesai</label>
                        <p class="text-gray-900 dark:text-gray-100">{{ $event->end_date->format('d M Y, H:i') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Kapasitas Total</label>
                        <p class="text-gray-900 dark:text-gray-100 font-medium">{{ number_format($event->total_capacity) }} orang</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Kapasitas Tersedia</label>
                        <p class="text-gray-900 dark:text-gray-100 font-medium">{{ number_format($event->available_capacity) }} orang</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Event Poster -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Poster Event</h3>
                <div class="aspect-[3/4] bg-gray-100 dark:bg-dark-700 rounded-lg overflow-hidden">
                    <img src="{{ $event->poster }}" 
                         alt="{{ $event->title }}"
                         class="w-full h-full object-cover">
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Statistik</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">Total Tiket</span>
                        <span class="font-semibold text-gray-900 dark:text-gray-100">{{ $statistics['total_tickets'] }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">Tiket Terjual</span>
                        <span class="font-semibold text-green-600">{{ $statistics['used_tickets'] }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">Tiket Dibatalkan</span>
                        <span class="font-semibold text-red-600">{{ $statistics['cancelled_tickets'] }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">Total Revenue</span>
                        <span class="font-semibold text-primary">Rp {{ number_format($statistics['total_revenue'], 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-400">Utilisasi Kapasitas</span>
                        <span class="font-semibold text-gray-900 dark:text-gray-100">{{ number_format($statistics['capacity_utilization'], 1) }}%</span>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                        <span>Kapasitas Terpakai</span>
                        <span>{{ number_format($statistics['capacity_utilization'], 1) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full transition-all duration-300" 
                             style="width: {{ min($statistics['capacity_utilization'], 100) }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="400">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Aksi Cepat</h3>
                
                <div class="space-y-3">
                    <form action="{{ route('admin.tickets.toggle-featured', $event) }}" method="POST">
                        @csrf
                        <button type="submit" 
                                class="w-full px-4 py-2 text-sm font-medium rounded-lg transition-colors
                                @if($event->is_featured) 
                                    bg-yellow-100 text-yellow-800 hover:bg-yellow-200
                                @else 
                                    bg-blue-100 text-blue-800 hover:bg-blue-200
                                @endif">
                            @if($event->is_featured)
                                Hapus dari Featured
                            @else
                                Tambah ke Featured
                            @endif
                        </button>
                    </form>
                    
                    <a href="{{ route('tickets.show', $event->slug) }}" target="_blank"
                       class="block w-full px-4 py-2 text-sm font-medium text-center bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors">
                        Lihat di Frontend
                    </a>
                    
                    @if($statistics['total_tickets'] == 0)
                        <form action="{{ route('admin.tickets.destroy', $event) }}" method="POST" 
                              onsubmit="return confirm('Apakah Anda yakin ingin menghapus event ini?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full px-4 py-2 text-sm font-medium bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors">
                                Hapus Event
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
