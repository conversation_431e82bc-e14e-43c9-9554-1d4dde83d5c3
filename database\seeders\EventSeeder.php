<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Event;
use App\Models\Category;
use App\Models\User;
use Carbon\Carbon;

class Ticketseeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $organizers = User::where('role', User::ROLE_PENJUAL)->get();
        $categories = Category::all();

        $tickets = [
            [
                'title' => 'Jakarta Music Festival 2024',
                'slug' => 'jakarta-music-festival-2024',
                'description' => 'Festival musik terbesar di Jakarta dengan lineup artis internasional dan lokal terbaik. Nikmati pengalaman musik yang tak terlupakan dengan sound system berkualitas tinggi dan stage production yang memukau.',
                'short_description' => 'Festival musik terbesar di Jakarta dengan lineup artis internasional dan lokal terbaik.',
                'category_id' => $categories->where('slug', 'musik-konser')->first()->id,
                'organizer_id' => $organizers->random()->id,
                'venue_name' => 'Jakarta Convention Center',
                'venue_address' => 'Jl. Gatot Subroto, Senayan, Jakarta Pusat',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'latitude' => -6.2088,
                'longitude' => 106.8456,
                'start_date' => Carbon::now()->addDays(30),
                'end_date' => Carbon::now()->addDays(32),
                'price' => 150000,
                'original_price' => 200000,
                'total_capacity' => 5000,
                'available_capacity' => 5000,
                'status' => Event::STATUS_PUBLISHED,
                'is_featured' => true,
                'tags' => ['musik', 'festival', 'jakarta', 'konser'],
                'sale_start_date' => Carbon::now()->subDays(7),
                'sale_end_date' => Carbon::now()->addDays(29),
            ],
            [
                'title' => 'Digital Marketing Masterclass',
                'slug' => 'digital-marketing-masterclass',
                'description' => 'Workshop intensif digital marketing untuk meningkatkan skill pemasaran digital Anda. Dipandu oleh praktisi berpengalaman dengan studi kasus real dari industri.',
                'short_description' => 'Workshop intensif digital marketing untuk meningkatkan skill pemasaran digital Anda.',
                'category_id' => $categories->where('slug', 'workshop-seminar')->first()->id,
                'organizer_id' => $organizers->random()->id,
                'venue_name' => 'Balai Kartini',
                'venue_address' => 'Jl. Gatot Subroto Kav. 37, Jakarta Selatan',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'latitude' => -6.2297,
                'longitude' => 106.8253,
                'start_date' => Carbon::now()->addDays(15),
                'end_date' => Carbon::now()->addDays(15)->addHours(8),
                'price' => 299000,
                'total_capacity' => 200,
                'available_capacity' => 200,
                'status' => Event::STATUS_PUBLISHED,
                'is_featured' => false,
                'tags' => ['digital marketing', 'workshop', 'bisnis'],
                'sale_start_date' => Carbon::now()->subDays(5),
                'sale_end_date' => Carbon::now()->addDays(14),
            ],
            [
                'title' => 'Bali Food & Culture Festival',
                'slug' => 'bali-food-culture-festival',
                'description' => 'Festival kuliner dan budaya Bali yang menampilkan makanan tradisional, pertunjukan seni, dan workshop memasak. Rasakan kekayaan budaya Bali dalam satu tempat.',
                'short_description' => 'Festival kuliner dan budaya Bali dengan makanan tradisional dan pertunjukan seni.',
                'category_id' => $categories->where('slug', 'kuliner')->first()->id,
                'organizer_id' => $organizers->random()->id,
                'venue_name' => 'Sanur Beach',
                'venue_address' => 'Pantai Sanur, Denpasar, Bali',
                'city' => 'Denpasar',
                'province' => 'Bali',
                'latitude' => -8.6705,
                'longitude' => 115.2126,
                'start_date' => Carbon::now()->addDays(45),
                'end_date' => Carbon::now()->addDays(47),
                'price' => 75000,
                'total_capacity' => 1000,
                'available_capacity' => 1000,
                'status' => Event::STATUS_PUBLISHED,
                'is_featured' => true,
                'tags' => ['kuliner', 'budaya', 'bali', 'festival'],
                'sale_start_date' => Carbon::now()->subDays(3),
                'sale_end_date' => Carbon::now()->addDays(44),
            ],
            [
                'title' => 'Tech Conference Indonesia 2024',
                'slug' => 'tech-conference-indonesia-2024',
                'description' => 'Konferensi teknologi terbesar di Indonesia dengan pembicara dari perusahaan teknologi terkemuka. Pelajari tren teknologi terbaru dan networking dengan para profesional IT.',
                'short_description' => 'Konferensi teknologi terbesar di Indonesia dengan pembicara dari perusahaan teknologi terkemuka.',
                'category_id' => $categories->where('slug', 'teknologi')->first()->id,
                'organizer_id' => $organizers->random()->id,
                'venue_name' => 'ICE BSD',
                'venue_address' => 'Jl. BSD Grand Boulevard, Tangerang Selatan',
                'city' => 'Tangerang Selatan',
                'province' => 'Banten',
                'latitude' => -6.3018,
                'longitude' => 106.6519,
                'start_date' => Carbon::now()->addDays(60),
                'end_date' => Carbon::now()->addDays(61),
                'price' => 500000,
                'original_price' => 750000,
                'total_capacity' => 800,
                'available_capacity' => 800,
                'status' => Event::STATUS_PUBLISHED,
                'is_featured' => true,
                'tags' => ['teknologi', 'conference', 'IT', 'programming'],
                'sale_start_date' => Carbon::now()->subDays(10),
                'sale_end_date' => Carbon::now()->addDays(59),
            ],
            [
                'title' => 'Startup Pitch Competition',
                'slug' => 'startup-pitch-competition',
                'description' => 'Kompetisi pitch startup dengan hadiah total 1 miliar rupiah. Kesempatan emas untuk startup early stage mendapatkan funding dan mentoring dari investor terkemuka.',
                'short_description' => 'Kompetisi pitch startup dengan hadiah total 1 miliar rupiah.',
                'category_id' => $categories->where('slug', 'bisnis-networking')->first()->id,
                'organizer_id' => $organizers->random()->id,
                'venue_name' => 'Universitas Indonesia',
                'venue_address' => 'Kampus UI Depok, Jawa Barat',
                'city' => 'Depok',
                'province' => 'Jawa Barat',
                'latitude' => -6.3622,
                'longitude' => 106.8263,
                'start_date' => Carbon::now()->addDays(25),
                'end_date' => Carbon::now()->addDays(25)->addHours(10),
                'price' => 0,
                'is_free' => true,
                'total_capacity' => 300,
                'available_capacity' => 300,
                'status' => Event::STATUS_PUBLISHED,
                'is_featured' => false,
                'requires_approval' => true,
                'tags' => ['startup', 'pitch', 'competition', 'bisnis'],
                'sale_start_date' => Carbon::now()->subDays(14),
                'sale_end_date' => Carbon::now()->addDays(20),
            ],
        ];

        foreach ($tickets as $eventData) {
            Event::create($eventData);
        }

        // Create additional random tickets
        for ($i = 0; $i < 15; $i++) {
            Event::create([
                'title' => 'Event Sample ' . ($i + 1),
                'slug' => 'event-sample-' . ($i + 1),
                'description' => 'Deskripsi lengkap untuk event sample ' . ($i + 1) . '. Event ini adalah contoh event yang dibuat untuk testing dan demo aplikasi tixara.',
                'short_description' => 'Event sample untuk testing aplikasi tixara.',
                'category_id' => $categories->random()->id,
                'organizer_id' => $organizers->random()->id,
                'venue_name' => 'Venue Sample ' . ($i + 1),
                'venue_address' => 'Alamat venue sample ' . ($i + 1),
                'city' => collect(['Jakarta', 'Bandung', 'Surabaya', 'Medan', 'Yogyakarta'])->random(),
                'province' => 'Jawa Barat',
                'start_date' => Carbon::now()->addDays(rand(10, 90)),
                'end_date' => Carbon::now()->addDays(rand(10, 90))->addHours(rand(2, 12)),
                'price' => rand(50000, 500000),
                'total_capacity' => rand(100, 1000),
                'available_capacity' => rand(100, 1000),
                'status' => collect([Event::STATUS_PUBLISHED, Event::STATUS_DRAFT])->random(),
                'is_featured' => rand(0, 1) == 1,
                'tags' => ['sample', 'test', 'demo'],
                'sale_start_date' => Carbon::now()->subDays(rand(1, 30)),
                'sale_end_date' => Carbon::now()->addDays(rand(10, 89)),
            ]);
        }
    }
}
