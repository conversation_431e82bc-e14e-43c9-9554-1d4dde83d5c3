@echo off
echo Creating Missing Events Table for TiXara...

echo.
echo [1/6] Checking current database state...
php artisan tinker --execute="
try {
    echo 'Checking current database state...' . PHP_EOL;
    
    \$tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');
    \$tableNames = array_map(function(\$table) {
        return array_values((array)\$table)[0];
    }, \$tables);
    
    \$hasUsers = in_array('users', \$tableNames);
    \$hasCategories = in_array('categories', \$tableNames);
    \$hasEvents = in_array('events', \$tableNames);
    \$hasOrders = in_array('orders', \$tableNames);
    \$hasTickets = in_array('tickets', \$tableNames);
    
    echo 'Current tables:' . PHP_EOL;
    echo '- Users: ' . (\$hasUsers ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Categories: ' . (\$hasCategories ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Events: ' . (\$hasEvents ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Orders: ' . (\$hasOrders ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Tickets: ' . (\$hasTickets ? 'EXISTS' : 'MISSING') . PHP_EOL;
    
    if (!\$hasEvents) {
        echo PHP_EOL . 'CONFIRMED: Events table is missing' . PHP_EOL;
    } else {
        echo PHP_EOL . 'WARNING: Events table already exists!' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Database check error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [2/6] Disabling foreign key checks...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=0;');
    echo 'Foreign key checks disabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [3/6] Creating events table...
php artisan migrate --path=database/migrations/2024_01_01_000003_create_events_table.php --force

echo.
echo [4/6] Fixing dependent tables if needed...
php artisan tinker --execute="
try {
    echo 'Checking dependent tables...' . PHP_EOL;
    
    // Check if orders table exists and has correct foreign keys
    if (\Illuminate\Support\Facades\Schema::hasTable('orders')) {
        \$ordersColumns = \Illuminate\Support\Facades\Schema::getColumnListing('orders');
        if (!in_array('event_id', \$ordersColumns)) {
            echo 'Orders table needs event_id column' . PHP_EOL;
        } else {
            echo 'Orders table has event_id column' . PHP_EOL;
        }
    }
    
    // Check if tickets table exists and has correct foreign keys
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets')) {
        \$ticketsColumns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
        if (!in_array('event_id', \$ticketsColumns)) {
            echo 'Tickets table needs event_id column' . PHP_EOL;
        } else {
            echo 'Tickets table has event_id column' . PHP_EOL;
        }
    }
    
} catch (Exception \$e) {
    echo 'Dependent table check error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/6] Re-enabling foreign key checks...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    echo 'Foreign key checks re-enabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [6/6] Verifying events table creation...
php artisan tinker --execute="
try {
    echo 'Verifying events table...' . PHP_EOL;
    
    if (\Illuminate\Support\Facades\Schema::hasTable('events')) {
        echo '✓ Events table created successfully' . PHP_EOL;
        
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('events');
        echo 'Events table columns:' . PHP_EOL;
        foreach (\$columns as \$column) {
            echo '  - ' . \$column . PHP_EOL;
        }
        
        // Test basic operations
        echo PHP_EOL . 'Testing basic operations...' . PHP_EOL;
        
        // Test model instantiation
        \$event = new \App\Models\Event();
        echo '✓ Event model can be instantiated' . PHP_EOL;
        
        // Test relationships
        if (\Illuminate\Support\Facades\Schema::hasTable('categories')) {
            \$category = \App\Models\Category::first();
            if (\$category) {
                echo '✓ Can access category relationship' . PHP_EOL;
            }
        }
        
        if (\Illuminate\Support\Facades\Schema::hasTable('users')) {
            \$user = \App\Models\User::first();
            if (\$user) {
                echo '✓ Can access organizer relationship' . PHP_EOL;
            }
        }
        
    } else {
        echo '✗ Events table creation failed!' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Verification error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo ========================================
echo Events Table Creation Results
echo ========================================
echo.
echo ✓ COMPLETED TASKS:
echo   - Checked database state
echo   - Created events table
echo   - Verified table structure
echo   - Tested basic operations
echo.
echo ✓ EVENTS TABLE STRUCTURE:
echo   - id (primary key)
echo   - title, description, venue_name
echo   - start_date, end_date
echo   - category_id (foreign key to categories)
echo   - organizer_id (foreign key to users)
echo   - price, total_capacity, available_capacity
echo   - status, poster_url, gallery_images
echo   - timestamps
echo.
echo ✓ NEXT STEPS:
echo   - Run remaining migrations: php artisan migrate
echo   - Seed sample data: php artisan db:seed
echo   - Test application: php artisan serve
echo.
echo Events table should now be available for use!
echo.
pause
