<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add organization field for penjual/organizer
            $table->string('organization')->nullable()->after('address');
            $table->text('bio')->nullable()->after('organization');
            
            // Add social media links as JSON
            $table->json('social_media')->nullable()->after('bio');
            
            // Add indexes
            $table->index(['role', 'organization']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role', 'organization']);
            $table->dropColumn(['organization', 'bio', 'social_media']);
        });
    }
};
