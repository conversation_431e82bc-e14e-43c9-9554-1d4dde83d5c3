<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class FixAllPasswords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:fix-passwords';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix all user passwords to match the documented credentials';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing all user passwords...');
        $this->newLine();

        $users = User::all();
        $fixed = 0;

        foreach ($users as $user) {
            $newPassword = $this->getPasswordForRole($user->role);
            
            // Update password
            $user->update([
                'password' => Hash::make($newPassword),
                'is_active' => true,
                'email_verified_at' => $user->email_verified_at ?? now(),
            ]);

            $this->info("✅ Fixed: {$user->email} | Password: {$newPassword}");
            $fixed++;
        }

        $this->newLine();
        $this->info("🎉 Fixed {$fixed} user passwords!");
        $this->newLine();

        // Display login credentials
        $this->info('=== 🔑 LOGIN CREDENTIALS ===');
        $this->newLine();

        $adminUsers = User::where('role', 'admin')->get();
        if ($adminUsers->count() > 0) {
            $this->info('👑 ADMIN ACCOUNTS:');
            foreach ($adminUsers as $user) {
                $this->info("Email: {$user->email} | Password: TiXara@2024");
            }
            $this->newLine();
        }

        $staffUsers = User::where('role', 'staff')->get();
        if ($staffUsers->count() > 0) {
            $this->info('👥 STAFF ACCOUNTS:');
            foreach ($staffUsers as $user) {
                $this->info("Email: {$user->email} | Password: Staff@2024");
            }
            $this->newLine();
        }

        $penjualUsers = User::where('role', 'penjual')->get();
        if ($penjualUsers->count() > 0) {
            $this->info('🎪 PENJUAL (EVENT ORGANIZER) ACCOUNTS:');
            foreach ($penjualUsers as $user) {
                $this->info("Email: {$user->email} | Password: Penjual@2024");
            }
            $this->newLine();
        }

        $pembeliUsers = User::where('role', 'pembeli')->get();
        if ($pembeliUsers->count() > 0) {
            $this->info('🛒 PEMBELI (CUSTOMER) ACCOUNTS:');
            foreach ($pembeliUsers as $user) {
                $this->info("Email: {$user->email} | Password: Pembeli@2024");
            }
            $this->newLine();
        }

        $this->info('💡 You can now login with any of the above credentials!');
        $this->info('🌐 Access the application at: http://localhost/Project-TiXara.my.id');

        return 0;
    }

    private function getPasswordForRole($role)
    {
        return match($role) {
            'admin' => 'TiXara@2024',
            'staff' => 'Staff@2024',
            'penjual' => 'Penjual@2024',
            'pembeli' => 'Pembeli@2024',
            default => 'TiXara@2024'
        };
    }
}
