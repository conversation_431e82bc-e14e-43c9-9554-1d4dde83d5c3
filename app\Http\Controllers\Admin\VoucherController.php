<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Voucher;
use App\Models\Event;
use App\Models\Category;
use App\Models\VoucherUsage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Carbon\Carbon;

class VoucherController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of vouchers
     */
    public function index(Request $request)
    {
        $query = Voucher::query()->with(['createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true)
                          ->where('starts_at', '<=', now())
                          ->where('expires_at', '>=', now());
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'expired':
                    $query->where('expires_at', '<', now());
                    break;
                case 'scheduled':
                    $query->where('starts_at', '>', now());
                    break;
                case 'exhausted':
                    $query->whereNotNull('usage_limit')
                          ->whereRaw('used_count >= usage_limit');
                    break;
            }
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        if (in_array($sortBy, ['code', 'name', 'type', 'value', 'used_count', 'starts_at', 'expires_at', 'created_at'])) {
            $query->orderBy($sortBy, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $vouchers = $query->paginate(15)->withQueryString();

        // Statistics
        $stats = [
            'total' => Voucher::count(),
            'active' => Voucher::valid()->count(),
            'expired' => Voucher::where('expires_at', '<', now())->count(),
            'total_usage' => VoucherUsage::count(),
            'total_savings' => VoucherUsage::sum('discount_amount'),
        ];

        return view('pages.admin.vouchers.index', [
            'title' => 'Kelola Voucher',
            'vouchers' => $vouchers,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'type', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new voucher
     */
    public function create()
    {
        $events = Event::published()->orderBy('title')->get(['id', 'title']);
        $categories = Category::active()->orderBy('name')->get(['id', 'name']);

        return view('pages.admin.vouchers.create', [
            'title' => 'Tambah Voucher Baru',
            'events' => $events,
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created voucher
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:50|unique:vouchers,code',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'min_order_amount' => 'nullable|numeric|min:0',
            'max_discount_amount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'required|integer|min:1|max:100',
            'starts_at' => 'required|date|after_or_equal:today',
            'expires_at' => 'required|date|after:starts_at',
            'applicable_events' => 'nullable|array',
            'applicable_events.*' => 'exists:events,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'exists:categories,id',
            'applicable_user_roles' => 'nullable|array',
            'applicable_user_roles.*' => 'in:pembeli,penjual',
            'min_tickets' => 'nullable|integer|min:1',
            'max_tickets' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Additional validation
        if ($request->type === 'percentage' && $request->value > 100) {
            return back()->withErrors(['value' => 'Persentase diskon tidak boleh lebih dari 100%'])->withInput();
        }

        if ($request->max_tickets && $request->min_tickets && $request->max_tickets < $request->min_tickets) {
            return back()->withErrors(['max_tickets' => 'Maksimal tiket harus lebih besar dari minimal tiket'])->withInput();
        }

        $voucher = Voucher::create([
            'code' => strtoupper($request->code),
            'name' => $request->name,
            'description' => $request->description,
            'type' => $request->type,
            'value' => $request->value,
            'min_order_amount' => $request->min_order_amount ?? 0,
            'max_discount_amount' => $request->max_discount_amount,
            'usage_limit' => $request->usage_limit,
            'usage_limit_per_user' => $request->usage_limit_per_user,
            'starts_at' => $request->starts_at,
            'expires_at' => $request->expires_at,
            'applicable_events' => $request->applicable_events,
            'applicable_categories' => $request->applicable_categories,
            'applicable_user_roles' => $request->applicable_user_roles,
            'min_tickets' => $request->min_tickets ?? 1,
            'max_tickets' => $request->max_tickets,
            'is_active' => $request->boolean('is_active', true),
            'is_public' => $request->boolean('is_public', true),
            'created_by_type' => 'admin',
            'created_by_id' => auth()->id(),
        ]);

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher berhasil dibuat!');
    }

    /**
     * Display the specified voucher
     */
    public function show(Voucher $voucher)
    {
        $voucher->load(['createdBy', 'usages.user', 'usages.event', 'usages.order']);
        
        // Usage statistics
        $usageStats = [
            'total_usage' => $voucher->usages->count(),
            'total_savings' => $voucher->usages->sum('discount_amount'),
            'unique_users' => $voucher->usages->unique('user_id')->count(),
            'avg_discount' => $voucher->usages->avg('discount_amount'),
            'usage_by_day' => $voucher->usages()
                ->selectRaw('DATE(used_at) as date, COUNT(*) as count, SUM(discount_amount) as total_discount')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];

        return view('pages.admin.vouchers.show', [
            'title' => 'Detail Voucher: ' . $voucher->code,
            'voucher' => $voucher,
            'usageStats' => $usageStats,
        ]);
    }

    /**
     * Show the form for editing the specified voucher
     */
    public function edit(Voucher $voucher)
    {
        $events = Event::published()->orderBy('title')->get(['id', 'title']);
        $categories = Category::active()->orderBy('name')->get(['id', 'name']);

        return view('pages.admin.vouchers.edit', [
            'title' => 'Edit Voucher: ' . $voucher->code,
            'voucher' => $voucher,
            'events' => $events,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified voucher
     */
    public function update(Request $request, Voucher $voucher)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:50|unique:vouchers,code,' . $voucher->id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'min_order_amount' => 'nullable|numeric|min:0',
            'max_discount_amount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'required|integer|min:1|max:100',
            'starts_at' => 'required|date',
            'expires_at' => 'required|date|after:starts_at',
            'applicable_events' => 'nullable|array',
            'applicable_events.*' => 'exists:events,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'exists:categories,id',
            'applicable_user_roles' => 'nullable|array',
            'applicable_user_roles.*' => 'in:pembeli,penjual',
            'min_tickets' => 'nullable|integer|min:1',
            'max_tickets' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Additional validation
        if ($request->type === 'percentage' && $request->value > 100) {
            return back()->withErrors(['value' => 'Persentase diskon tidak boleh lebih dari 100%'])->withInput();
        }

        if ($request->max_tickets && $request->min_tickets && $request->max_tickets < $request->min_tickets) {
            return back()->withErrors(['max_tickets' => 'Maksimal tiket harus lebih besar dari minimal tiket'])->withInput();
        }

        $voucher->update([
            'code' => strtoupper($request->code),
            'name' => $request->name,
            'description' => $request->description,
            'type' => $request->type,
            'value' => $request->value,
            'min_order_amount' => $request->min_order_amount ?? 0,
            'max_discount_amount' => $request->max_discount_amount,
            'usage_limit' => $request->usage_limit,
            'usage_limit_per_user' => $request->usage_limit_per_user,
            'starts_at' => $request->starts_at,
            'expires_at' => $request->expires_at,
            'applicable_events' => $request->applicable_events,
            'applicable_categories' => $request->applicable_categories,
            'applicable_user_roles' => $request->applicable_user_roles,
            'min_tickets' => $request->min_tickets ?? 1,
            'max_tickets' => $request->max_tickets,
            'is_active' => $request->boolean('is_active'),
            'is_public' => $request->boolean('is_public'),
        ]);

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher berhasil diperbarui!');
    }

    /**
     * Remove the specified voucher
     */
    public function destroy(Voucher $voucher)
    {
        // Check if voucher has been used
        if ($voucher->usages()->count() > 0) {
            return back()->with('error', 'Voucher tidak dapat dihapus karena sudah pernah digunakan!');
        }

        $voucher->delete();

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher berhasil dihapus!');
    }

    /**
     * Toggle voucher active status
     */
    public function toggleStatus(Voucher $voucher)
    {
        $voucher->update(['is_active' => !$voucher->is_active]);

        $status = $voucher->is_active ? 'diaktifkan' : 'dinonaktifkan';

        return redirect()->back()
            ->with('success', "Voucher berhasil {$status}!");
    }

    /**
     * Generate voucher code
     */
    public function generateCode(Request $request)
    {
        $prefix = $request->get('prefix', 'TIKPRO');
        $length = $request->get('length', 6);
        
        do {
            $code = strtoupper($prefix . Str::random($length));
        } while (Voucher::where('code', $code)->exists());

        return response()->json(['code' => $code]);
    }
}
