<!-- Logo -->
<div class="flex items-center flex-shrink-0 px-4 py-6 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center w-full">
        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
            <span class="text-white font-bold text-lg">T</span>
        </div>
        <div class="ml-3">
            <h1 class="text-xl font-bold text-gray-900 dark:text-white">TiXara</h1>
            <p class="text-sm text-gray-500 dark:text-gray-400">Organizer Panel</p>
        </div>
        <button @click="showMobileMenu = false" class="ml-auto p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 lg:hidden">
            <i data-lucide="x" class="w-4 h-4 text-gray-500 dark:text-gray-400"></i>
        </button>
    </div>
</div>

<!-- Navigation -->
<nav class="flex-1 px-3 py-4 space-y-2 overflow-y-auto scrollbar-thin">
    <!-- Dashboard -->
    <a href="{{ route('organizer.dashboard') }}"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('organizer.dashboard') ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}">
        <div class="flex items-center justify-center w-6 h-6 mr-3 {{ request()->routeIs('organizer.dashboard') ? 'text-green-600 dark:text-green-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}">
            <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Dashboard</span>
        @if(request()->routeIs('organizer.dashboard'))
            <div class="ml-auto w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full"></div>
        @endif
    </a>

    <!-- My Events -->
    <a href="{{ route('organizer.tickets.index') }}"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('organizer.tickets.*') ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}">
        <div class="flex items-center justify-center w-6 h-6 mr-3 {{ request()->routeIs('organizer.tickets.*') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}">
            <i data-lucide="calendar" class="w-5 h-5"></i>
        </div>
        <span class="truncate">My Events</span>
        @if(request()->routeIs('organizer.tickets.*'))
            <div class="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
        @endif
    </a>

    <!-- Orders -->
    <a href="{{ route('organizer.orders.index') }}"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('organizer.orders.*') ? 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}">
        <div class="flex items-center justify-center w-6 h-6 mr-3 {{ request()->routeIs('organizer.orders.*') ? 'text-purple-600 dark:text-purple-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}">
            <i data-lucide="shopping-cart" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Orders</span>
        @if(request()->routeIs('organizer.orders.*'))
            <div class="ml-auto w-2 h-2 bg-purple-600 dark:bg-purple-400 rounded-full"></div>
        @endif
    </a>

    <!-- Analytics -->
    <a href="{{ route('organizer.analytics.index') }}"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('organizer.analytics.*') ? 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}">
        <div class="flex items-center justify-center w-6 h-6 mr-3 {{ request()->routeIs('organizer.analytics.*') ? 'text-orange-600 dark:text-orange-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}">
            <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Analytics</span>
        @if(request()->routeIs('organizer.analytics.*'))
            <div class="ml-auto w-2 h-2 bg-orange-600 dark:bg-orange-400 rounded-full"></div>
        @endif
    </a>

    <!-- Payments -->
    <a href="{{ route('organizer.payments.index') }}"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('organizer.payments.*') ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}">
        <div class="flex items-center justify-center w-6 h-6 mr-3 {{ request()->routeIs('organizer.payments.*') ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}">
            <i data-lucide="credit-card" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Payments</span>
        @if(request()->routeIs('organizer.payments.*'))
            <div class="ml-auto w-2 h-2 bg-yellow-600 dark:bg-yellow-400 rounded-full"></div>
        @endif
    </a>

    <!-- Divider -->
    <div class="my-4 border-t border-gray-200 dark:border-gray-700"></div>

    <!-- Profile -->
    <a href="{{ route('organizer.profile.edit') }}"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('organizer.profile.*') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}">
        <div class="flex items-center justify-center w-6 h-6 mr-3 {{ request()->routeIs('organizer.profile.*') ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}">
            <i data-lucide="user" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Profile</span>
        @if(request()->routeIs('organizer.profile.*'))
            <div class="ml-auto w-2 h-2 bg-indigo-600 dark:bg-indigo-400 rounded-full"></div>
        @endif
    </a>

    <!-- Settings -->
    <a href="{{ route('organizer.settings.index') }}"
       class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('organizer.settings.*') ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}">
        <div class="flex items-center justify-center w-6 h-6 mr-3 {{ request()->routeIs('organizer.settings.*') ? 'text-gray-700 dark:text-gray-300' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}">
            <i data-lucide="settings" class="w-5 h-5"></i>
        </div>
        <span class="truncate">Settings</span>
        @if(request()->routeIs('organizer.settings.*'))
            <div class="ml-auto w-2 h-2 bg-gray-600 dark:bg-gray-400 rounded-full"></div>
        @endif
    </a>
</nav>

<!-- User Info -->
<div class="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4">
    <div class="flex items-center">
        <img src="{{ auth()->user()->avatar_url }}" 
             alt="{{ auth()->user()->name }}" 
             class="w-10 h-10 rounded-xl object-cover ring-2 ring-gray-200 dark:ring-gray-700">
        <div class="ml-3 flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ auth()->user()->name }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ ucfirst(auth()->user()->role) }}</p>
        </div>
        <div class="ml-2">
            <button class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200" 
                    onclick="document.getElementById('logout-form').submit()">
                <i data-lucide="log-out" class="w-4 h-4 text-gray-500 dark:text-gray-400"></i>
            </button>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="hidden">
                @csrf
            </form>
        </div>
    </div>
</div>
