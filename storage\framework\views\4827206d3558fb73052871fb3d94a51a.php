<?php $__env->startSection('title', '<PERSON><PERSON><PERSON><PERSON> - Ti<PERSON>'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Contact Page Styles */
.contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.contact-card {
    background: white;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.contact-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.contact-icon.email {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.contact-icon.phone {
    background: linear-gradient(135deg, #10b981, #059669);
}

.contact-icon.address {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.contact-icon.social {
    background: linear-gradient(135deg, #ec4899, #db2777);
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 16px 32px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.map-container {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="contact-hero text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 backdrop-blur-sm">
                <i data-lucide="phone" class="w-10 h-10 text-white"></i>
            </div>
            <h1 class="text-5xl md:text-6xl font-bold mb-6">
                Hubungi Kami
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                Kami siap membantu Anda 24/7
            </p>
            <p class="text-lg mb-12 max-w-2xl mx-auto opacity-80">
                Punya pertanyaan tentang TiXara? Tim support kami siap membantu Anda dengan respon cepat dan solusi terbaik.
            </p>
        </div>
    </div>
</div>

<!-- Contact Information -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Informasi Kontak
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Berbagai cara untuk menghubungi tim TiXara
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 contact-grid">
            <!-- Email -->
            <div class="contact-card text-center">
                <div class="contact-icon email mx-auto">
                    <i data-lucide="mail" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Email</h3>
                <p class="text-gray-600 mb-4">Kirim email untuk pertanyaan detail</p>
                <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-semibold">
                    <EMAIL>
                </a>
                <br>
                <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-semibold">
                    <EMAIL>
                </a>
            </div>

            <!-- Phone -->
            <div class="contact-card text-center">
                <div class="contact-icon phone mx-auto">
                    <i data-lucide="phone" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Telepon</h3>
                <p class="text-gray-600 mb-4">Hubungi kami langsung</p>
                <a href="tel:+6281234567890" class="text-green-600 hover:text-green-700 font-semibold">
                    +62 812-3456-7890
                </a>
                <br>
                <span class="text-sm text-gray-500">24/7 Customer Support</span>
            </div>

            <!-- Address -->
            <div class="contact-card text-center">
                <div class="contact-icon address mx-auto">
                    <i data-lucide="map-pin" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Alamat</h3>
                <p class="text-gray-600 mb-4">Kunjungi kantor kami</p>
                <address class="text-gray-700 not-italic">
                    Jl. Teknologi No. 123<br>
                    Jakarta Selatan 12345<br>
                    Indonesia
                </address>
            </div>

            <!-- Social Media -->
            <div class="contact-card text-center">
                <div class="contact-icon social mx-auto">
                    <i data-lucide="share-2" class="w-8 h-8 text-white"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Social Media</h3>
                <p class="text-gray-600 mb-4">Follow kami di sosial media</p>
                <div class="flex justify-center space-x-4">
                    <a href="#" class="text-blue-600 hover:text-blue-700">
                        <i data-lucide="facebook" class="w-5 h-5"></i>
                    </a>
                    <a href="#" class="text-pink-600 hover:text-pink-700">
                        <i data-lucide="instagram" class="w-5 h-5"></i>
                    </a>
                    <a href="#" class="text-blue-400 hover:text-blue-500">
                        <i data-lucide="twitter" class="w-5 h-5"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contact Form & Map -->
<div class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div>
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                    Kirim Pesan
                </h2>
                
                <form action="<?php echo e(route('contact.send')); ?>" method="POST" class="space-y-6">
                    <?php echo csrf_field(); ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="name" class="form-label">Nama Lengkap</label>
                            <input type="text" id="name" name="name" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" id="email" name="email" class="form-input" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone" class="form-label">Nomor Telepon</label>
                        <input type="tel" id="phone" name="phone" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label for="subject" class="form-label">Subjek</label>
                        <select id="subject" name="subject" class="form-input" required>
                            <option value="">Pilih Subjek</option>
                            <option value="general">Pertanyaan Umum</option>
                            <option value="technical">Masalah Teknis</option>
                            <option value="billing">Billing & Pembayaran</option>
                            <option value="partnership">Kerjasama</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="message" class="form-label">Pesan</label>
                        <textarea id="message" name="message" class="form-input form-textarea" rows="5" required></textarea>
                    </div>
                    
                    <button type="submit" class="submit-btn">
                        <i data-lucide="send" class="w-5 h-5 inline mr-2"></i>
                        Kirim Pesan
                    </button>
                </form>
            </div>

            <!-- Map -->
            <div>
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
                    Lokasi Kami
                </h2>
                
                <div class="map-container">
                    <iframe 
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.521260322283!2d106.8195613507864!3d-6.194741395493371!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69f5390917b759%3A0x6b45e67356080477!2sJl.%20Teknologi%2C%20Jakarta%20Selatan%2C%20Daerah%20Khusus%20Ibukota%20Jakarta!5e0!3m2!1sen!2sid!4v1635123456789!5m2!1sen!2sid"
                        width="100%" 
                        height="400" 
                        style="border:0;" 
                        allowfullscreen="" 
                        loading="lazy" 
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
                
                <div class="mt-8 p-6 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Jam Operasional</h3>
                    <div class="space-y-2 text-gray-600 dark:text-gray-300">
                        <div class="flex justify-between">
                            <span>Senin - Jumat</span>
                            <span>09:00 - 18:00 WIB</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Sabtu</span>
                            <span>09:00 - 15:00 WIB</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Minggu</span>
                            <span>Tutup</span>
                        </div>
                        <div class="border-t border-gray-300 dark:border-gray-600 pt-2 mt-4">
                            <div class="flex justify-between font-semibold">
                                <span>Customer Support</span>
                                <span>24/7</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Pertanyaan yang Sering Diajukan
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">
                Mungkin jawaban yang Anda cari sudah ada di sini
            </p>
        </div>

        <div class="space-y-4">
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Bagaimana cara membeli tiket di TiXara?
                </h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Anda bisa browse event, pilih tiket yang diinginkan, isi data pembeli, pilih metode pembayaran, dan konfirmasi pembelian.
                </p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Apakah bisa refund tiket?
                </h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Kebijakan refund tergantung pada organizer event. Silakan cek detail kebijakan refund di halaman event.
                </p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Bagaimana cara menjadi organizer?
                </h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Daftar akun, pilih sebagai organizer, lengkapi verifikasi identitas, dan Anda bisa mulai membuat event.
                </p>
            </div>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo e(route('guide.faq')); ?>" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-300">
                <i data-lucide="help-circle" class="w-5 h-5 mr-2"></i>
                Lihat Semua FAQ
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
/*
 * Contact Page JavaScript
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const message = document.getElementById('message').value.trim();
            
            if (!name || !email || !message) {
                e.preventDefault();
                alert('Mohon lengkapi semua field yang wajib diisi.');
                return false;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('.submit-btn');
            submitBtn.innerHTML = '<i data-lucide="loader" class="w-5 h-5 inline mr-2 animate-spin"></i>Mengirim...';
            submitBtn.disabled = true;
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/contact.blade.php ENDPATH**/ ?>