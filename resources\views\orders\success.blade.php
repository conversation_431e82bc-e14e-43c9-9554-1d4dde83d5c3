@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-green-50 via-white to-primary/10">

    <!-- Success Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8" data-aos="fade-up">
                <!-- Success Animation -->
                <div class="w-24 h-24 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 success-animation">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                    </svg>
                </div>

                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Pembayaran Berhasil! 🎉
                </h1>
                <p class="text-lg text-gray-600 mb-2">
                    Terima kasih! Tiket Anda sudah aktif dan siap digunakan.
                </p>
                <p class="text-sm text-gray-500">
                    Order #{{ $order->order_number }} • {{ $order->paid_at->format('d M Y, H:i') }} WIB
                </p>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8" data-aos="fade-up" data-aos-delay="200">
                <a href="{{ route('tickets.my-tickets') }}"
                   class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="ticket" class="w-6 h-6 text-primary"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-1">Lihat Tiket</h3>
                    <p class="text-sm text-gray-600">Akses tiket digital Anda</p>
                </a>

                <button onclick="downloadTicket()"
                        class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="download" class="w-6 h-6 text-blue-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-1">Download Tiket</h3>
                    <p class="text-sm text-gray-600">Simpan tiket ke perangkat</p>
                </button>

                <button onclick="shareEvent()"
                        class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="share-2" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-1">Bagikan Event</h3>
                    <p class="text-sm text-gray-600">Ajak teman bergabung</p>
                </button>
            </div>
        </div>
    </section>

    <!-- Order Details -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Event Information -->
                <div class="lg:col-span-2 space-y-6">

                    <!-- Event Card -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden" data-aos="fade-up">
                        <div class="relative">
                            <img src="{{ $order->event->poster_url }}"
                                 alt="{{ $order->event->title }}"
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 left-4">
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    Tiket Aktif
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">{{ $order->event->title }}</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="calendar" class="w-5 h-5 text-gray-400"></i>
                                    <div>
                                        <div class="font-semibold text-gray-900">{{ $order->event->start_date->format('d M Y') }}</div>
                                        <div class="text-gray-600">{{ $order->event->start_date->format('H:i') }} - {{ $order->event->end_date->format('H:i') }} WIB</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="map-pin" class="w-5 h-5 text-gray-400"></i>
                                    <div>
                                        <div class="font-semibold text-gray-900">{{ $order->event->venue_name }}</div>
                                        <div class="text-gray-600">{{ $order->event->city }}</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="user" class="w-5 h-5 text-gray-400"></i>
                                    <div>
                                        <div class="font-semibold text-gray-900">{{ $order->customer_name }}</div>
                                        <div class="text-gray-600">{{ $order->quantity }} tiket</div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="credit-card" class="w-5 h-5 text-gray-400"></i>
                                    <div>
                                        <div class="font-semibold text-gray-900">{{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</div>
                                        <div class="text-gray-600">{{ $order->payment_reference }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Important Information -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-lg font-bold text-blue-900 mb-4 flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                            Informasi Penting
                        </h3>
                        <div class="space-y-3 text-sm text-blue-800">
                            <div class="flex items-start space-x-3">
                                <i data-lucide="smartphone" class="w-4 h-4 mt-0.5 text-blue-600"></i>
                                <div>
                                    <div class="font-semibold">Tiket Digital</div>
                                    <div>Tunjukkan tiket digital atau QR code saat masuk event</div>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i data-lucide="clock" class="w-4 h-4 mt-0.5 text-blue-600"></i>
                                <div>
                                    <div class="font-semibold">Datang Tepat Waktu</div>
                                    <div>Hadir 30 menit sebelum event dimulai untuk registrasi</div>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i data-lucide="id-card" class="w-4 h-4 mt-0.5 text-blue-600"></i>
                                <div>
                                    <div class="font-semibold">Bawa Identitas</div>
                                    <div>Siapkan KTP/SIM/Paspor yang masih berlaku</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24">
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                            <h3 class="text-lg font-bold text-gray-900 mb-4">Ringkasan Pembayaran</h3>

                            <div class="space-y-3 mb-6">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal</span>
                                    <span>Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Biaya admin</span>
                                    <span>Rp {{ number_format($order->admin_fee, 0, ',', '.') }}</span>
                                </div>
                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between text-green-600">
                                        <span>Diskon</span>
                                        <span>-Rp {{ number_format($order->discount_amount, 0, ',', '.') }}</span>
                                    </div>
                                @endif
                                <div class="border-t border-gray-200 pt-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-gray-900">Total Dibayar</span>
                                        <span class="text-lg font-bold text-green-600">
                                            Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Status -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                <div class="flex items-center text-green-800">
                                    <i data-lucide="check-circle" class="w-5 h-5 mr-2"></i>
                                    <div>
                                        <div class="font-semibold">Pembayaran Berhasil</div>
                                        <div class="text-sm">{{ $order->paid_at->format('d M Y, H:i') }} WIB</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Next Steps -->
                            <div class="space-y-3">
                                <h4 class="font-semibold text-gray-900">Langkah Selanjutnya:</h4>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-primary rounded-full"></div>
                                        <span>Simpan tiket di perangkat Anda</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-primary rounded-full"></div>
                                        <span>Cek email untuk konfirmasi</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-primary rounded-full"></div>
                                        <span>Siapkan identitas diri</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-primary rounded-full"></div>
                                        <span>Datang tepat waktu</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
function downloadTicket() {
    // Redirect to ticket download
    window.location.href = '{{ route("tickets.my-tickets") }}';
}

function shareEvent() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $order->event->title }}',
            text: 'Saya akan menghadiri {{ $order->event->title }}. Yuk ikutan!',
            url: '{{ route("tickets.show", $order->event->slug) }}'
        });
    } else {
        // Fallback to copy link
        const url = '{{ route("tickets.show", $order->event->slug) }}';
        navigator.clipboard.writeText(url).then(() => {
            showNotification('Link event berhasil disalin!', 'success');
        });
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info'}" class="w-5 h-5"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Success notification and sound on page load
document.addEventListener('DOMContentLoaded', function() {
    // Play success sound
    playSuccessSound();

    // Show success notification
    showNotification('🎉 Pembayaran berhasil! Tiket Anda sudah aktif dan siap digunakan.', 'success');

    // Simple confetti effect
    setTimeout(() => {
        for (let i = 0; i < 100; i++) {
            createConfetti();
        }
    }, 500);

    // Additional confetti burst
    setTimeout(() => {
        for (let i = 0; i < 50; i++) {
            createConfetti();
        }
    }, 1500);
});

function playSuccessSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Play a sequence of success sounds
        const frequencies = [523, 659, 784, 1047]; // C, E, G, C (major chord)

        frequencies.forEach((freq, index) => {
            setTimeout(() => {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            }, index * 150);
        });

        // Final celebration sound
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(1047, audioContext.currentTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        }, 800);

    } catch (error) {
        console.error('Error playing success sound:', error);
    }
}

function createConfetti() {
    const confetti = document.createElement('div');
    const colors = ['#A8D5BA', '#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0', '#FFC107'];
    const color = colors[Math.floor(Math.random() * colors.length)];
    const size = Math.random() * 6 + 4; // 4-10px

    confetti.style.cssText = `
        position: fixed;
        width: ${size}px;
        height: ${size}px;
        background: ${color};
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        left: ${Math.random() * 100}vw;
        top: -10px;
        animation: confetti-fall ${Math.random() * 3 + 2}s linear forwards;
        opacity: 0.8;
    `;

    document.body.appendChild(confetti);

    setTimeout(() => {
        if (document.body.contains(confetti)) {
            document.body.removeChild(confetti);
        }
    }, 5000);
}

// Add CSS for confetti animation
const style = document.createElement('style');
style.textContent = `
    @keyframes confetti-fall {
        0% {
            transform: translateY(-10px) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(720deg);
            opacity: 0;
        }
    }

    .success-animation {
        animation: bounce 0.6s ease-in-out;
    }

    @keyframes bounce {
        0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0,0,0);
        }
        40%, 43% {
            transform: translate3d(0,-30px,0);
        }
        70% {
            transform: translate3d(0,-15px,0);
        }
        90% {
            transform: translate3d(0,-4px,0);
        }
    }
`;
document.head.appendChild(style);
</script>
@endpush
