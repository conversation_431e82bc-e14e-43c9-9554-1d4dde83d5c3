<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckImageExtensions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'image:check-extensions';

    /**
     * The console command description.
     */
    protected $description = 'Check available image processing extensions';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Checking Image Processing Extensions...');
        $this->newLine();

        // Check GD Extension
        $this->checkGdExtension();
        $this->newLine();

        // Check Imagick Extension
        $this->checkImagickExtension();
        $this->newLine();

        // Check current configuration
        $this->checkCurrentConfig();
        $this->newLine();

        // Provide recommendations
        $this->provideRecommendations();

        return Command::SUCCESS;
    }

    /**
     * Check GD extension
     */
    private function checkGdExtension(): void
    {
        $this->info('📊 GD Extension Status:');
        
        if (extension_loaded('gd')) {
            $this->line('  ✅ GD Extension: <fg=green>AVAILABLE</fg=green>');
            
            $gdInfo = gd_info();
            $this->line('  📋 GD Version: ' . ($gdInfo['GD Version'] ?? 'Unknown'));
            
            // Check supported formats
            $formats = [];
            if ($gdInfo['JPEG Support'] ?? false) $formats[] = 'JPEG';
            if ($gdInfo['PNG Support'] ?? false) $formats[] = 'PNG';
            if ($gdInfo['GIF Read Support'] ?? false) $formats[] = 'GIF';
            if ($gdInfo['WebP Support'] ?? false) $formats[] = 'WebP';
            
            $this->line('  🎨 Supported Formats: ' . implode(', ', $formats));
            
            // Check memory limit
            $memoryLimit = ini_get('memory_limit');
            $this->line('  💾 PHP Memory Limit: ' . $memoryLimit);
            
        } else {
            $this->line('  ❌ GD Extension: <fg=red>NOT AVAILABLE</fg=red>');
            $this->warn('  ⚠️  GD extension is required for image processing');
        }
    }

    /**
     * Check Imagick extension
     */
    private function checkImagickExtension(): void
    {
        $this->info('🎭 Imagick Extension Status:');
        
        if (extension_loaded('imagick')) {
            $this->line('  ✅ Imagick Extension: <fg=green>AVAILABLE</fg=green>');
            
            if (class_exists('Imagick')) {
                $imagick = new \Imagick();
                $version = $imagick->getVersion();
                $this->line('  📋 Imagick Version: ' . ($version['versionString'] ?? 'Unknown'));
                
                // Check supported formats
                $formats = $imagick->queryFormats();
                $commonFormats = array_intersect($formats, ['JPEG', 'PNG', 'GIF', 'WEBP', 'SVG', 'PDF']);
                $this->line('  🎨 Common Formats: ' . implode(', ', $commonFormats));
                
                // Check resource limits
                $pixelLimit = $imagick->getResourceLimit(\Imagick::RESOURCETYPE_MEMORY);
                $this->line('  💾 Memory Resource Limit: ' . $this->formatBytes($pixelLimit));
            }
            
        } else {
            $this->line('  ❌ Imagick Extension: <fg=red>NOT AVAILABLE</fg=red>');
            $this->comment('  ℹ️  Imagick is optional but provides better performance and more features');
        }
    }

    /**
     * Check current configuration
     */
    private function checkCurrentConfig(): void
    {
        $this->info('⚙️ Current Configuration:');
        
        $driver = config('image.driver', 'gd');
        $this->line('  🔧 Configured Driver: <fg=yellow>' . strtoupper($driver) . '</fg=yellow>');
        
        $envDriver = env('IMAGE_DRIVER', 'gd');
        $this->line('  📄 .env Driver Setting: ' . strtoupper($envDriver));
        
        // Check if configured driver is available
        if ($driver === 'imagick' && !extension_loaded('imagick')) {
            $this->error('  ⚠️  WARNING: Imagick driver configured but extension not available!');
            $this->line('  💡 Will automatically fallback to GD driver');
        } elseif ($driver === 'gd' && !extension_loaded('gd')) {
            $this->error('  ❌ ERROR: GD driver configured but extension not available!');
        } else {
            $this->line('  ✅ Driver Configuration: <fg=green>VALID</fg=green>');
        }
        
        // Show image quality settings
        $jpegQuality = config('image.quality.jpeg', 85);
        $pngQuality = config('image.quality.png', 9);
        $webpQuality = config('image.quality.webp', 85);
        
        $this->line('  🎨 Quality Settings:');
        $this->line('    • JPEG: ' . $jpegQuality . '%');
        $this->line('    • PNG: ' . $pngQuality . ' (0-9)');
        $this->line('    • WebP: ' . $webpQuality . '%');
    }

    /**
     * Provide recommendations
     */
    private function provideRecommendations(): void
    {
        $this->info('💡 Recommendations:');
        
        $hasGd = extension_loaded('gd');
        $hasImagick = extension_loaded('imagick');
        $currentDriver = config('image.driver', 'gd');
        
        if (!$hasGd && !$hasImagick) {
            $this->error('  ❌ CRITICAL: No image processing extensions available!');
            $this->line('  📝 Install GD extension: sudo apt-get install php-gd (Ubuntu/Debian)');
            $this->line('  📝 Or install Imagick: sudo apt-get install php-imagick');
            $this->line('  📝 Then restart your web server');
            
        } elseif ($hasGd && !$hasImagick) {
            $this->line('  ✅ GD is available and sufficient for basic image processing');
            if ($currentDriver !== 'gd') {
                $this->line('  💡 Consider setting IMAGE_DRIVER=gd in .env file');
            }
            $this->line('  📈 For better performance, consider installing Imagick extension');
            
        } elseif (!$hasGd && $hasImagick) {
            $this->line('  ✅ Imagick is available and provides excellent image processing');
            if ($currentDriver !== 'imagick') {
                $this->line('  💡 Consider setting IMAGE_DRIVER=imagick in .env file');
            }
            
        } else {
            $this->line('  🎉 Both GD and Imagick are available!');
            $this->line('  💡 Imagick generally provides better performance and more features');
            $this->line('  💡 GD is more widely supported and easier to install');
            $this->line('  ⚙️  Current driver: ' . strtoupper($currentDriver));
        }
        
        $this->newLine();
        $this->line('📚 For more information, see: docs/IMAGE_PROCESSING.md');
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) return '0 B';
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor(log($bytes, 1024));
        
        return sprintf('%.2f %s', $bytes / pow(1024, $factor), $units[$factor]);
    }
}
