@extends('layouts.admin')

@section('title', 'Manajemen Pengguna')

@push('styles')
<style>
/* Custom styles for User Management */
.user-card {
    transition: all 0.3s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.role-badge {
    transition: all 0.2s ease;
}

.bulk-actions-bar {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.bulk-actions-bar.show {
    transform: translateY(0);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .filter-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .user-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="users" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Manajemen Pengguna</h1>
                            <p class="text-gray-600 dark:text-gray-400">Kelola semua pengguna platform TiXara</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="{{ route('admin.users.create') }}"
                       class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 shadow-sm">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                        Tambah Pengguna
                    </a>
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-sm">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Export
                            <i data-lucide="chevron-down" class="w-4 h-4 ml-2"></i>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                            <div class="py-1">
                                <a href="#" onclick="exportUsers('csv')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Export CSV
                                </a>
                                <a href="#" onclick="exportUsers('excel')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-spreadsheet" class="w-4 h-4 mr-3"></i>
                                    Export Excel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stats-grid">
            <!-- Total Users -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 user-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Pengguna</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_users']) }}
                        </p>
                        <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            <i data-lucide="trending-up" class="w-3 h-3 inline mr-1"></i>
                            +5% dari bulan lalu
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>

            <!-- Active Users -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 user-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Pengguna Aktif</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['active_users']) }}
                        </p>
                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                            <i data-lucide="check-circle" class="w-3 h-3 inline mr-1"></i>
                            Status aktif
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="user-check" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Verified Users -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 user-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Email Terverifikasi</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['verified_users']) }}
                        </p>
                        <p class="text-xs text-purple-600 dark:text-purple-400 mt-1">
                            <i data-lucide="mail-check" class="w-3 h-3 inline mr-1"></i>
                            Email verified
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="mail-check" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
            </div>

            <!-- Organizers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 user-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Organizer</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['organizer_count']) }}
                        </p>
                        <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">
                            <i data-lucide="briefcase" class="w-3 h-3 inline mr-1"></i>
                            Event organizers
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="briefcase" class="w-6 h-6 text-orange-600 dark:text-orange-400"></i>
                    </div>
                </div>
            </div>
        </div>

    <!-- Filters and Search -->
    <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6 mb-6" data-aos="fade-up" data-aos-delay="400">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cari Pengguna</label>
                <input type="text" id="search" name="search" value="{{ request('search') }}"
                       placeholder="Nama, email, atau telepon..."
                       class="w-full px-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100">
            </div>

            <!-- Role Filter -->
            <div>
                <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Role</label>
                <select id="role" name="role" class="w-full px-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100">
                    <option value="">Semua Role</option>
                    <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                    <option value="staff" {{ request('role') === 'staff' ? 'selected' : '' }}>Staff</option>
                    <option value="penjual" {{ request('role') === 'penjual' ? 'selected' : '' }}>Organizer</option>
                    <option value="pembeli" {{ request('role') === 'pembeli' ? 'selected' : '' }}>Pembeli</option>
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <select id="status" name="status" class="w-full px-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100">
                    <option value="">Semua Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Aktif</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Tidak Aktif</option>
                    <option value="verified" {{ request('status') === 'verified' ? 'selected' : '' }}>Terverifikasi</option>
                    <option value="unverified" {{ request('status') === 'unverified' ? 'selected' : '' }}>Belum Verifikasi</option>
                </select>
            </div>

            <!-- Sort -->
            <div>
                <label for="sort" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Urutkan</label>
                <select id="sort" name="sort" class="w-full px-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100">
                    <option value="latest" {{ request('sort') === 'latest' ? 'selected' : '' }}>Terbaru</option>
                    <option value="oldest" {{ request('sort') === 'oldest' ? 'selected' : '' }}>Terlama</option>
                    <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Nama A-Z</option>
                    <option value="email" {{ request('sort') === 'email' ? 'selected' : '' }}>Email A-Z</option>
                    <option value="role" {{ request('sort') === 'role' ? 'selected' : '' }}>Role</option>
                </select>
            </div>

            <!-- Filter Buttons -->
            <div class="md:col-span-4 flex justify-end space-x-3">
                <a href="{{ route('admin.users.index') }}"
                   class="px-4 py-2 border border-gray-300 dark:border-dark-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors">
                    Reset
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                    Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    <div class="bg-white dark:bg-dark-800 rounded-xl shadow-lg overflow-hidden" data-aos="fade-up" data-aos-delay="600">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-dark-600">
                <thead class="bg-gray-50 dark:bg-dark-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Pengguna
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Role
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Event
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Bergabung
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Aksi
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-dark-600">
                    @forelse($users as $user)
                    <tr class="hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" name="user_ids[]" value="{{ $user->id }}" class="user-checkbox rounded border-gray-300 text-primary focus:ring-primary">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-4">
                                <img src="{{ $user->profile_photo_url }}"
                                     alt="{{ $user->name }}"
                                     class="w-10 h-10 rounded-full object-cover ring-2 ring-primary/20">
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ $user->name }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $user->email }}</div>
                                    @if($user->phone)
                                        <div class="text-xs text-gray-400 dark:text-gray-500">{{ $user->phone }}</div>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                @if($user->role === 'admin') bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                @elseif($user->role === 'staff') bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
                                @elseif($user->role === 'penjual') bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400
                                @else bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 @endif">
                                {{ ucfirst($user->role) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex flex-col space-y-1">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    @if($user->is_active) bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                    @else bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 @endif">
                                    {{ $user->is_active ? 'Aktif' : 'Tidak Aktif' }}
                                </span>
                                @if($user->email_verified_at)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                                        Terverifikasi
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                        Belum Verifikasi
                                    </span>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {{ $user->organized_events_count }} event
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ $user->created_at->format('d M Y') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                                <a href="{{ route('admin.users.show', $user) }}"
                                   class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                                    Lihat
                                </a>
                                <a href="{{ route('admin.users.edit', $user) }}"
                                   class="text-primary hover:text-primary/80 transition-colors">
                                    Edit
                                </a>
                                @if($user->id !== auth()->id())
                                    <form action="{{ route('admin.users.destroy', $user) }}" method="POST" class="inline"
                                          onsubmit="return confirm('Apakah Anda yakin ingin menghapus pengguna ini?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors">
                                            Hapus
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center space-y-3">
                                <svg class="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                                <p class="text-gray-500 dark:text-gray-400">Tidak ada pengguna yang ditemukan</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($users->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-dark-600">
                {{ $users->links() }}
            </div>
        @endif
    </div>

    <!-- Bulk Actions -->
    <div id="bulk-actions" class="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white dark:bg-dark-800 rounded-xl shadow-lg border border-gray-200 dark:border-dark-600 p-4 hidden">
        <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600 dark:text-gray-400">
                <span id="selected-count">0</span> pengguna dipilih
            </span>
            <div class="flex space-x-2">
                <button onclick="bulkAction('activate')" class="px-3 py-1 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors text-sm">
                    Aktifkan
                </button>
                <button onclick="bulkAction('deactivate')" class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-lg hover:bg-yellow-200 transition-colors text-sm">
                    Nonaktifkan
                </button>
                <button onclick="bulkAction('verify')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors text-sm">
                    Verifikasi
                </button>
                <button onclick="bulkAction('delete')" class="px-3 py-1 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors text-sm">
                    Hapus
                </button>
            </div>
        </div>
    </div>

    <!-- Bulk Action Form -->
    <form id="bulk-action-form" action="{{ route('admin.users.bulk-action') }}" method="POST" style="display: none;">
        @csrf
        <input type="hidden" name="action" id="bulk-action-input">
        <div id="bulk-user-ids"></div>
    </form>
</div>

@push('scripts')
<script>
// Alpine.js data for user management
function userManagement() {
    return {
        selectedUsers: [],
        selectAll: false,

        toggleSelectAll() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.selectAll;
            });
            this.updateSelectedUsers();
        },

        updateSelectedUsers() {
            this.selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked'))
                .map(cb => cb.value);

            const totalCheckboxes = document.querySelectorAll('.user-checkbox').length;
            this.selectAll = this.selectedUsers.length === totalCheckboxes;

            // Update bulk actions visibility
            const bulkActions = document.getElementById('bulk-actions');
            if (this.selectedUsers.length > 0) {
                bulkActions.classList.remove('hidden');
                bulkActions.classList.add('show');
            } else {
                bulkActions.classList.add('hidden');
                bulkActions.classList.remove('show');
            }

            // Update count
            document.getElementById('selected-count').textContent = this.selectedUsers.length;
        }
    }
}

// Export functions
function exportUsers(format = 'csv') {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);

    const exportUrl = `{{ route('admin.users.export') }}?${params.toString()}`;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `users_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('Export started', 'Your user data export is being prepared.', 'success');
}

// Bulk actions
function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');

    if (checkedBoxes.length === 0) {
        showNotification('No Selection', 'Pilih minimal satu pengguna', 'warning');
        return;
    }

    let confirmMessage = '';
    let actionText = '';

    switch (action) {
        case 'delete':
            confirmMessage = 'Apakah Anda yakin ingin menghapus pengguna yang dipilih?';
            actionText = 'Menghapus';
            break;
        case 'activate':
            confirmMessage = 'Apakah Anda yakin ingin mengaktifkan pengguna yang dipilih?';
            actionText = 'Mengaktifkan';
            break;
        case 'deactivate':
            confirmMessage = 'Apakah Anda yakin ingin menonaktifkan pengguna yang dipilih?';
            actionText = 'Menonaktifkan';
            break;
        case 'verify':
            confirmMessage = 'Apakah Anda yakin ingin memverifikasi email pengguna yang dipilih?';
            actionText = 'Memverifikasi';
            break;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    showNotification('Processing', `${actionText} ${checkedBoxes.length} pengguna...`, 'info');

    // Prepare form
    const form = document.getElementById('bulk-action-form');
    const actionInput = document.getElementById('bulk-action-input');
    const userIdsContainer = document.getElementById('bulk-user-ids');

    actionInput.value = action;
    userIdsContainer.innerHTML = '';

    checkedBoxes.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids[]';
        input.value = checkbox.value;
        userIdsContainer.appendChild(input);
    });

    form.submit();
}

// User quick actions
function quickAction(userId, action) {
    let confirmMessage = '';
    let actionText = '';

    switch (action) {
        case 'activate':
            confirmMessage = 'Aktifkan pengguna ini?';
            actionText = 'Mengaktifkan';
            break;
        case 'deactivate':
            confirmMessage = 'Nonaktifkan pengguna ini?';
            actionText = 'Menonaktifkan';
            break;
        case 'verify':
            confirmMessage = 'Verifikasi email pengguna ini?';
            actionText = 'Memverifikasi';
            break;
        case 'delete':
            confirmMessage = 'Hapus pengguna ini? Tindakan ini tidak dapat dibatalkan.';
            actionText = 'Menghapus';
            break;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    showNotification('Processing', `${actionText} pengguna...`, 'info');

    // Create and submit form
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/users/${userId}/${action}`;

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    if (action === 'delete') {
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);
    }

    document.body.appendChild(form);
    form.submit();
}

// Notification system
function showNotification(title, message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

    const colors = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-white',
        info: 'bg-blue-500 text-white'
    };

    notification.className += ` ${colors[type] || colors.info}`;

    notification.innerHTML = `
        <div class="flex items-start">
            <div class="flex-1">
                <h4 class="font-medium">${title}</h4>
                <p class="text-sm opacity-90">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Setup checkbox event listeners
    const selectAllCheckbox = document.getElementById('select-all');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsVisibility();
        });
    }

    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionsVisibility);
    });

    function updateBulkActionsVisibility() {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');

        if (selectedCount) {
            selectedCount.textContent = checkedBoxes.length;
        }

        if (bulkActions) {
            if (checkedBoxes.length > 0) {
                bulkActions.classList.remove('hidden');
                bulkActions.classList.add('show');
            } else {
                bulkActions.classList.add('hidden');
                bulkActions.classList.remove('show');
            }
        }

        // Update select all state
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedBoxes.length === userCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < userCheckboxes.length;
        }
    }
});
</script>
@endpush
@endsection
