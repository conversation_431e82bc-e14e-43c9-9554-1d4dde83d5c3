<?php

/**
 * Feedback Model
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: <PERSON><PERSON><PERSON>zula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Feedback extends Model
{
    use HasFactory;

    protected $fillable = [
        'feedback_number',
        'user_id',
        'type',
        'category',
        'title',
        'message',
        'rating',
        'contact_email',
        'contact_phone',
        'is_anonymous',
        'status',
        'ip_address',
        'user_agent',
        'admin_response',
        'responded_at',
        'responded_by',
    ];

    protected $casts = [
        'is_anonymous' => 'boolean',
        'responded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who submitted the feedback
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin who responded to the feedback
     */
    public function responder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'responded_by');
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            'suggestion' => 'Saran',
            'complaint' => 'Keluhan',
            'compliment' => 'Pujian',
            'feature_request' => 'Permintaan Fitur',
            'bug_report' => 'Laporan Bug',
            'other' => 'Lainnya',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get category label
     */
    public function getCategoryLabelAttribute(): string
    {
        return match($this->category) {
            'ui_ux' => 'UI/UX',
            'performance' => 'Performa',
            'feature' => 'Fitur',
            'content' => 'Konten',
            'service' => 'Layanan',
            'technical' => 'Teknis',
            'other' => 'Lainnya',
            default => ucfirst($this->category),
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Menunggu',
            'reviewed' => 'Ditinjau',
            'responded' => 'Direspon',
            'implemented' => 'Diimplementasi',
            'rejected' => 'Ditolak',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get type color
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'suggestion' => 'primary',
            'complaint' => 'warning',
            'compliment' => 'success',
            'feature_request' => 'info',
            'bug_report' => 'danger',
            'other' => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'reviewed' => 'info',
            'responded' => 'primary',
            'implemented' => 'success',
            'rejected' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get rating stars
     */
    public function getRatingStarsAttribute(): string
    {
        if (!$this->rating) {
            return '';
        }

        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $stars .= '★';
            } else {
                $stars .= '☆';
            }
        }

        return $stars;
    }

    /**
     * Check if feedback has response
     */
    public function hasResponse(): bool
    {
        return !empty($this->admin_response);
    }

    /**
     * Check if feedback is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if feedback is responded
     */
    public function isResponded(): bool
    {
        return in_array($this->status, ['responded', 'implemented']);
    }

    /**
     * Scope for pending feedback
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for responded feedback
     */
    public function scopeResponded($query)
    {
        return $query->whereIn('status', ['responded', 'implemented']);
    }

    /**
     * Scope for feedback by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for feedback by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for feedback with rating
     */
    public function scopeWithRating($query)
    {
        return $query->whereNotNull('rating');
    }

    /**
     * Scope for anonymous feedback
     */
    public function scopeAnonymous($query)
    {
        return $query->where('is_anonymous', true);
    }
}
