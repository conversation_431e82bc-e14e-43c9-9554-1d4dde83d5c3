<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'iconColor' => 'blue',
    'padding' => 'p-6',
    'hover' => true,
    'border' => true,
    'shadow' => 'shadow-sm',
    'rounded' => 'rounded-xl',
    'background' => 'bg-white dark:bg-gray-800',
    'headerClass' => '',
    'bodyClass' => '',
    'footerClass' => '',
    'aos' => null,
    'aosDelay' => null
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'iconColor' => 'blue',
    'padding' => 'p-6',
    'hover' => true,
    'border' => true,
    'shadow' => 'shadow-sm',
    'rounded' => 'rounded-xl',
    'background' => 'bg-white dark:bg-gray-800',
    'headerClass' => '',
    'bodyClass' => '',
    'footerClass' => '',
    'aos' => null,
    'aosDelay' => null
]); ?>
<?php foreach (array_filter(([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'iconColor' => 'blue',
    'padding' => 'p-6',
    'hover' => true,
    'border' => true,
    'shadow' => 'shadow-sm',
    'rounded' => 'rounded-xl',
    'background' => 'bg-white dark:bg-gray-800',
    'headerClass' => '',
    'bodyClass' => '',
    'footerClass' => '',
    'aos' => null,
    'aosDelay' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    $iconColors = [
        'blue' => 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
        'green' => 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
        'yellow' => 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400',
        'red' => 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
        'purple' => 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400',
        'orange' => 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400',
        'pink' => 'bg-pink-100 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400',
        'indigo' => 'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400',
        'gray' => 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400',
    ];
    
    $classes = collect([
        $background,
        $rounded,
        $shadow,
        $padding,
        $border ? 'border border-gray-200 dark:border-gray-700' : '',
        $hover ? 'hover-lift' : '',
        'transition-all duration-200'
    ])->filter()->implode(' ');
    
    $aosAttributes = '';
    if ($aos) {
        $aosAttributes .= ' data-aos="' . $aos . '"';
        if ($aosDelay) {
            $aosAttributes .= ' data-aos-delay="' . $aosDelay . '"';
        }
    }
?>

<div <?php echo e($attributes->merge(['class' => $classes])); ?><?php echo $aosAttributes; ?>>
    <?php if($title || $subtitle || $icon || isset($header)): ?>
        <div class="flex items-center justify-between mb-6 <?php echo e($headerClass); ?>">
            <div class="flex items-center space-x-3">
                <?php if($icon): ?>
                    <div class="w-12 h-12 <?php echo e($iconColors[$iconColor] ?? $iconColors['blue']); ?> rounded-lg flex items-center justify-center">
                        <i data-lucide="<?php echo e($icon); ?>" class="w-6 h-6"></i>
                    </div>
                <?php endif; ?>
                
                <?php if($title || $subtitle): ?>
                    <div>
                        <?php if($title): ?>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($title); ?></h3>
                        <?php endif; ?>
                        <?php if($subtitle): ?>
                            <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($subtitle); ?></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if(isset($header)): ?>
                <?php echo e($header); ?>

            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if(isset($body) || $slot->isNotEmpty()): ?>
        <div class="<?php echo e($bodyClass); ?>">
            <?php if(isset($body)): ?>
                <?php echo e($body); ?>

            <?php else: ?>
                <?php echo e($slot); ?>

            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if(isset($footer)): ?>
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 <?php echo e($footerClass); ?>">
            <?php echo e($footer); ?>

        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/components/modern-card.blade.php ENDPATH**/ ?>