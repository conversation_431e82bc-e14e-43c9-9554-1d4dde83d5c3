<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

class HandleMissingImages
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Check if this is a 404 response for a storage image
        if ($response->getStatusCode() === 404 && $this->isStorageImageRequest($request)) {
            return $this->generatePlaceholderImage($request);
        }

        return $response;
    }

    /**
     * Check if the request is for a storage image
     */
    private function isStorageImageRequest(Request $request): bool
    {
        $path = $request->path();

        return str_starts_with($path, 'storage/') &&
               preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $path);
    }

    /**
     * Generate a placeholder image response
     */
    private function generatePlaceholderImage(Request $request): Response
    {
        $path = $request->path();

        // Extract image type from path
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        $mimeType = $this->getMimeType($extension);

        // Determine image type and generate appropriate placeholder
        if (str_contains($path, 'poster')) {
            $placeholderUrl = "https://via.placeholder.com/600x400/A8D5BA/FFFFFF?text=Missing+Poster";
        } elseif (str_contains($path, 'gallery')) {
            $placeholderUrl = "https://via.placeholder.com/800x600/A8D5BA/FFFFFF?text=Missing+Gallery+Image";
        } elseif (str_contains($path, 'avatar')) {
            $placeholderUrl = "https://via.placeholder.com/200x200/A8D5BA/FFFFFF?text=Missing+Avatar";
        } else {
            $placeholderUrl = "https://via.placeholder.com/400x400/A8D5BA/FFFFFF?text=Missing+Image";
        }

        // Redirect to placeholder image
        return redirect($placeholderUrl);
    }

    /**
     * Get MIME type from file extension
     */
    private function getMimeType(string $extension): string
    {
        return match(strtolower($extension)) {
            'jpg', 'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            default => 'image/jpeg',
        };
    }
}
