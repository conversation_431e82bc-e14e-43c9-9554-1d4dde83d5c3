<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserLevelController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display user level management page
     */
    public function index(Request $request)
    {
        $query = User::query()
            ->where('role', '!=', 'pembeli') // Only show penjual and admin
            ->with(['organizedEvents', 'orders']);

        // Filter by level
        if ($request->filled('level')) {
            $query->where('user_level', $request->level);
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('organization', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get level statistics
        $levelStats = User::where('role', '!=', 'pembeli')
            ->select('user_level', DB::raw('count(*) as count'))
            ->groupBy('user_level')
            ->pluck('count', 'user_level')
            ->toArray();

        $levels = config('user_levels.levels', []);

        return view('pages.admin.user-levels.index', compact('users', 'levelStats', 'levels'));
    }

    /**
     * Update user level
     */
    public function updateLevel(Request $request, User $user)
    {
        $request->validate([
            'user_level' => 'required|in:star,star_plus,premium,platinum'
        ]);

        $oldLevel = $user->user_level;
        $newLevel = $request->user_level;

        $user->update([
            'user_level' => $newLevel
        ]);

        // Log the level change
        activity()
            ->performedOn($user)
            ->causedBy(auth()->user())
            ->withProperties([
                'old_level' => $oldLevel,
                'new_level' => $newLevel
            ])
            ->log('User level updated');

        return response()->json([
            'success' => true,
            'message' => "Level pengguna berhasil diubah dari {$oldLevel} ke {$newLevel}",
            'user' => [
                'id' => $user->id,
                'level' => $newLevel,
                'level_display' => $user->getLevelDisplayName(),
                'level_color' => $user->getLevelColor()
            ]
        ]);
    }

    /**
     * Bulk update user levels
     */
    public function bulkUpdateLevel(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'user_level' => 'required|in:star,star_plus,premium,platinum'
        ]);

        $userIds = $request->user_ids;
        $newLevel = $request->user_level;

        $users = User::whereIn('id', $userIds)->get();
        
        foreach ($users as $user) {
            $oldLevel = $user->user_level;
            $user->update(['user_level' => $newLevel]);

            // Log the level change
            activity()
                ->performedOn($user)
                ->causedBy(auth()->user())
                ->withProperties([
                    'old_level' => $oldLevel,
                    'new_level' => $newLevel,
                    'bulk_update' => true
                ])
                ->log('User level updated (bulk)');
        }

        return response()->json([
            'success' => true,
            'message' => count($userIds) . " pengguna berhasil diubah levelnya ke {$newLevel}",
            'updated_count' => count($userIds)
        ]);
    }

    /**
     * Get level requirements and benefits
     */
    public function getLevelInfo($level)
    {
        $levelConfig = config("user_levels.levels.{$level}");
        
        if (!$levelConfig) {
            return response()->json(['error' => 'Level tidak ditemukan'], 404);
        }

        return response()->json([
            'level' => $level,
            'config' => $levelConfig
        ]);
    }

    /**
     * Auto-upgrade users based on performance
     */
    public function autoUpgrade(Request $request)
    {
        $upgradedUsers = [];
        $levels = ['star_plus', 'premium', 'platinum'];

        $users = User::where('role', '!=', 'pembeli')
            ->with(['organizedEvents', 'orders'])
            ->get();

        foreach ($users as $user) {
            $currentLevel = $user->user_level;
            $currentLevelIndex = array_search($currentLevel, array_keys(config('user_levels.levels')));
            
            // Check if user qualifies for higher levels
            foreach ($levels as $targetLevel) {
                $targetLevelIndex = array_search($targetLevel, array_keys(config('user_levels.levels')));
                
                // Only check levels higher than current
                if ($targetLevelIndex > $currentLevelIndex && $user->qualifiesForUpgrade($targetLevel)) {
                    $user->update(['user_level' => $targetLevel]);
                    
                    $upgradedUsers[] = [
                        'user' => $user,
                        'old_level' => $currentLevel,
                        'new_level' => $targetLevel
                    ];

                    // Log the auto-upgrade
                    activity()
                        ->performedOn($user)
                        ->causedBy(auth()->user())
                        ->withProperties([
                            'old_level' => $currentLevel,
                            'new_level' => $targetLevel,
                            'auto_upgrade' => true
                        ])
                        ->log('User level auto-upgraded');

                    break; // Stop at first qualifying level
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($upgradedUsers) . ' pengguna berhasil di-upgrade otomatis',
            'upgraded_users' => $upgradedUsers
        ]);
    }

    /**
     * Export user levels data
     */
    public function export(Request $request)
    {
        $users = User::where('role', '!=', 'pembeli')
            ->with(['organizedEvents', 'orders'])
            ->get();

        $data = $users->map(function($user) {
            return [
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'level' => $user->user_level,
                'level_display' => $user->getLevelDisplayName(),
                'organization' => $user->organization,
                'total_events' => $user->organizedEvents->count(),
                'total_orders' => $user->orders->count(),
                'created_at' => $user->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data,
            'filename' => 'user_levels_' . now()->format('Y-m-d_H-i-s') . '.json'
        ]);
    }
}
