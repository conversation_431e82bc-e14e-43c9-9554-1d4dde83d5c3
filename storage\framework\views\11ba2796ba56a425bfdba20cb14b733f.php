<?php $__env->startSection('title', 'Analytics'); ?>
<?php $__env->startSection('subtitle', 'Platform performance insights and metrics'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header with Controls -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Analytics Dashboard</h1>
            <p class="text-gray-600 dark:text-gray-400">Comprehensive platform performance metrics</p>
        </div>
        
        <div class="mt-4 md:mt-0 flex items-center space-x-3">
            <!-- Date Range Selector -->
            <select id="dateRange" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                <option value="7" <?php echo e($dateRange == 7 ? 'selected' : ''); ?>>Last 7 Days</option>
                <option value="30" <?php echo e($dateRange == 30 ? 'selected' : ''); ?>>Last 30 Days</option>
                <option value="90" <?php echo e($dateRange == 90 ? 'selected' : ''); ?>>Last 90 Days</option>
                <option value="365" <?php echo e($dateRange == 365 ? 'selected' : ''); ?>>Last Year</option>
            </select>
            
            <!-- Export Button -->
            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['variant' => 'outline','icon' => 'download','onclick' => 'exportAnalytics()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'outline','icon' => 'download','onclick' => 'exportAnalytics()']); ?>
                Export
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <?php if (isset($component)) { $__componentOriginal8f216e051c231b98198765acd723fb77 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8f216e051c231b98198765acd723fb77 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.stats-card','data' => ['title' => 'Total Revenue','value' => 'Rp ' . number_format($revenueData['total_revenue'] ?? 0, 0, ',', '.'),'icon' => 'dollar-sign','iconColor' => 'green','change' => ($revenueData['growth_rate'] ?? 0) . '%','changeType' => ($revenueData['growth_rate'] ?? 0) >= 0 ? 'positive' : 'negative','changeLabel' => 'vs last period','trend' => ($revenueData['growth_rate'] ?? 0) >= 0 ? 'up' : 'down','aos' => 'fade-up','aosDelay' => '100']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('stats-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Total Revenue','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('Rp ' . number_format($revenueData['total_revenue'] ?? 0, 0, ',', '.')),'icon' => 'dollar-sign','icon-color' => 'green','change' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(($revenueData['growth_rate'] ?? 0) . '%'),'change-type' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(($revenueData['growth_rate'] ?? 0) >= 0 ? 'positive' : 'negative'),'change-label' => 'vs last period','trend' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(($revenueData['growth_rate'] ?? 0) >= 0 ? 'up' : 'down'),'aos' => 'fade-up','aos-delay' => '100']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $attributes = $__attributesOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__attributesOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $component = $__componentOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__componentOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
        
        <?php if (isset($component)) { $__componentOriginal8f216e051c231b98198765acd723fb77 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8f216e051c231b98198765acd723fb77 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.stats-card','data' => ['title' => 'Total Users','value' => number_format($userAnalytics['total_users'] ?? 0, 0, ',', '.'),'icon' => 'users','iconColor' => 'blue','change' => '+' . number_format($userAnalytics['total_users'] ?? 0, 0, ',', '.'),'changeType' => 'positive','changeLabel' => 'new users','trend' => 'up','aos' => 'fade-up','aosDelay' => '200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('stats-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Total Users','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(number_format($userAnalytics['total_users'] ?? 0, 0, ',', '.')),'icon' => 'users','icon-color' => 'blue','change' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('+' . number_format($userAnalytics['total_users'] ?? 0, 0, ',', '.')),'change-type' => 'positive','change-label' => 'new users','trend' => 'up','aos' => 'fade-up','aos-delay' => '200']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $attributes = $__attributesOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__attributesOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $component = $__componentOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__componentOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
        
        <?php if (isset($component)) { $__componentOriginal8f216e051c231b98198765acd723fb77 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8f216e051c231b98198765acd723fb77 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.stats-card','data' => ['title' => 'Total Events','value' => number_format($eventAnalytics['total_events'] ?? 0, 0, ',', '.'),'icon' => 'calendar','iconColor' => 'purple','change' => number_format($eventAnalytics['published_events'] ?? 0, 0, ',', '.') . ' published','changeType' => 'neutral','aos' => 'fade-up','aosDelay' => '300']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('stats-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Total Events','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(number_format($eventAnalytics['total_events'] ?? 0, 0, ',', '.')),'icon' => 'calendar','icon-color' => 'purple','change' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(number_format($eventAnalytics['published_events'] ?? 0, 0, ',', '.') . ' published'),'change-type' => 'neutral','aos' => 'fade-up','aos-delay' => '300']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $attributes = $__attributesOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__attributesOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $component = $__componentOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__componentOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
        
        <?php if (isset($component)) { $__componentOriginal8f216e051c231b98198765acd723fb77 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8f216e051c231b98198765acd723fb77 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.stats-card','data' => ['title' => 'Tickets Sold','value' => number_format($eventAnalytics['total_tickets_sold'] ?? 0, 0, ',', '.'),'icon' => 'ticket','iconColor' => 'orange','aos' => 'fade-up','aosDelay' => '400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('stats-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Tickets Sold','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(number_format($eventAnalytics['total_tickets_sold'] ?? 0, 0, ',', '.')),'icon' => 'ticket','icon-color' => 'orange','aos' => 'fade-up','aos-delay' => '400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $attributes = $__attributesOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__attributesOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8f216e051c231b98198765acd723fb77)): ?>
<?php $component = $__componentOriginal8f216e051c231b98198765acd723fb77; ?>
<?php unset($__componentOriginal8f216e051c231b98198765acd723fb77); ?>
<?php endif; ?>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Chart -->
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['title' => 'Revenue Trends','icon' => 'trending-up','iconColor' => 'green','aos' => 'fade-up','aosDelay' => '500']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Revenue Trends','icon' => 'trending-up','icon-color' => 'green','aos' => 'fade-up','aos-delay' => '500']); ?>
            <div class="h-80">
                <canvas id="revenueChart"></canvas>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>

        <!-- User Growth Chart -->
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['title' => 'User Growth','icon' => 'users','iconColor' => 'blue','aos' => 'fade-up','aosDelay' => '600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'User Growth','icon' => 'users','icon-color' => 'blue','aos' => 'fade-up','aos-delay' => '600']); ?>
            <div class="h-80">
                <canvas id="userChart"></canvas>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
    </div>

    <!-- Additional Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Top Categories -->
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['title' => 'Top Categories','icon' => 'tag','iconColor' => 'purple','aos' => 'fade-up','aosDelay' => '700']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Top Categories','icon' => 'tag','icon-color' => 'purple','aos' => 'fade-up','aos-delay' => '700']); ?>
            <div class="space-y-4">
                <?php if(isset($categoryPerformance) && count($categoryPerformance) > 0): ?>
                    <?php $__currentLoopData = $categoryPerformance->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                                <span class="text-white text-sm font-bold"><?php echo e(substr($category->name ?? 'N/A', 0, 2)); ?></span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white"><?php echo e($category->name ?? 'Unknown'); ?></p>
                                <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($category->events_count ?? 0); ?> events</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white">Rp <?php echo e(number_format($category->total_revenue ?? 0, 0, ',', '.')); ?></p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <?php if (isset($component)) { $__componentOriginal074a021b9d42f490272b5eefda63257c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal074a021b9d42f490272b5eefda63257c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.empty-state','data' => ['icon' => 'tag','title' => 'No category data','description' => 'Category performance data will appear here','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('empty-state'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'tag','title' => 'No category data','description' => 'Category performance data will appear here','size' => 'sm']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal074a021b9d42f490272b5eefda63257c)): ?>
<?php $attributes = $__attributesOriginal074a021b9d42f490272b5eefda63257c; ?>
<?php unset($__attributesOriginal074a021b9d42f490272b5eefda63257c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal074a021b9d42f490272b5eefda63257c)): ?>
<?php $component = $__componentOriginal074a021b9d42f490272b5eefda63257c; ?>
<?php unset($__componentOriginal074a021b9d42f490272b5eefda63257c); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>

        <!-- Geographic Distribution -->
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['title' => 'Top Locations','icon' => 'map-pin','iconColor' => 'orange','aos' => 'fade-up','aosDelay' => '800']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Top Locations','icon' => 'map-pin','icon-color' => 'orange','aos' => 'fade-up','aos-delay' => '800']); ?>
            <div class="space-y-4">
                <?php if(isset($geographicData) && count($geographicData) > 0): ?>
                    <?php $__currentLoopData = $geographicData->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
                                <i data-lucide="map-pin" class="w-4 h-4 text-orange-600 dark:text-orange-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white"><?php echo e($location->location ?? 'Unknown'); ?></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white"><?php echo e($location->events_count ?? 0); ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">events</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <?php if (isset($component)) { $__componentOriginal074a021b9d42f490272b5eefda63257c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal074a021b9d42f490272b5eefda63257c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.empty-state','data' => ['icon' => 'map','title' => 'No location data','description' => 'Geographic data will appear here','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('empty-state'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'map','title' => 'No location data','description' => 'Geographic data will appear here','size' => 'sm']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal074a021b9d42f490272b5eefda63257c)): ?>
<?php $attributes = $__attributesOriginal074a021b9d42f490272b5eefda63257c; ?>
<?php unset($__attributesOriginal074a021b9d42f490272b5eefda63257c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal074a021b9d42f490272b5eefda63257c)): ?>
<?php $component = $__componentOriginal074a021b9d42f490272b5eefda63257c; ?>
<?php unset($__componentOriginal074a021b9d42f490272b5eefda63257c); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>

        <!-- Quick Stats -->
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['title' => 'Quick Stats','icon' => 'activity','iconColor' => 'indigo','aos' => 'fade-up','aosDelay' => '900']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Quick Stats','icon' => 'activity','icon-color' => 'indigo','aos' => 'fade-up','aos-delay' => '900']); ?>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Active Users</span>
                    <span class="font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($userAnalytics['active_users'] ?? 0, 0, ',', '.')); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Organizers</span>
                    <span class="font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($userAnalytics['total_organizers'] ?? 0, 0, ',', '.')); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Published Events</span>
                    <span class="font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($eventAnalytics['published_events'] ?? 0, 0, ',', '.')); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Avg Revenue/Event</span>
                    <span class="font-semibold text-gray-900 dark:text-white">
                        Rp <?php echo e(number_format(($eventAnalytics['total_events'] ?? 0) > 0 ? ($revenueData['total_revenue'] ?? 0) / ($eventAnalytics['total_events'] ?? 1) : 0, 0, ',', '.')); ?>

                    </span>
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($revenueData['daily_revenue']->pluck('date') ?? [], 15, 512) ?>,
            datasets: [{
                label: 'Revenue',
                data: <?php echo json_encode($revenueData['daily_revenue']->pluck('revenue') ?? [], 15, 512) ?>,
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // User Growth Chart
    const userCtx = document.getElementById('userChart').getContext('2d');
    const userChart = new Chart(userCtx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($userAnalytics['daily_registrations']->pluck('date') ?? [], 15, 512) ?>,
            datasets: [{
                label: 'New Users',
                data: <?php echo json_encode($userAnalytics['daily_registrations']->pluck('registrations') ?? [], 15, 512) ?>,
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Date range change handler
    document.getElementById('dateRange').addEventListener('change', function() {
        window.location.href = '<?php echo e(route("admin.analytics.index")); ?>?range=' + this.value;
    });
});

function exportAnalytics() {
    const range = document.getElementById('dateRange').value;
    window.location.href = '<?php echo e(route("admin.analytics.export")); ?>?range=' + range;
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/admin/analytics/index.blade.php ENDPATH**/ ?>