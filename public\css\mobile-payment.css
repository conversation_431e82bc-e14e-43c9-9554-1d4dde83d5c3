/* Mobile Payment Optimizations */

/* Touch-friendly buttons */
@media (max-width: 768px) {
    .payment-button,
    .copy-button,
    .status-check-button {
        min-height: 48px;
        font-size: 16px;
        padding: 12px 16px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    
    .payment-button:active,
    .copy-button:active,
    .status-check-button:active {
        transform: scale(0.98);
    }
}

/* QR Code responsive container */
.qr-container {
    position: relative;
    width: 100%;
    max-width: 280px;
    margin: 0 auto;
}

.qr-container canvas {
    width: 100% !important;
    height: auto !important;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Virtual Account number styling */
.va-number {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 1px;
    word-break: break-all;
    padding: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
    border-radius: 8px;
    text-align: center;
}

@media (max-width: 640px) {
    .va-number {
        font-size: 16px;
        padding: 10px;
        letter-spacing: 0.5px;
    }
}

/* Payment method cards */
.payment-method-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.3s ease;
}

.payment-method-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

/* Mobile-specific layout adjustments */
@media (max-width: 768px) {
    .payment-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .payment-sidebar {
        order: -1; /* Show summary first on mobile */
        position: static;
        margin-bottom: 1.5rem;
    }
    
    .payment-content {
        padding: 1rem;
    }
    
    .payment-header {
        text-align: center;
        padding: 1.5rem 1rem;
    }
    
    .payment-title {
        font-size: 1.5rem;
        line-height: 1.3;
    }
    
    .payment-subtitle {
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
}

/* Countdown timer mobile styling */
.countdown-mobile {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Payment instructions mobile */
.payment-instructions-mobile {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1rem;
    margin: 1rem 0;
}

.payment-instructions-mobile ol {
    margin: 0;
    padding-left: 1.25rem;
}

.payment-instructions-mobile li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    font-size: 14px;
}

/* App suggestions mobile */
.app-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

.app-badge {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Status indicator mobile */
.status-indicator-mobile {
    position: fixed;
    bottom: 80px; /* Above floating footer */
    left: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    z-index: 40;
}

@media (min-width: 769px) {
    .status-indicator-mobile {
        display: none;
    }
}

/* Loading states */
.qr-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Copy feedback */
.copy-feedback {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: #10b981;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.copy-feedback.show {
    opacity: 1;
}

.copy-feedback::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #10b981;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .payment-method-card,
    .payment-button,
    .copy-button,
    .status-check-button {
        transition: none;
    }
    
    .loading-spinner {
        animation: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .payment-method-card {
        border: 2px solid #000;
    }
    
    .va-number {
        border: 2px solid #000;
        background: #fff;
    }
    
    .app-badge {
        border: 2px solid #000;
    }
}

/* Focus states for keyboard navigation */
.payment-button:focus,
.copy-button:focus,
.status-check-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .payment-button,
    .copy-button,
    .status-check-button,
    .status-indicator-mobile {
        display: none;
    }
    
    .qr-container {
        break-inside: avoid;
    }
    
    .va-number {
        border: 2px solid #000;
        background: #fff;
    }
}
