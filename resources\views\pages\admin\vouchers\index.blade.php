@extends('layouts.admin')

@section('title', $title)

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $title }}</h1>
            <p class="text-muted">Kelola voucher diskon untuk meningkatkan penjualan tiket</p>
        </div>
        <a href="{{ route('admin.vouchers.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Tambah Voucher
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Voucher</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Voucher Aktif</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['active']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Penggunaan</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_usage']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Penghematan</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">Rp {{ number_format($stats['total_savings'], 0, ',', '.') }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter & Pencarian</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.vouchers.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Pencarian</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ $filters['search'] ?? '' }}" placeholder="Kode atau nama voucher">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">Semua Status</option>
                                <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Aktif</option>
                                <option value="inactive" {{ ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' }}>Tidak Aktif</option>
                                <option value="expired" {{ ($filters['status'] ?? '') === 'expired' ? 'selected' : '' }}>Kedaluwarsa</option>
                                <option value="scheduled" {{ ($filters['status'] ?? '') === 'scheduled' ? 'selected' : '' }}>Terjadwal</option>
                                <option value="exhausted" {{ ($filters['status'] ?? '') === 'exhausted' ? 'selected' : '' }}>Habis</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">Tipe</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">Semua Tipe</option>
                                <option value="percentage" {{ ($filters['type'] ?? '') === 'percentage' ? 'selected' : '' }}>Persentase</option>
                                <option value="fixed" {{ ($filters['type'] ?? '') === 'fixed' ? 'selected' : '' }}>Nominal Tetap</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="sort">Urutkan</label>
                            <select class="form-control" id="sort" name="sort">
                                <option value="created_at" {{ ($filters['sort'] ?? '') === 'created_at' ? 'selected' : '' }}>Tanggal Dibuat</option>
                                <option value="code" {{ ($filters['sort'] ?? '') === 'code' ? 'selected' : '' }}>Kode</option>
                                <option value="name" {{ ($filters['sort'] ?? '') === 'name' ? 'selected' : '' }}>Nama</option>
                                <option value="used_count" {{ ($filters['sort'] ?? '') === 'used_count' ? 'selected' : '' }}>Penggunaan</option>
                                <option value="expires_at" {{ ($filters['sort'] ?? '') === 'expires_at' ? 'selected' : '' }}>Kedaluwarsa</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="direction">Arah</label>
                            <select class="form-control" id="direction" name="direction">
                                <option value="desc" {{ ($filters['direction'] ?? '') === 'desc' ? 'selected' : '' }}>Menurun</option>
                                <option value="asc" {{ ($filters['direction'] ?? '') === 'asc' ? 'selected' : '' }}>Menaik</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">Filter</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Vouchers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Voucher</h6>
        </div>
        <div class="card-body">
            @if($vouchers->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Kode</th>
                                <th>Nama</th>
                                <th>Tipe</th>
                                <th>Nilai</th>
                                <th>Penggunaan</th>
                                <th>Status</th>
                                <th>Kedaluwarsa</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($vouchers as $voucher)
                                <tr>
                                    <td>
                                        <span class="badge badge-secondary">{{ $voucher->code }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ $voucher->name }}</strong>
                                        @if($voucher->description)
                                            <br><small class="text-muted">{{ Str::limit($voucher->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($voucher->type === 'percentage')
                                            <span class="badge badge-info">Persentase</span>
                                        @else
                                            <span class="badge badge-success">Nominal</span>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $voucher->formatted_value }}</strong>
                                        @if($voucher->min_order_amount > 0)
                                            <br><small class="text-muted">Min: Rp {{ number_format($voucher->min_order_amount, 0, ',', '.') }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="mr-2">
                                                <small class="text-muted">{{ $voucher->used_count }}</small>
                                                @if($voucher->usage_limit)
                                                    <small class="text-muted">/ {{ $voucher->usage_limit }}</small>
                                                @endif
                                            </div>
                                            @if($voucher->usage_limit)
                                                <div class="progress flex-grow-1" style="height: 6px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ $voucher->usage_percentage }}%"
                                                         aria-valuenow="{{ $voucher->usage_percentage }}" 
                                                         aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                            $status = $voucher->status;
                                            $statusClass = match($status) {
                                                'active' => 'success',
                                                'inactive' => 'secondary',
                                                'expired' => 'danger',
                                                'scheduled' => 'warning',
                                                'exhausted' => 'dark',
                                                default => 'secondary'
                                            };
                                            $statusText = match($status) {
                                                'active' => 'Aktif',
                                                'inactive' => 'Tidak Aktif',
                                                'expired' => 'Kedaluwarsa',
                                                'scheduled' => 'Terjadwal',
                                                'exhausted' => 'Habis',
                                                default => 'Unknown'
                                            };
                                        @endphp
                                        <span class="badge badge-{{ $statusClass }}">{{ $statusText }}</span>
                                    </td>
                                    <td>
                                        <small>{{ $voucher->expires_at->format('d/m/Y H:i') }}</small>
                                        @if($voucher->expires_at->isFuture())
                                            <br><small class="text-muted">{{ $voucher->expires_at->diffForHumans() }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.vouchers.show', $voucher) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.vouchers.edit', $voucher) }}" 
                                               class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('admin.vouchers.toggle-status', $voucher) }}" 
                                                  method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" 
                                                        class="btn btn-sm btn-outline-{{ $voucher->is_active ? 'danger' : 'success' }}" 
                                                        title="{{ $voucher->is_active ? 'Nonaktifkan' : 'Aktifkan' }}">
                                                    <i class="fas fa-{{ $voucher->is_active ? 'times' : 'check' }}"></i>
                                                </button>
                                            </form>
                                            @if($voucher->usages()->count() === 0)
                                                <form action="{{ route('admin.vouchers.destroy', $voucher) }}" 
                                                      method="POST" class="d-inline"
                                                      onsubmit="return confirm('Yakin ingin menghapus voucher ini?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Hapus">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <small class="text-muted">
                            Menampilkan {{ $vouchers->firstItem() }} - {{ $vouchers->lastItem() }} 
                            dari {{ $vouchers->total() }} voucher
                        </small>
                    </div>
                    <div>
                        {{ $vouchers->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">Belum ada voucher</h5>
                    <p class="text-muted">Mulai buat voucher pertama untuk meningkatkan penjualan tiket.</p>
                    <a href="{{ route('admin.vouchers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tambah Voucher
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.progress {
    background-color: #e9ecef;
}
.badge {
    font-size: 0.75em;
}
</style>
@endpush
