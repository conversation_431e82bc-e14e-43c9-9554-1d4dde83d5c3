@props([
    'count' => 0,
    'color' => 'red',
    'size' => 'sm', // xs, sm, md, lg
    'position' => 'top-right', // top-right, top-left, bottom-right, bottom-left
    'show' => true
])

@php
    $colors = [
        'red' => 'bg-red-500 text-white',
        'blue' => 'bg-blue-500 text-white',
        'green' => 'bg-green-500 text-white',
        'yellow' => 'bg-yellow-500 text-white',
        'purple' => 'bg-purple-500 text-white',
        'orange' => 'bg-orange-500 text-white',
        'pink' => 'bg-pink-500 text-white',
        'indigo' => 'bg-indigo-500 text-white',
        'gray' => 'bg-gray-500 text-white',
    ];
    
    $sizes = [
        'xs' => 'w-3 h-3 text-xs',
        'sm' => 'w-4 h-4 text-xs',
        'md' => 'w-5 h-5 text-sm',
        'lg' => 'w-6 h-6 text-sm'
    ];
    
    $positions = [
        'top-right' => '-top-1 -right-1',
        'top-left' => '-top-1 -left-1',
        'bottom-right' => '-bottom-1 -right-1',
        'bottom-left' => '-bottom-1 -left-1'
    ];
    
    $shouldShow = $show && $count > 0;
    
    $classes = collect([
        'absolute',
        $positions[$position] ?? $positions['top-right'],
        $sizes[$size] ?? $sizes['sm'],
        $colors[$color] ?? $colors['red'],
        'rounded-full',
        'flex',
        'items-center',
        'justify-center',
        'font-medium',
        'leading-none'
    ])->implode(' ');
@endphp

@if($shouldShow)
    <span {{ $attributes->merge(['class' => $classes]) }}>
        {{ $count > 99 ? '99+' : $count }}
    </span>
@endif
