<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Musik & Konser',
                'slug' => 'musik-konser',
                'description' => 'Konser musik, festival musik, dan pertunjukan musik dari berbagai genre',
                'icon' => 'musical-note',
                'color' => '#A8D5BA',
                'sort_order' => 1,
            ],
            [
                'name' => 'Workshop & Seminar',
                'slug' => 'workshop-seminar',
                'description' => 'Workshop, seminar, pelatihan, dan acara edukasi untuk pengembangan skill',
                'icon' => 'academic-cap',
                'color' => '#F4A261',
                'sort_order' => 2,
            ],
            [
                'name' => 'Festival & Pameran',
                'slug' => 'festival-pameran',
                'description' => 'Festival budaya, pameran seni, dan acara komunitas yang meriah',
                'icon' => 'flag',
                'color' => '#E76F51',
                'sort_order' => 3,
            ],
            [
                'name' => 'Seni & Budaya',
                'slug' => 'seni-budaya',
                'description' => 'Pertunjukan teater, tari tradisional, pameran seni, dan acara budaya',
                'icon' => 'sparkles',
                'color' => '#9D4EDD',
                'sort_order' => 4,
            ],
            [
                'name' => 'Kuliner & Food Festival',
                'slug' => 'kuliner-food-festival',
                'description' => 'Festival makanan, wine tasting, cooking class, dan acara kuliner',
                'icon' => 'cake',
                'color' => '#FF6B6B',
                'sort_order' => 5,
            ],
            [
                'name' => 'Gaming & Esports',
                'slug' => 'gaming-esports',
                'description' => 'Tournament gaming, esports competition, gaming convention, dan acara gaming',
                'icon' => 'device-gamepad-2',
                'color' => '#4ECDC4',
                'sort_order' => 6,
            ],
            [
                'name' => 'Olahraga',
                'slug' => 'olahraga',
                'description' => 'Pertandingan olahraga, turnamen, dan acara kebugaran',
                'icon' => 'trophy',
                'color' => '#2A9D8F',
                'sort_order' => 4,
            ],
            [
                'name' => 'Teknologi',
                'slug' => 'teknologi',
                'description' => 'Conference teknologi, hackathon, dan acara IT',
                'icon' => 'computer-desktop',
                'color' => '#264653',
                'sort_order' => 5,
            ],
            [
                'name' => 'Kuliner',
                'slug' => 'kuliner',
                'description' => 'Food festival, cooking class, dan acara kuliner',
                'icon' => 'cake',
                'color' => '#E9C46A',
                'sort_order' => 6,
            ],
            [
                'name' => 'Seni & Budaya',
                'slug' => 'seni-budaya',
                'description' => 'Pertunjukan seni, teater, dan acara budaya',
                'icon' => 'paint-brush',
                'color' => '#F76C6C',
                'sort_order' => 7,
            ],
            [
                'name' => 'Bisnis & Startup',
                'slug' => 'bisnis-startup',
                'description' => 'Business conference, networking event, startup pitch, dan acara bisnis',
                'icon' => 'briefcase',
                'color' => '#A8DADC',
                'sort_order' => 8,
            ],
            [
                'name' => 'Kesehatan & Wellness',
                'slug' => 'kesehatan-wellness',
                'description' => 'Yoga class, meditation workshop, health seminar, dan acara kesehatan',
                'icon' => 'heart',
                'color' => '#FFB3BA',
                'sort_order' => 9,
            ],
            [
                'name' => 'Pendidikan & Pelatihan',
                'slug' => 'pendidikan-pelatihan',
                'description' => 'Course online/offline, training program, certification, dan acara edukasi',
                'icon' => 'book-open',
                'color' => '#BAFFC9',
                'sort_order' => 10,
            ],
            [
                'name' => 'Otomotif',
                'slug' => 'otomotif',
                'description' => 'Car show, motor exhibition, automotive expo, dan acara otomotif',
                'icon' => 'car',
                'color' => '#BAE1FF',
                'sort_order' => 11,
            ],
            [
                'name' => 'Lifestyle & Fashion',
                'slug' => 'lifestyle-fashion',
                'description' => 'Fashion show, beauty workshop, lifestyle expo, dan acara fashion',
                'icon' => 'shirt',
                'color' => '#FFFFBA',
                'sort_order' => 12,
            ],
            [
                'name' => 'Anak & Keluarga',
                'slug' => 'anak-keluarga',
                'description' => 'Family fun day, kids workshop, parenting seminar, dan acara keluarga',
                'icon' => 'users',
                'color' => '#FFD1DC',
                'sort_order' => 13,
            ],
            [
                'name' => 'Religi & Spiritual',
                'slug' => 'religi-spiritual',
                'description' => 'Kajian agama, retreat spiritual, seminar keagamaan, dan acara religi',
                'icon' => 'moon',
                'color' => '#E6E6FA',
                'sort_order' => 14,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
