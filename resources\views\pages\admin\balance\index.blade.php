@extends('layouts.admin')

@section('title', 'Manajemen Saldo')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Manajemen Saldo</h1>
            <p class="text-muted">Kelola saldo pengguna dan transaksi</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.balance.withdrawals') }}" class="btn btn-warning">
                <i class="fas fa-money-bill-wave"></i> Kelola Penarikan
            </a>
            <button type="button" class="btn btn-info" onclick="exportData()">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Saldo
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($stats['total_balances'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Saldo Pending
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($stats['total_pending'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Pendapatan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($stats['total_earned'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Fee Terkumpul
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($stats['total_fees_collected'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter & Pencarian</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.balance.index') }}" class="row">
                <div class="col-md-4 mb-3">
                    <label for="search" class="form-label">Pencarian</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Nama atau email pengguna...">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="min_balance" class="form-label">Saldo Minimum</label>
                    <input type="number" class="form-control" id="min_balance" name="min_balance"
                           value="{{ request('min_balance') }}" placeholder="0">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="max_balance" class="form-label">Saldo Maksimum</label>
                    <input type="number" class="form-control" id="max_balance" name="max_balance"
                           value="{{ request('max_balance') }}" placeholder="1000000">
                </div>
                <div class="col-md-2 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ route('admin.balance.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Balance Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Saldo Pengguna</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Pengguna</th>
                            <th>Saldo</th>
                            <th>Saldo Tersedia</th>
                            <th>Pending</th>
                            <th>Total Pendapatan</th>
                            <th>Total Penarikan</th>
                            <th>Fee Dibayar</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($balances as $balance)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $balance->user->avatar_url }}" alt="{{ $balance->user->name }}"
                                             class="rounded-circle me-2" width="40" height="40">
                                        <div>
                                            <div class="font-weight-bold">{{ $balance->user->name }}</div>
                                            <div class="text-muted small">{{ $balance->user->email }}</div>
                                            <span class="badge badge-{{ $balance->user->role == 'admin' ? 'danger' : 'primary' }}">
                                                {{ ucfirst($balance->user->role) }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="font-weight-bold text-success">
                                        {{ $balance->formatted_balance }}
                                    </span>
                                </td>
                                <td>
                                    <span class="font-weight-bold text-primary">
                                        {{ $balance->formatted_available_balance }}
                                    </span>
                                </td>
                                <td>
                                    @if($balance->pending_balance > 0)
                                        <span class="font-weight-bold text-warning">
                                            Rp {{ number_format($balance->pending_balance, 0, ',', '.') }}
                                        </span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>Rp {{ number_format($balance->total_earned, 0, ',', '.') }}</td>
                                <td>Rp {{ number_format($balance->total_withdrawn, 0, ',', '.') }}</td>
                                <td>Rp {{ number_format($balance->total_fees_paid, 0, ',', '.') }}</td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                                data-bs-toggle="dropdown">
                                            Aksi
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="#"
                                                   onclick="showAdjustBalanceModal({{ $balance->user->id }}, '{{ $balance->user->name }}', {{ $balance->balance }})">
                                                    <i class="fas fa-edit me-2"></i>Sesuaikan Saldo
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('admin.users.show', $balance->user) }}">
                                                    <i class="fas fa-eye me-2"></i>Lihat Detail
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-wallet fa-3x mb-3"></i>
                                        <p>Tidak ada data saldo yang ditemukan</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($balances->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $balances->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Adjust Balance Modal -->
<div class="modal fade" id="adjustBalanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sesuaikan Saldo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="adjustBalanceForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Pengguna</label>
                        <input type="text" class="form-control" id="userName" readonly>
                        <input type="hidden" id="userId">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Saldo Saat Ini</label>
                        <input type="text" class="form-control" id="currentBalance" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="adjustType" class="form-label">Jenis Penyesuaian</label>
                        <select class="form-control" id="adjustType" name="type" required>
                            <option value="add">Tambah Saldo</option>
                            <option value="deduct">Kurangi Saldo</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="adjustAmount" class="form-label">Jumlah</label>
                        <input type="number" class="form-control" id="adjustAmount" name="amount"
                               min="1" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="adjustDescription" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="adjustDescription" name="description"
                                  rows="3" required placeholder="Alasan penyesuaian saldo..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Sesuaikan Saldo</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showAdjustBalanceModal(userId, userName, currentBalance) {
    document.getElementById('userId').value = userId;
    document.getElementById('userName').value = userName;
    document.getElementById('currentBalance').value = 'Rp ' + new Intl.NumberFormat('id-ID').format(currentBalance);

    new bootstrap.Modal(document.getElementById('adjustBalanceModal')).show();
}

document.getElementById('adjustBalanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const userId = document.getElementById('userId').value;
    const formData = new FormData(this);

    try {
        const response = await fetch(`/admin/balance/${userId}/adjust`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('adjustBalanceModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

async function exportData() {
    try {
        const response = await fetch('/admin/balance/export', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (data.success) {
            // Download JSON file
            const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            a.click();
            window.URL.revokeObjectURL(url);

            showAlert('success', 'Data berhasil diexport');
        } else {
            showAlert('error', 'Gagal export data');
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

function showAlert(type, message) {
    // You can implement your preferred alert system here
    alert(message);
}
</script>
@endpush
