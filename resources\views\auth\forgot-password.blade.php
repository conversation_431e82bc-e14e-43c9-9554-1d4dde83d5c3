@extends('layouts.auth')

@section('content')
<div>
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
            </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Lupa Password?</h2>
        <p class="text-gray-600">
            Tidak masalah! Masukkan email Anda dan kami akan mengirim link reset password.
        </p>
    </div>

    <!-- Forgot Password Form -->
    <form method="POST" action="{{ route('password.email') }}" class="space-y-6">
        @csrf

        <!-- Email Field -->
        <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                Email
            </label>
            <div class="relative">
                <input type="email" 
                       id="email" 
                       name="email" 
                       value="{{ old('email') }}"
                       class="input-field w-full px-4 py-3 pl-12 rounded-xl focus:outline-none @error('email') border-red-500 @enderror"
                       placeholder="Masukkan email Anda"
                       required>
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-tickets-none">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                    </svg>
                </div>
            </div>
            @error('email')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Submit Button -->
        <button type="submit" 
                class="btn-primary w-full py-3 px-4 rounded-xl font-semibold text-lg">
            Kirim Link Reset Password
        </button>
    </form>

    <!-- Help Text -->
    <div class="mt-8 p-4 bg-blue-50 rounded-xl">
        <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <div class="text-sm text-blue-700">
                <p class="font-medium mb-1">Catatan:</p>
                <ul class="space-y-1 text-xs">
                    <li>• Link reset password akan dikirim ke email Anda</li>
                    <li>• Periksa folder spam jika tidak menerima email</li>
                    <li>• Link berlaku selama 60 menit</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Back to Login -->
    <div class="text-center mt-6">
        <a href="{{ route('login') }}" 
           class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Kembali ke login
        </a>
    </div>
</div>
@endsection
