@props([
    'icon' => 'inbox',
    'title' => 'No data found',
    'description' => null,
    'actionText' => null,
    'actionUrl' => null,
    'actionIcon' => 'plus',
    'iconColor' => 'gray',
    'size' => 'md' // sm, md, lg
])

@php
    $iconColors = [
        'blue' => 'text-blue-400',
        'green' => 'text-green-400',
        'yellow' => 'text-yellow-400',
        'red' => 'text-red-400',
        'purple' => 'text-purple-400',
        'orange' => 'text-orange-400',
        'pink' => 'text-pink-400',
        'indigo' => 'text-indigo-400',
        'gray' => 'text-gray-400',
    ];
    
    $sizes = [
        'sm' => [
            'container' => 'py-8',
            'icon' => 'w-8 h-8',
            'title' => 'text-lg',
            'description' => 'text-sm',
            'button' => 'px-4 py-2 text-sm'
        ],
        'md' => [
            'container' => 'py-12',
            'icon' => 'w-12 h-12',
            'title' => 'text-xl',
            'description' => 'text-base',
            'button' => 'px-6 py-3 text-base'
        ],
        'lg' => [
            'container' => 'py-16',
            'icon' => 'w-16 h-16',
            'title' => 'text-2xl',
            'description' => 'text-lg',
            'button' => 'px-8 py-4 text-lg'
        ]
    ];
    
    $sizeConfig = $sizes[$size] ?? $sizes['md'];
@endphp

<div {{ $attributes->merge(['class' => 'text-center ' . $sizeConfig['container']]) }}>
    <!-- Icon -->
    <div class="mx-auto mb-4">
        <i data-lucide="{{ $icon }}" class="{{ $sizeConfig['icon'] }} mx-auto {{ $iconColors[$iconColor] ?? $iconColors['gray'] }}"></i>
    </div>
    
    <!-- Title -->
    <h3 class="{{ $sizeConfig['title'] }} font-semibold text-gray-900 dark:text-white mb-2">{{ $title }}</h3>
    
    <!-- Description -->
    @if($description)
        <p class="{{ $sizeConfig['description'] }} text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">{{ $description }}</p>
    @endif
    
    <!-- Action Button -->
    @if($actionText && $actionUrl)
        <a href="{{ $actionUrl }}" 
           class="inline-flex items-center {{ $sizeConfig['button'] }} bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
            <i data-lucide="{{ $actionIcon }}" class="w-4 h-4 mr-2"></i>
            {{ $actionText }}
        </a>
    @endif
    
    <!-- Custom Action Slot -->
    @isset($action)
        <div class="mt-6">
            {{ $action }}
        </div>
    @endisset
</div>
