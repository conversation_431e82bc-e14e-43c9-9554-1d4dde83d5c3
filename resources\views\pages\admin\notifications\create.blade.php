@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2"><PERSON><PERSON></h1>
                <p class="text-gray-600">Buat dan kirim notifikasi ke pengguna</p>
            </div>
            <a href="{{ route('admin.notifications.index') }}" 
               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                Kembali
            </a>
        </div>
    </div>

    <!-- Create Notification Form -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="100">
        <form action="{{ route('admin.notifications.store') }}" method="POST" class="p-6 space-y-6">
            @csrf
            
            <!-- Notification Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="lg:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Judul Notifikasi</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title') }}"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           placeholder="Masukkan judul notifikasi"
                           required>
                    @error('title')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Tipe Notifikasi</label>
                    <select id="type" 
                            name="type" 
                            class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                            required>
                        <option value="">Pilih Tipe</option>
                        <option value="system" {{ old('type') == 'system' ? 'selected' : '' }}>System</option>
                        <option value="user" {{ old('type') == 'user' ? 'selected' : '' }}>User</option>
                        <option value="event" {{ old('type') == 'event' ? 'selected' : '' }}>Event</option>
                        <option value="payment" {{ old('type') == 'payment' ? 'selected' : '' }}>Payment</option>
                    </select>
                    @error('type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Recipients -->
                <div>
                    <label for="recipients" class="block text-sm font-medium text-gray-700 mb-2">Penerima</label>
                    <select id="recipients" 
                            name="recipients" 
                            class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                            onchange="toggleRecipientOptions()"
                            required>
                        <option value="">Pilih Penerima</option>
                        <option value="all" {{ old('recipients') == 'all' ? 'selected' : '' }}>Semua Pengguna</option>
                        <option value="role" {{ old('recipients') == 'role' ? 'selected' : '' }}>Berdasarkan Role</option>
                        <option value="specific" {{ old('recipients') == 'specific' ? 'selected' : '' }}>Pengguna Spesifik</option>
                    </select>
                    @error('recipients')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Role Selection (conditional) -->
            <div id="role-selection" class="hidden">
                <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Pilih Role</label>
                <select id="role" 
                        name="role" 
                        class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                    <option value="">Pilih Role</option>
                    <option value="admin" {{ old('role') == 'admin' ? 'selected' : '' }}>Admin</option>
                    <option value="staff" {{ old('role') == 'staff' ? 'selected' : '' }}>Staff</option>
                    <option value="penjual" {{ old('role') == 'penjual' ? 'selected' : '' }}>Organizer/Penjual</option>
                    <option value="pembeli" {{ old('role') == 'pembeli' ? 'selected' : '' }}>Pembeli</option>
                </select>
                @error('role')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Specific Users Selection (conditional) -->
            <div id="users-selection" class="hidden">
                <label for="user_ids" class="block text-sm font-medium text-gray-700 mb-2">Pilih Pengguna</label>
                <div class="border border-gray-300 rounded-lg p-4 max-h-60 overflow-y-auto">
                    <div class="mb-3">
                        <input type="text" 
                               id="user-search" 
                               placeholder="Cari pengguna..." 
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                               onkeyup="filterUsers()">
                    </div>
                    <div id="users-list" class="space-y-2">
                        @foreach($users as $user)
                        <label class="flex items-center user-item" data-name="{{ strtolower($user->name) }}" data-email="{{ strtolower($user->email) }}">
                            <input type="checkbox" 
                                   name="user_ids[]" 
                                   value="{{ $user->id }}"
                                   {{ in_array($user->id, old('user_ids', [])) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-sm">
                                {{ $user->name }} ({{ $user->email }})
                                <span class="text-gray-500">- {{ ucfirst($user->role) }}</span>
                            </span>
                        </label>
                        @endforeach
                    </div>
                </div>
                @error('user_ids')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Message -->
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Pesan</label>
                <textarea id="message" 
                          name="message" 
                          rows="6" 
                          class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                          placeholder="Masukkan pesan notifikasi"
                          required>{{ old('message') }}</textarea>
                @error('message')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Preview Section -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Preview Notifikasi</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <div class="p-2 bg-primary/10 rounded-lg">
                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 id="preview-title" class="font-medium text-gray-900">Judul notifikasi akan muncul di sini</h4>
                            <p id="preview-message" class="text-sm text-gray-600 mt-1">Pesan notifikasi akan muncul di sini</p>
                            <p class="text-xs text-gray-500 mt-2">Baru saja</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.notifications.index') }}" 
                   class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                    Kirim Notifikasi
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleRecipientOptions() {
    const recipients = document.getElementById('recipients').value;
    const roleSelection = document.getElementById('role-selection');
    const usersSelection = document.getElementById('users-selection');
    
    // Hide all conditional sections
    roleSelection.classList.add('hidden');
    usersSelection.classList.add('hidden');
    
    // Show relevant section
    if (recipients === 'role') {
        roleSelection.classList.remove('hidden');
    } else if (recipients === 'specific') {
        usersSelection.classList.remove('hidden');
    }
}

function filterUsers() {
    const searchTerm = document.getElementById('user-search').value.toLowerCase();
    const userItems = document.querySelectorAll('.user-item');
    
    userItems.forEach(item => {
        const name = item.dataset.name;
        const email = item.dataset.email;
        
        if (name.includes(searchTerm) || email.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// Live preview
document.getElementById('title').addEventListener('input', function() {
    const title = this.value || 'Judul notifikasi akan muncul di sini';
    document.getElementById('preview-title').textContent = title;
});

document.getElementById('message').addEventListener('input', function() {
    const message = this.value || 'Pesan notifikasi akan muncul di sini';
    document.getElementById('preview-message').textContent = message;
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleRecipientOptions();
});
</script>
@endsection
