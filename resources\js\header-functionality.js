/**
 * Header Functionality for TiXara Application
 * Handles search, notifications, theme management, and other header features
 */

// Global header functionality
window.headerFunctions = {
    // Search functionality
    async searchEvents(query) {
        if (!query || query.length < 2) {
            return [];
        }

        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                const data = await response.json();

                // Log search results for debugging
                console.log('Search results:', data);

                if (data.error) {
                    console.warn('Search API error:', data.error);
                    window.showNotification?.(data.message || 'Pencarian tidak tersedia', 'warning', 3000);
                    return [];
                }

                return data.results || [];
            } else {
                console.error('Search API response error:', response.status, response.statusText);
                window.showNotification?.('Pencarian tidak tersedia', 'error', 3000);
                return [];
            }
        } catch (error) {
            console.error('Search network error:', error);
            window.showNotification?.('Koneksi bermasalah, coba lagi', 'error', 3000);
            return [];
        }
    },

    // Notification management
    async loadNotifications() {
        // Only load notifications if user is authenticated
        if (!document.querySelector('.notification-badge')) {
            return;
        }

        try {
            const response = await fetch('/api/notifications/latest', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                const data = await response.json();

                console.log('Notifications loaded:', data);

                if (data.error) {
                    console.warn('Notifications API error:', data.error);
                    if (response.status === 401) {
                        // User not authenticated, hide notification features
                        const notificationButton = document.querySelector('[data-notifications]');
                        if (notificationButton) {
                            notificationButton.style.display = 'none';
                        }
                    }
                    return;
                }

                this.updateNotificationsList(data.notifications || []);
                this.updateNotificationBadge(data.unread_count || 0);
            } else if (response.status === 401) {
                // User not authenticated
                console.log('User not authenticated for notifications');
                const notificationButton = document.querySelector('[data-notifications]');
                if (notificationButton) {
                    notificationButton.style.display = 'none';
                }
            } else {
                console.error('Notifications API error:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    },

    updateNotificationsList(notifications) {
        const notificationsList = document.getElementById('notifications-list');
        const emptyState = document.getElementById('notifications-empty');

        if (!notificationsList) return;

        if (notifications.length === 0) {
            notificationsList.innerHTML = '';
            if (emptyState) emptyState.style.display = 'block';
            return;
        }

        if (emptyState) emptyState.style.display = 'none';

        const notificationsHtml = notifications.map(notification => `
            <div class="px-4 py-3 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 cursor-pointer border-b border-pasta-cream/30 dark:border-dark-600 transition-all duration-300 group"
                 onclick="markNotificationAsRead('${notification.id}')">
                <div class="flex items-start space-x-3">
                    <div class="w-3 h-3 bg-gradient-to-r from-pasta-salmon to-pasta-peach rounded-full mt-2 shadow-sm group-hover:scale-110 transition-transform duration-300 ${notification.read_at ? 'opacity-50' : ''}"></div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">${notification.title}</h4>
                            <span class="text-xs text-gray-500 dark:text-gray-400">${this.formatTimeAgo(notification.created_at)}</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">${notification.message}</p>
                        <div class="flex items-center space-x-2 mt-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                ${notification.type || 'Info'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        notificationsList.innerHTML = notificationsHtml;
    },

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.style.display = 'flex';
                badge.textContent = count > 99 ? '99+' : count;
            } else {
                badge.style.display = 'none';
            }
        }
    },

    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Baru saja';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lalu`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lalu`;
        return `${Math.floor(diffInSeconds / 86400)} hari lalu`;
    },

    // Theme management
    applyTheme(themeColor, darkMode) {
        const root = document.documentElement;
        const themes = {
            green: { name: 'Hijau Pasta', primary: '#A8D5BA' },
            blue: { name: 'Biru Laut', primary: '#6366F1' },
            purple: { name: 'Ungu Muda', primary: '#8B5CF6' },
            pink: { name: 'Pink Soft', primary: '#EC4899' },
            orange: { name: 'Orange Hangat', primary: '#F97316' },
            red: { name: 'Merah Ceria', primary: '#EF4444' },
            monochrome: { name: 'Monokrom', primary: '#6B7280' },
            dark: { name: 'Gelap Total', primary: '#1F2937' }
        };

        const theme = themes[themeColor] || themes.green;

        // Apply dark mode
        if (darkMode || themeColor === 'dark') {
            root.classList.add('dark');
        } else {
            root.classList.remove('dark');
        }

        // Apply theme colors
        root.style.setProperty('--color-primary', theme.primary);

        // Update CSS custom properties for pasta colors
        if (themeColor === 'green') {
            root.style.setProperty('--pasta-sage', '#A8D5BA');
            root.style.setProperty('--pasta-cream', '#F5F5DC');
            root.style.setProperty('--pasta-mint', '#98FB98');
            root.style.setProperty('--pasta-peach', '#FFCBA4');
            root.style.setProperty('--pasta-salmon', '#FA8072');
        }

        // Update meta theme color for mobile browsers
        const metaThemeColor = document.querySelector('meta[name=theme-color]');
        if (metaThemeColor) {
            metaThemeColor.setAttribute('content', theme.primary);
        }

        // Save to localStorage
        localStorage.setItem('themeColor', themeColor);
        localStorage.setItem('darkMode', darkMode);

        // Dispatch event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { themeColor, darkMode, theme }
        }));
    },

    // Sound notification
    playNotificationSound() {
        if (window.liveNotifications && window.liveNotifications.soundEnabled) {
            window.liveNotifications.playNotificationSound();
        }
    }
};

// Global functions for backward compatibility
window.markNotificationAsRead = async function(notificationId) {
    try {
        const response = await fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        if (response.ok) {
            // Reload notifications
            window.headerFunctions.loadNotifications();
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
    }
};

window.markAllNotificationsAsRead = async function() {
    try {
        const response = await fetch('/api/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        if (response.ok) {
            // Reload notifications
            window.headerFunctions.loadNotifications();
            window.showNotification('Semua notifikasi telah ditandai sebagai dibaca', 'success');
        }
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
    }
};

// Initialize header functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Load notifications if user is authenticated
    if (document.querySelector('.notification-badge')) {
        window.headerFunctions.loadNotifications();

        // Refresh notifications every 30 seconds
        setInterval(() => {
            window.headerFunctions.loadNotifications();
        }, 30000);
    }

    // Initialize theme from localStorage
    const savedTheme = localStorage.getItem('themeColor') || 'green';
    const savedDarkMode = localStorage.getItem('darkMode') === 'true';
    window.headerFunctions.applyTheme(savedTheme, savedDarkMode);
});

// Listen for new notifications from live notifications system
window.addEventListener('newNotification', function(event) {
    const { notification, count } = event.detail;

    // Update badge
    window.headerFunctions.updateNotificationBadge(count);

    // Play sound
    window.headerFunctions.playNotificationSound();

    // Reload notifications list
    window.headerFunctions.loadNotifications();
});
