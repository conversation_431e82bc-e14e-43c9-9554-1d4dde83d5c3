<?php

namespace App\Services;

use App\Models\Ticket;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class QRCodeService
{
    /**
     * Generate QR code for ticket
     */
    public function generateTicketQRCode(Ticket $ticket): string
    {
        $qrData = $this->generateQRData($ticket);

        // Generate QR code as SVG
        $qrCode = QrCode::format('svg')
            ->size(300)
            ->margin(2)
            ->generate($qrData);

        // Save QR code to storage
        $filename = 'qr-codes/ticket-' . $ticket->id . '.svg';
        Storage::disk('public')->put($filename, $qrCode);

        return $filename;
    }

    /**
     * Generate QR code data for ticket
     */
    public function generateQRData(Ticket $ticket): string
    {
        return json_encode([
            'ticket_id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'qr_code' => $ticket->qr_code,
            'event_id' => $ticket->event_id,
            'buyer_id' => $ticket->buyer_id,
            'validation_url' => route('tickets.validate', $ticket->ticket_number),
            'generated_at' => now()->toISOString(),
        ]);
    }

    /**
     * Generate QR code for validation URL
     */
    public function generateValidationQRCode(Ticket $ticket): string
    {
        $validationUrl = route('tickets.validate', $ticket->ticket_number);

        $qrCode = QrCode::format('svg')
            ->size(200)
            ->margin(1)
            ->generate($validationUrl);

        $filename = 'qr-codes/validation-' . $ticket->id . '.svg';
        Storage::disk('public')->put($filename, $qrCode);

        return $filename;
    }

    /**
     * Generate QR code as base64 image
     */
    public function generateQRCodeBase64(Ticket $ticket): string
    {
        $qrData = $this->generateQRData($ticket);

        return QrCode::format('png')
            ->size(300)
            ->margin(2)
            ->generate($qrData);
    }

    /**
     * Validate QR code data
     */
    public function validateQRData(string $qrData): array
    {
        try {
            $data = json_decode($qrData, true);

            if (!$data || !isset($data['ticket_number'])) {
                return [
                    'valid' => false,
                    'message' => 'QR Code tidak valid'
                ];
            }

            $ticket = Ticket::where('ticket_number', $data['ticket_number'])->first();

            if (!$ticket) {
                return [
                    'valid' => false,
                    'message' => 'Tiket tidak ditemukan'
                ];
            }

            if ($ticket->qr_code !== $data['qr_code']) {
                return [
                    'valid' => false,
                    'message' => 'QR Code tidak cocok dengan tiket'
                ];
            }

            return [
                'valid' => true,
                'ticket' => $ticket,
                'message' => 'QR Code valid'
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => 'QR Code format tidak valid'
            ];
        }
    }

    /**
     * Generate batch QR codes for multiple tickets
     */
    public function generateBatchQRCodes(array $tickets): array
    {
        $qrCodes = [];

        foreach ($tickets as $ticket) {
            $qrCodes[$ticket->id] = $this->generateTicketQRCode($ticket);
        }

        return $qrCodes;
    }

    /**
     * Delete QR code file
     */
    public function deleteQRCode(string $filename): bool
    {
        if (Storage::disk('public')->exists($filename)) {
            return Storage::disk('public')->delete($filename);
        }

        return false;
    }

    /**
     * Get QR code URL
     */
    public function getQRCodeUrl(string $filename): string
    {
        return Storage::disk('public')->url($filename);
    }

    /**
     * Generate QR code for event (for organizer download)
     */
    public function generateEventQRCode($event, string $qrData): string
    {
        return QrCode::format('svg')
            ->size(400)
            ->margin(2)
            ->color(0, 0, 0)
            ->backgroundColor(255, 255, 255)
            ->generate($qrData);
    }
}
