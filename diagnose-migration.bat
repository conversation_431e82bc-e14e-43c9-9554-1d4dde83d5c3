@echo off
echo Diagnosing Migration Issues for TiXara...

echo.
echo [1/7] Checking database connection...
php artisan tinker --execute="
try {
    \$connection = \Illuminate\Support\Facades\DB::connection();
    echo 'Database connection: SUCCESS' . PHP_EOL;
    echo 'Database: ' . \$connection->getDatabaseName() . PHP_EOL;
    echo 'Driver: ' . \$connection->getDriverName() . PHP_EOL;
    
    // Test basic query
    \$result = \Illuminate\Support\Facades\DB::select('SELECT 1 as test');
    echo 'Basic query test: SUCCESS' . PHP_EOL;
    
} catch (Exception \$e) {
    echo 'Database connection: FAILED' . PHP_EOL;
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
    echo 'Please check your .env database configuration' . PHP_EOL;
}
"

echo.
echo [2/7] Checking migration files...
php artisan tinker --execute="
try {
    echo 'Checking migration files exist:' . PHP_EOL;
    
    \$migrationFiles = [
        '2014_10_12_000000_create_users_table.php',
        '2024_01_01_000001_add_role_fields_to_users_table.php', 
        '2024_01_01_000002_create_categories_table.php',
        '2024_01_01_000003_create_tickets_table.php',
        '2024_01_01_000004_create_orders_table.php',
        '2024_01_01_000005_create_tickets_table.php'
    ];
    
    \$migrationPath = database_path('migrations');
    
    foreach (\$migrationFiles as \$file) {
        \$fullPath = \$migrationPath . DIRECTORY_SEPARATOR . \$file;
        if (file_exists(\$fullPath)) {
            echo '✓ ' . \$file . PHP_EOL;
        } else {
            echo '✗ ' . \$file . ' MISSING' . PHP_EOL;
        }
    }
    
} catch (Exception \$e) {
    echo 'Migration file check error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [3/7] Checking current migration status...
php artisan migrate:status

echo.
echo [4/7] Checking current tables...
php artisan tinker --execute="
try {
    echo 'Current tables in database:' . PHP_EOL;
    \$tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');
    
    if (empty(\$tables)) {
        echo 'No tables found' . PHP_EOL;
    } else {
        foreach (\$tables as \$table) {
            \$tableName = array_values((array)\$table)[0];
            echo '- ' . \$tableName . PHP_EOL;
            
            // Check for problematic foreign keys
            if (\$tableName === 'orders' || \$tableName === 'tickets') {
                \$columns = \Illuminate\Support\Facades\Schema::getColumnListing(\$tableName);
                if (in_array('tiket_id', \$columns)) {
                    echo '  WARNING: ' . \$tableName . ' has tiket_id column (should be event_id)' . PHP_EOL;
                }
                if (in_array('event_id', \$columns)) {
                    echo '  OK: ' . \$tableName . ' has event_id column' . PHP_EOL;
                }
            }
        }
    }
    
} catch (Exception \$e) {
    echo 'Table check error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/7] Checking for foreign key constraints...
php artisan tinker --execute="
try {
    echo 'Checking foreign key constraints:' . PHP_EOL;
    
    \$constraints = \Illuminate\Support\Facades\DB::select('
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ');
    
    if (empty(\$constraints)) {
        echo 'No foreign key constraints found' . PHP_EOL;
    } else {
        foreach (\$constraints as \$constraint) {
            echo '- ' . \$constraint->TABLE_NAME . '.' . \$constraint->COLUMN_NAME . 
                 ' -> ' . \$constraint->REFERENCED_TABLE_NAME . '.' . \$constraint->REFERENCED_COLUMN_NAME . PHP_EOL;
        }
    }
    
} catch (Exception \$e) {
    echo 'Foreign key check error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [6/7] Checking for problematic references...
php artisan tinker --execute="
try {
    echo 'Checking for problematic table references:' . PHP_EOL;
    
    // Check if any foreign keys reference non-existent tables
    \$problematicRefs = \Illuminate\Support\Facades\DB::select('
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
        AND REFERENCED_TABLE_NAME IS NOT NULL
        AND REFERENCED_TABLE_NAME NOT IN (
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE()
        )
    ');
    
    if (empty(\$problematicRefs)) {
        echo 'No problematic foreign key references found' . PHP_EOL;
    } else {
        echo 'PROBLEMATIC REFERENCES FOUND:' . PHP_EOL;
        foreach (\$problematicRefs as \$ref) {
            echo '✗ ' . \$ref->TABLE_NAME . '.' . \$ref->COLUMN_NAME . 
                 ' references non-existent table: ' . \$ref->REFERENCED_TABLE_NAME . PHP_EOL;
        }
    }
    
} catch (Exception \$e) {
    echo 'Problematic reference check error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [7/7] Generating diagnosis report...
php artisan tinker --execute="
try {
    echo PHP_EOL . '========================================' . PHP_EOL;
    echo 'MIGRATION DIAGNOSIS REPORT' . PHP_EOL;
    echo '========================================' . PHP_EOL;
    
    // Check what needs to be done
    \$tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');
    \$tableNames = array_map(function(\$table) {
        return array_values((array)\$table)[0];
    }, \$tables);
    
    \$requiredTables = ['users', 'categories', 'tickets', 'orders', 'tickets'];
    \$missingTables = array_diff(\$requiredTables, \$tableNames);
    
    if (empty(\$missingTables)) {
        echo '✓ All required tables exist' . PHP_EOL;
        
        // Check for column issues
        \$issues = [];
        
        if (in_array('orders', \$tableNames)) {
            \$orderColumns = \Illuminate\Support\Facades\Schema::getColumnListing('orders');
            if (in_array('tiket_id', \$orderColumns)) {
                \$issues[] = 'orders table has tiket_id (should be event_id)';
            }
        }
        
        if (in_array('tickets', \$tableNames)) {
            \$ticketColumns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
            if (in_array('tiket_id', \$ticketColumns)) {
                \$issues[] = 'tickets table has tiket_id (should be event_id)';
            }
        }
        
        if (empty(\$issues)) {
            echo '✓ No column issues found' . PHP_EOL;
            echo PHP_EOL . 'RECOMMENDATION: Database looks good!' . PHP_EOL;
        } else {
            echo '✗ Column issues found:' . PHP_EOL;
            foreach (\$issues as \$issue) {
                echo '  - ' . \$issue . PHP_EOL;
            }
            echo PHP_EOL . 'RECOMMENDATION: Run retry-migration.bat to fix column issues' . PHP_EOL;
        }
        
    } else {
        echo '✗ Missing tables: ' . implode(', ', \$missingTables) . PHP_EOL;
        
        if (count(\$missingTables) === count(\$requiredTables)) {
            echo PHP_EOL . 'RECOMMENDATION: Run fresh-migrate.bat for clean start' . PHP_EOL;
        } else {
            echo PHP_EOL . 'RECOMMENDATION: Run retry-migration.bat to complete migration' . PHP_EOL;
        }
    }
    
    echo PHP_EOL . 'AVAILABLE SCRIPTS:' . PHP_EOL;
    echo '- fresh-migrate.bat: Complete database reset (deletes all data)' . PHP_EOL;
    echo '- retry-migration.bat: Fix current migration issues' . PHP_EOL;
    echo '- fix-migration-errors.bat: Step-by-step migration fix' . PHP_EOL;
    
} catch (Exception \$e) {
    echo 'Diagnosis report error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
pause
