<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UangTixBalance;

class InitializeUangTixBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'uangtix:init-balances {--force : Force create balances for all users}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize UangTix balances for all users who don\'t have one';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing UangTix balances...');

        $force = $this->option('force');
        
        if ($force) {
            $this->warn('Force mode: Creating balances for ALL users');
            $users = User::all();
        } else {
            // Only get users without UangTix balance
            $users = User::whereDoesntHave('uangTixBalance')->get();
        }

        if ($users->isEmpty()) {
            $this->info('All users already have UangTix balances!');
            return;
        }

        $this->info("Found {$users->count()} users without UangTix balance");

        $bar = $this->output->createProgressBar($users->count());
        $bar->start();

        $created = 0;
        foreach ($users as $user) {
            try {
                if ($force) {
                    // Delete existing balance if force mode
                    UangTixBalance::where('user_id', $user->id)->delete();
                }

                UangTixBalance::create([
                    'user_id' => $user->id,
                    'balance' => 0,
                    'total_earned' => 0,
                    'total_spent' => 0,
                    'total_deposited' => 0,
                    'total_withdrawn' => 0,
                    'is_active' => true,
                ]);

                $created++;
            } catch (\Exception $e) {
                $this->error("Failed to create balance for user {$user->id}: " . $e->getMessage());
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Successfully created {$created} UangTix balances!");

        // Show summary
        $totalBalances = UangTixBalance::count();
        $totalUsers = User::count();
        
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Users', $totalUsers],
                ['Total UangTix Balances', $totalBalances],
                ['Coverage', round(($totalBalances / $totalUsers) * 100, 2) . '%'],
            ]
        );
    }
}
