@echo off
echo 🔄 Restarting Laragon Services...
echo ================================

echo.
echo 🛑 Stopping Apache...
net stop "Apache2.4" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Apache stopped successfully
) else (
    echo ℹ️  Apache was not running or could not be stopped
)

echo.
echo 🛑 Stopping MySQL...
net stop "mysql" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL stopped successfully
) else (
    echo ℹ️  MySQL was not running or could not be stopped
)

echo.
echo ⏳ Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Starting MySQL...
net start "mysql" 2>nul
if %errorlevel% equ 0 (
    echo ✅ MySQL started successfully
) else (
    echo ❌ Failed to start MySQL
)

echo.
echo 🚀 Starting Apache...
net start "Apache2.4" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Apache started successfully
) else (
    echo ❌ Failed to start Apache
)

echo.
echo 🧪 Testing PHP configuration...
php -v
if %errorlevel% equ 0 (
    echo ✅ PHP is working correctly
) else (
    echo ❌ PHP configuration has issues
)

echo.
echo ✅ Laragon services restart completed!
echo 💡 If you still see warnings, try restarting Laragon completely.
pause
