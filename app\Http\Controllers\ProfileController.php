<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the profile page
     */
    public function show()
    {
        $user = Auth::user();
        
        // Get user preferences (you can add these fields to users table or create a separate preferences table)
        $emailNotification = $user->email_notifications ?? true; // Default to true
        $pushNotification = $user->push_notifications ?? true;
        $smsNotification = $user->sms_notifications ?? false;
        
        return view('pages.profile', compact(
            'user',
            'emailNotification',
            'pushNotification', 
            'smsNotification'
        ));
    }

    /**
     * Update profile information
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'birth_date' => $request->birth_date,
            'gender' => $request->gender,
            'address' => $request->address,
        ]);

        return redirect()->route('profile')->with('success', 'Profil berhasil diperbarui!');
    }

    /**
     * Update password
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = Auth::user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Password saat ini tidak benar.']);
        }

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('profile')->with('success', 'Password berhasil diperbarui!');
    }

    /**
     * Update profile photo
     */
    public function updatePhoto(Request $request)
    {
        $request->validate([
            'profile_photo' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $user = Auth::user();

        // Delete old photo if exists
        if ($user->avatar && Storage::exists('public/' . $user->avatar)) {
            Storage::delete('public/' . $user->avatar);
        }

        // Store new photo
        $path = $request->file('profile_photo')->store('users/avatars', 'public');

        $user->update([
            'avatar' => $path,
        ]);

        return redirect()->route('profile')->with('success', 'Foto profil berhasil diperbarui!');
    }

    /**
     * Toggle email notification setting
     */
    public function toggleEmailNotification(Request $request)
    {
        $user = Auth::user();
        
        $user->update([
            'email_notifications' => !($user->email_notifications ?? true),
        ]);

        $status = $user->email_notifications ? 'diaktifkan' : 'dinonaktifkan';
        
        return response()->json([
            'success' => true,
            'message' => "Notifikasi email berhasil {$status}",
            'emailNotification' => $user->email_notifications
        ]);
    }

    /**
     * Toggle push notification setting
     */
    public function togglePushNotification(Request $request)
    {
        $user = Auth::user();
        
        $user->update([
            'push_notifications' => !($user->push_notifications ?? true),
        ]);

        $status = $user->push_notifications ? 'diaktifkan' : 'dinonaktifkan';
        
        return response()->json([
            'success' => true,
            'message' => "Notifikasi push berhasil {$status}",
            'pushNotification' => $user->push_notifications
        ]);
    }

    /**
     * Toggle SMS notification setting
     */
    public function toggleSmsNotification(Request $request)
    {
        $user = Auth::user();
        
        $user->update([
            'sms_notifications' => !($user->sms_notifications ?? false),
        ]);

        $status = $user->sms_notifications ? 'diaktifkan' : 'dinonaktifkan';
        
        return response()->json([
            'success' => true,
            'message' => "Notifikasi SMS berhasil {$status}",
            'smsNotification' => $user->sms_notifications
        ]);
    }

    /**
     * Delete account
     */
    public function deleteAccount(Request $request)
    {
        $request->validate([
            'password' => 'required',
        ]);

        $user = Auth::user();

        // Check if password is correct
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'Password tidak benar.']);
        }

        // Delete user's avatar if exists
        if ($user->avatar && Storage::exists('public/' . $user->avatar)) {
            Storage::delete('public/' . $user->avatar);
        }

        // Logout user
        Auth::logout();

        // Delete user account
        $user->delete();

        return redirect()->route('home')->with('success', 'Akun berhasil dihapus.');
    }
}
