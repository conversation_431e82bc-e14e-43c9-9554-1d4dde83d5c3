@extends('layouts.app')

@section('title', 'Detail Saldo UangTix')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-wallet text-warning me-2"></i>
                Detail Saldo UangTix
            </h1>
            <p class="text-muted">Informasi lengkap tentang saldo dan transaksi UangTix Anda</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('uangtix.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="{{ route('uangtix.transactions') }}" class="btn btn-primary">
                <i class="fas fa-history"></i> Riwayat Transaksi
            </a>
        </div>
    </div>

    <!-- Balance Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-warning text-white shadow-lg">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="text-white mb-1">{{ $balance->formatted_balance }}</h2>
                            <p class="text-white-75 mb-2">Saldo UangTix Anda</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-white-50 d-block">Setara dengan:</small>
                                    <strong class="text-white">{{ $balance->formatted_balance_idr }}</strong>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-white-50 d-block">Kurs saat ini:</small>
                                    <strong class="text-white">{{ $exchangeRate->formatted_rates['uangtix_to_idr'] }}</strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex flex-column align-items-end">
                                <i class="fas fa-wallet fa-4x text-white-25 mb-2"></i>
                                <span class="badge badge-{{ $balance->is_active ? 'success' : 'danger' }} badge-lg">
                                    {{ $balance->is_active ? 'Akun Aktif' : 'Akun Nonaktif' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Earned
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                UTX {{ number_format($balance->total_earned, 0, ',', '.') }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ $balance->formatted_earned_idr }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Total Spent
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                UTX {{ number_format($balance->total_spent, 0, ',', '.') }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ $balance->formatted_spent_idr }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Deposited
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                UTX {{ number_format($balance->total_deposited, 0, ',', '.') }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ $balance->formatted_deposited_idr }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Withdrawn
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                UTX {{ number_format($balance->total_withdrawn, 0, ',', '.') }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ $balance->formatted_withdrawn_idr }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-minus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Statistics -->
    @if(isset($monthlyStats) && !empty($monthlyStats))
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statistik Bulanan
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($monthlyStats as $month => $stats)
                        <div class="col-md-4 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="font-weight-bold text-primary">{{ $month }}</h6>
                                <div class="small">
                                    <div class="d-flex justify-content-between">
                                        <span>Transaksi:</span>
                                        <span class="font-weight-bold">{{ $stats['transactions'] }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Total In:</span>
                                        <span class="text-success">+{{ number_format($stats['total_in'], 0, ',', '.') }} UTX</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Total Out:</span>
                                        <span class="text-danger">-{{ number_format($stats['total_out'], 0, ',', '.') }} UTX</span>
                                    </div>
                                    <hr class="my-2">
                                    <div class="d-flex justify-content-between">
                                        <span class="font-weight-bold">Net:</span>
                                        <span class="font-weight-bold text-{{ $stats['net'] >= 0 ? 'success' : 'danger' }}">
                                            {{ $stats['net'] >= 0 ? '+' : '' }}{{ number_format($stats['net'], 0, ',', '.') }} UTX
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Account Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Informasi Akun
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <tr>
                                <td class="font-weight-bold">Status Akun:</td>
                                <td>
                                    <span class="badge badge-{{ $balance->is_active ? 'success' : 'danger' }}">
                                        {{ $balance->is_active ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Dibuat:</td>
                                <td>{{ $balance->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Terakhir Update:</td>
                                <td>{{ $balance->updated_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">ID Akun:</td>
                                <td><code>{{ $balance->id }}</code></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exchange-alt me-2"></i>
                        Informasi Kurs
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <tr>
                                <td class="font-weight-bold">IDR ke UangTix:</td>
                                <td>{{ $exchangeRate->formatted_rates['idr_to_uangtix'] }}</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">UangTix ke IDR:</td>
                                <td>{{ $exchangeRate->formatted_rates['uangtix_to_idr'] }}</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Min Deposit:</td>
                                <td>{{ $exchangeRate->formatted_limits['min_deposit'] }}</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Min Penarikan:</td>
                                <td>{{ $exchangeRate->formatted_limits['min_withdrawal'] }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
