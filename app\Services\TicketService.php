<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\Event;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Str;
use Carbon\Carbon;

class TicketService
{
    /**
     * Generate unique ticket number
     */
    public function generateTicketNumber(): string
    {
        do {
            $ticketNumber = 'TIK-' . strtoupper(Str::random(8));
        } while (Ticket::where('ticket_number', $ticketNumber)->exists());

        return $ticketNumber;
    }

    /**
     * Create ticket for an order
     */
    public function createTicket(Order $order, array $attendeeData = []): Ticket
    {
        $ticketData = [
            'ticket_number' => $this->generateTicketNumber(),
            'qr_code' => $this->generateQRCode(),
            'event_id' => $order->event_id,
            'buyer_id' => $order->user_id,
            'order_id' => $order->id,
            'attendee_name' => $attendeeData['name'] ?? $order->customer_name,
            'attendee_email' => $attendeeData['email'] ?? $order->customer_email,
            'attendee_phone' => $attendeeData['phone'] ?? $order->customer_phone,
            'price' => $order->unit_price,
            'admin_fee' => $order->admin_fee / $order->quantity, // Distribute admin fee
            'total_paid' => $order->unit_price + ($order->admin_fee / $order->quantity),
            'status' => 'active',
            'purchased_at' => now(),
        ];

        return Ticket::create($ticketData);
    }

    /**
     * Create multiple tickets for an order
     */
    public function createTicketsForOrder(Order $order, array $attendeesData = []): array
    {
        $tickets = [];
        
        for ($i = 0; $i < $order->quantity; $i++) {
            $attendeeData = $attendeesData[$i] ?? [];
            $tickets[] = $this->createTicket($order, $attendeeData);
        }

        return $tickets;
    }

    /**
     * Generate QR code data
     */
    public function generateQRCode(): string
    {
        return Str::uuid()->toString();
    }

    /**
     * Validate ticket
     */
    public function validateTicket(string $ticketNumber, ?User $validator = null): array
    {
        $ticket = Ticket::where('ticket_number', $ticketNumber)->first();

        if (!$ticket) {
            return [
                'success' => false,
                'message' => 'Tiket tidak ditemukan',
                'ticket' => null
            ];
        }

        if ($ticket->status === 'used') {
            return [
                'success' => false,
                'message' => 'Tiket sudah digunakan pada ' . $ticket->used_at->format('d M Y H:i'),
                'ticket' => $ticket
            ];
        }

        if ($ticket->status === 'cancelled') {
            return [
                'success' => false,
                'message' => 'Tiket telah dibatalkan',
                'ticket' => $ticket
            ];
        }

        // Check if event is still valid
        $event = $ticket->event;
        if ($event->end_date < now()) {
            return [
                'success' => false,
                'message' => 'Event sudah berakhir',
                'ticket' => $ticket
            ];
        }

        // Mark ticket as used
        $ticket->update([
            'status' => 'used',
            'used_at' => now(),
            'validated_by' => $validator?->id,
        ]);

        return [
            'success' => true,
            'message' => 'Tiket berhasil divalidasi',
            'ticket' => $ticket->fresh()
        ];
    }

    /**
     * Cancel ticket
     */
    public function cancelTicket(Ticket $ticket, string $reason = null): bool
    {
        if ($ticket->status === 'used') {
            return false; // Cannot cancel used ticket
        }

        $ticket->update([
            'status' => 'cancelled',
            'notes' => $reason,
        ]);

        // Update event capacity
        $event = $ticket->event;
        $event->increment('available_capacity');

        return true;
    }

    /**
     * Get ticket statistics for an event
     */
    public function getEventTicketStats(Event $event): array
    {
        $tickets = $event->tickets();

        return [
            'total' => $tickets->count(),
            'active' => $tickets->where('status', 'active')->count(),
            'used' => $tickets->where('status', 'used')->count(),
            'cancelled' => $tickets->where('status', 'cancelled')->count(),
            'revenue' => $tickets->where('status', 'used')->sum('price'),
            'capacity_utilization' => $event->total_capacity > 0 ? 
                ($tickets->where('status', 'used')->count() / $event->total_capacity) * 100 : 0,
        ];
    }

    /**
     * Get user ticket history
     */
    public function getUserTicketHistory(User $user): array
    {
        $tickets = $user->tickets()->with(['event', 'order'])->latest()->get();

        return [
            'total_tickets' => $tickets->count(),
            'active_tickets' => $tickets->where('status', 'active')->count(),
            'used_tickets' => $tickets->where('status', 'used')->count(),
            'cancelled_tickets' => $tickets->where('status', 'cancelled')->count(),
            'total_spent' => $tickets->sum('total_paid'),
            'tickets' => $tickets,
        ];
    }

    /**
     * Check if user can purchase ticket for event
     */
    public function canUserPurchaseTicket(User $user, Event $event, int $quantity = 1): array
    {
        // Check if event is available for purchase
        if ($event->status !== 'published') {
            return [
                'can_purchase' => false,
                'reason' => 'Event tidak tersedia untuk pembelian'
            ];
        }

        // Check if event has started
        if ($event->start_date <= now()) {
            return [
                'can_purchase' => false,
                'reason' => 'Event sudah dimulai'
            ];
        }

        // Check capacity
        if ($event->available_capacity < $quantity) {
            return [
                'can_purchase' => false,
                'reason' => 'Kapasitas tidak mencukupi'
            ];
        }

        // Check if user already has ticket for this event (if single ticket per user)
        $existingTickets = $user->tickets()->where('event_id', $event->id)
            ->whereIn('status', ['active', 'used'])->count();

        if ($existingTickets > 0 && $event->max_tickets_per_user === 1) {
            return [
                'can_purchase' => false,
                'reason' => 'Anda sudah memiliki tiket untuk event ini'
            ];
        }

        return [
            'can_purchase' => true,
            'reason' => null
        ];
    }

    /**
     * Generate ticket download token
     */
    public function generateDownloadToken(Ticket $ticket): string
    {
        $token = Str::random(32);
        
        $ticket->update([
            'download_token' => $token,
            'download_count' => $ticket->download_count + 1,
            'last_downloaded_at' => now(),
        ]);

        return $token;
    }
}
