@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">

    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    Pembayaran
                </h1>
                <p class="text-lg text-gray-600" data-aos="fade-up" data-aos-delay="100">
                    Selesaikan pembayaran untuk mengaktifkan tiket Anda
                </p>
            </div>

            <!-- Timer -->
            <div class="bg-orange-50 border border-orange-200 rounded-xl p-4 mb-8" data-aos="fade-up" data-aos-delay="200">
                <div class="flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="text-orange-700 font-semibold">
                        Selesaikan pembayaran dalam:
                        <span id="countdown" class="font-bold text-orange-800"></span>
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- Payment Content -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Payment Methods -->
                <div class="lg:col-span-2 space-y-6">

                    <!-- Order Summary -->
                    <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">Detail Pesanan</h2>
                        <div class="flex items-start space-x-4">
                            <img src="{{ $order->event->poster_url }}"
                                 alt="{{ $order->event->title }}"
                                 class="w-20 h-20 object-cover rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 mb-2">{{ $order->event->title }}</h3>
                                <div class="space-y-1 text-sm text-gray-600">
                                    <div>{{ $order->event->start_date->format('d M Y, H:i') }} WIB</div>
                                    <div>{{ $order->event->venue_name }}, {{ $order->event->city }}</div>
                                    <div>{{ $order->quantity }} tiket × {{ $order->customer_name }}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-primary">
                                    Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    Order #{{ $order->order_number }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method Selection -->
                    <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="100">
                        <h2 class="text-xl font-bold text-gray-900 mb-6">Pilih Metode Pembayaran</h2>

                        <!-- Popular Methods -->
                        <div class="mb-6">
                            <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                </svg>
                                Metode Populer
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                @foreach($paymentMethods as $key => $method)
                                    @if($method['popular'] ?? false)
                                        <div class="payment-method-card" data-method="{{ $key }}">
                                            <div class="relative p-4 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-primary/50 transition-all duration-200 group">
                                                @if($method['popular'])
                                                    <div class="absolute -top-2 -right-2">
                                                        <span class="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                                                            Populer
                                                        </span>
                                                    </div>
                                                @endif
                                                <div class="flex items-center space-x-3">
                                                    <div class="w-10 h-10 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-lg flex items-center justify-center">
                                                        @include('components.payment-icon', ['type' => $method['icon']])
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="font-semibold text-gray-900 group-hover:text-primary transition-colors">
                                                            {{ $method['name'] }}
                                                        </h4>
                                                        <p class="text-xs text-gray-600">{{ $method['processing_time'] }}</p>
                                                    </div>
                                                    <div class="text-right">
                                                        @if($method['fee'] == 0)
                                                            <span class="text-xs text-green-600 font-semibold">Gratis</span>
                                                        @else
                                                            <span class="text-xs text-gray-500">
                                                                {{ is_numeric($method['fee']) && $method['fee'] < 100 ? $method['fee'] . '%' : 'Rp ' . number_format($method['fee'], 0, ',', '.') }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>

                        <!-- All Methods -->
                        <div class="mb-6">
                            <h3 class="text-sm font-semibold text-gray-700 mb-3">Semua Metode Pembayaran</h3>
                        </div>

                        <form method="POST" action="{{ route('orders.process-payment', $order) }}" x-data="paymentForm()">
                            @csrf

                            <div class="space-y-3">
                                @foreach($paymentMethods as $key => $method)
                                    <label class="block">
                                        <input type="radio"
                                               name="payment_method"
                                               value="{{ $key }}"
                                               x-model="selectedMethod"
                                               class="sr-only peer">
                                        <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="w-12 h-12 bg-gray-50 rounded-lg flex items-center justify-center peer-checked:bg-primary/10">
                                                        @include('components.payment-icon', ['type' => $method['icon']])
                                                    </div>
                                                    <div class="flex-1">
                                                        <div class="flex items-center space-x-2">
                                                            <h3 class="font-semibold text-gray-900">{{ $method['name'] }}</h3>
                                                            @if($method['popular'] ?? false)
                                                                <span class="bg-orange-100 text-orange-600 text-xs px-2 py-0.5 rounded-full font-semibold">
                                                                    Populer
                                                                </span>
                                                            @endif
                                                        </div>
                                                        <p class="text-sm text-gray-600">{{ $method['description'] }}</p>
                                                        <div class="flex items-center space-x-4 mt-1">
                                                            <span class="text-xs text-gray-500">
                                                                <i data-lucide="clock" class="w-3 h-3 inline mr-1"></i>
                                                                {{ $method['processing_time'] }}
                                                            </span>
                                                            @if($method['fee'] == 0)
                                                                <span class="text-xs text-green-600 font-semibold">
                                                                    <i data-lucide="check-circle" class="w-3 h-3 inline mr-1"></i>
                                                                    Gratis
                                                                </span>
                                                            @else
                                                                <span class="text-xs text-gray-500">
                                                                    <i data-lucide="credit-card" class="w-3 h-3 inline mr-1"></i>
                                                                    Biaya: {{ is_numeric($method['fee']) && $method['fee'] < 100 ? $method['fee'] . '%' : 'Rp ' . number_format($method['fee'], 0, ',', '.') }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-3">
                                                    @if($method['fee'] == 0)
                                                        <div class="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-semibold">
                                                            Gratis
                                                        </div>
                                                    @endif
                                                    <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                        <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                @endforeach
                            </div>

                            <!-- Payment Details -->
                            <div x-show="selectedMethod" class="mt-6">

                                <!-- Bank Transfer Details -->
                                <div x-show="selectedMethod === 'bank_transfer'" class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-blue-900 mb-4 flex items-center">
                                        <i data-lucide="building-2" class="w-5 h-5 mr-2"></i>
                                        Informasi Transfer Bank
                                    </h4>
                                    <div class="space-y-4">
                                        @foreach($paymentMethods['bank_transfer']['banks'] as $bank)
                                            <div class="bg-white rounded-lg p-4 border border-blue-100">
                                                <div class="flex justify-between items-center">
                                                    <div class="flex items-center space-x-3">
                                                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                            <span class="text-blue-600 font-bold text-sm">{{ $bank['code'] }}</span>
                                                        </div>
                                                        <div>
                                                            <div class="font-semibold text-gray-900">{{ $bank['name'] }}</div>
                                                            <div class="text-sm text-gray-600">{{ $bank['holder'] }}</div>
                                                        </div>
                                                    </div>
                                                    <div class="text-right">
                                                        <div class="font-mono text-lg font-bold text-gray-900">{{ $bank['account'] }}</div>
                                                        <button type="button" onclick="copyToClipboard('{{ $bank['account'] }}')"
                                                                class="text-xs text-primary hover:text-accent flex items-center">
                                                            <i data-lucide="copy" class="w-3 h-3 mr-1"></i>
                                                            Salin Nomor
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="mt-4 p-3 bg-blue-100 rounded-lg">
                                        <p class="text-sm text-blue-800">
                                            <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                                            Transfer dengan nominal <strong>persis</strong> sesuai total pembayaran untuk konfirmasi otomatis.
                                        </p>
                                    </div>
                                    <input type="hidden" name="payment_details[bank]" value="">
                                </div>

                                <!-- E-Wallet Details -->
                                <div x-show="selectedMethod === 'e_wallet'" class="bg-green-50 border border-green-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-green-900 mb-4 flex items-center">
                                        <i data-lucide="smartphone" class="w-5 h-5 mr-2"></i>
                                        Pilih E-Wallet
                                    </h4>
                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                        @foreach($paymentMethods['e_wallet']['providers'] as $provider)
                                            <label class="block">
                                                <input type="radio"
                                                       name="payment_details[provider]"
                                                       value="{{ $provider['code'] }}"
                                                       class="sr-only peer">
                                                <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-500 peer-checked:bg-green-50 text-center transition-all duration-200 hover:border-green-300">
                                                    <div class="w-8 h-8 mx-auto mb-2 bg-gray-100 rounded-lg flex items-center justify-center">
                                                        <span class="text-xs font-bold text-gray-600">{{ substr($provider['name'], 0, 2) }}</span>
                                                    </div>
                                                    <div class="font-semibold text-gray-900 text-sm">{{ $provider['name'] }}</div>
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        Min: Rp {{ number_format($provider['min_amount'], 0, ',', '.') }}
                                                    </div>
                                                </div>
                                            </label>
                                        @endforeach
                                    </div>
                                    <div class="mt-4 p-3 bg-green-100 rounded-lg">
                                        <p class="text-sm text-green-800">
                                            <i data-lucide="zap" class="w-4 h-4 inline mr-1"></i>
                                            Pembayaran akan diproses secara instan setelah konfirmasi.
                                        </p>
                                    </div>
                                </div>

                                <!-- QRIS Details -->
                                <div x-show="selectedMethod === 'qris'" class="bg-indigo-50 border border-indigo-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-indigo-900 mb-4 flex items-center">
                                        <i data-lucide="qr-code" class="w-5 h-5 mr-2"></i>
                                        Pembayaran QRIS
                                    </h4>
                                    <div class="text-center">
                                        <div class="w-48 h-48 mx-auto bg-white rounded-lg border-2 border-indigo-200 flex items-center justify-center mb-4">
                                            <div class="text-center">
                                                <i data-lucide="qr-code" class="w-16 h-16 mx-auto text-indigo-400 mb-2"></i>
                                                <p class="text-sm text-gray-600">QR Code akan muncul<br>setelah konfirmasi</p>
                                            </div>
                                        </div>
                                        <div class="space-y-2 text-sm text-indigo-800">
                                            <p class="flex items-center justify-center">
                                                <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                                                Scan dengan aplikasi bank/e-wallet apapun
                                            </p>
                                            <p class="flex items-center justify-center">
                                                <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                                                Konfirmasi pembayaran otomatis
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Virtual Account Details -->
                                <div x-show="selectedMethod === 'virtual_account'" class="bg-purple-50 border border-purple-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-purple-900 mb-4 flex items-center">
                                        <i data-lucide="credit-card" class="w-5 h-5 mr-2"></i>
                                        Virtual Account
                                    </h4>
                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3 mb-4">
                                        @foreach($paymentMethods['virtual_account']['banks'] as $bank)
                                            <label class="block">
                                                <input type="radio"
                                                       name="payment_details[va_bank]"
                                                       value="{{ $bank }}"
                                                       class="sr-only peer">
                                                <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-purple-500 peer-checked:bg-purple-50 text-center transition-all duration-200">
                                                    <div class="font-semibold text-gray-900">{{ $bank }}</div>
                                                </div>
                                            </label>
                                        @endforeach
                                    </div>
                                    <div class="bg-purple-100 rounded-lg p-3">
                                        <p class="text-sm text-purple-800">
                                            <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                                            Nomor Virtual Account akan diberikan setelah konfirmasi pesanan.
                                        </p>
                                    </div>
                                </div>

                                <!-- Credit Card Details -->
                                <div x-show="selectedMethod === 'credit_card'" class="bg-purple-50 border border-purple-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-purple-900 mb-4 flex items-center">
                                        <i data-lucide="credit-card" class="w-5 h-5 mr-2"></i>
                                        Informasi Kartu Kredit/Debit
                                    </h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Nomor Kartu</label>
                                            <input type="text"
                                                   name="payment_details[card_number]"
                                                   placeholder="1234 5678 9012 3456"
                                                   maxlength="19"
                                                   x-mask="9999 9999 9999 9999"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Kadaluarsa</label>
                                            <input type="text"
                                                   name="payment_details[expiry]"
                                                   placeholder="MM/YY"
                                                   maxlength="5"
                                                   x-mask="99/99"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">CVV</label>
                                            <input type="text"
                                                   name="payment_details[cvv]"
                                                   placeholder="123"
                                                   maxlength="4"
                                                   x-mask="9999"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20">
                                        </div>
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Nama Pemegang Kartu</label>
                                            <input type="text"
                                                   name="payment_details[card_holder]"
                                                   placeholder="Nama sesuai kartu"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20">
                                        </div>
                                    </div>
                                    <div class="mt-4 flex items-center space-x-4">
                                        <div class="flex items-center space-x-2">
                                            <img src="/images/cards/visa.png" alt="Visa" class="h-6">
                                            <img src="/images/cards/mastercard.png" alt="Mastercard" class="h-6">
                                            <img src="/images/cards/jcb.png" alt="JCB" class="h-6">
                                        </div>
                                        <div class="flex items-center text-xs text-purple-700">
                                            <i data-lucide="shield-check" class="w-4 h-4 mr-1"></i>
                                            Dilindungi 3D Secure
                                        </div>
                                    </div>
                                </div>

                                <!-- Cash Payment Details -->
                                <div x-show="selectedMethod === 'cash'" class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                                    <h4 class="font-semibold text-yellow-900 mb-4 flex items-center">
                                        <i data-lucide="banknote" class="w-5 h-5 mr-2"></i>
                                        Pembayaran di Tempat
                                    </h4>
                                    <div class="space-y-4">
                                        <div class="flex items-start space-x-3">
                                            <div class="w-8 h-8 bg-yellow-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                                <i data-lucide="info" class="w-4 h-4 text-yellow-700"></i>
                                            </div>
                                            <div class="text-sm text-yellow-800">
                                                <p class="font-semibold mb-2">Ketentuan Pembayaran di Tempat:</p>
                                                <ul class="space-y-2">
                                                    @foreach($paymentMethods['cash']['requirements'] as $requirement)
                                                        <li class="flex items-start space-x-2">
                                                            <i data-lucide="check" class="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0"></i>
                                                            <span>{{ $requirement }}</span>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="bg-yellow-100 rounded-lg p-3">
                                            <p class="text-sm text-yellow-800 font-semibold">
                                                <i data-lucide="alert-triangle" class="w-4 h-4 inline mr-1"></i>
                                                Penting: Konfirmasi kehadiran Anda H-1 melalui WhatsApp atau email.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="mt-8">
                                <button type="submit"
                                        :disabled="!selectedMethod || processing"
                                        @click="processing = true"
                                        class="w-full bg-gradient-to-r from-primary to-secondary text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span x-show="!processing">Bayar Sekarang</span>
                                    <span x-show="processing" class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Memproses Pembayaran...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Order Summary Sidebar -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24">
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                            <h3 class="text-lg font-bold text-gray-900 mb-4">Ringkasan Pembayaran</h3>

                            <div class="space-y-3 mb-6">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal</span>
                                    <span>Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Biaya admin</span>
                                    <span>Rp {{ number_format($order->admin_fee, 0, ',', '.') }}</span>
                                </div>
                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between text-green-600">
                                        <span>Diskon</span>
                                        <span>-Rp {{ number_format($order->discount_amount, 0, ',', '.') }}</span>
                                    </div>
                                @endif
                                <div class="border-t border-gray-200 pt-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-gray-900">Total</span>
                                        <span class="text-lg font-bold text-primary">
                                            Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Info -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                    </svg>
                                    Pembayaran aman dengan enkripsi SSL
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script src="https://unpkg.com/@alpinejs/mask@3.x.x/dist/cdn.min.js"></script>
<script>
function paymentForm() {
    return {
        selectedMethod: '',
        processing: false,

        init() {
            // Auto-select popular method if only one available
            const popularMethods = document.querySelectorAll('.payment-method-card');
            if (popularMethods.length === 1) {
                this.selectedMethod = popularMethods[0].dataset.method;
            }
        },

        selectMethod(method) {
            this.selectedMethod = method;
            // Scroll to payment details
            setTimeout(() => {
                const detailsSection = document.querySelector('[x-show="selectedMethod"]');
                if (detailsSection) {
                    detailsSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            }, 100);
        },

        calculateFee(method) {
            const methods = @json($paymentMethods);
            const selectedMethodData = methods[method];
            if (!selectedMethodData) return 0;

            const orderTotal = {{ $order->total_amount }};
            const fee = selectedMethodData.fee;

            if (typeof fee === 'number') {
                if (fee < 100) {
                    // Percentage fee
                    return Math.round(orderTotal * (fee / 100));
                } else {
                    // Flat fee
                    return fee;
                }
            }
            return 0;
        },

        getTotalWithFee() {
            if (!this.selectedMethod) return {{ $order->total_amount }};
            const fee = this.calculateFee(this.selectedMethod);
            return {{ $order->total_amount }} + fee;
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
            }).format(amount);
        }
    }
}

// Countdown timer with enhanced features
function startCountdown() {
    const expiresAt = new Date('{{ $order->expires_at }}').getTime();
    const countdownElement = document.getElementById('countdown');

    const timer = setInterval(function() {
        const now = new Date().getTime();
        const distance = expiresAt - now;

        if (distance < 0) {
            clearInterval(timer);
            countdownElement.innerHTML = "EXPIRED";
            countdownElement.classList.add('text-red-600', 'font-bold');

            // Show expiration modal
            showExpirationModal();
            return;
        }

        const hours = Math.floor(distance / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        let timeString = '';
        if (hours > 0) {
            timeString = hours + "h " + minutes + "m " + seconds + "s";
        } else {
            timeString = minutes + "m " + seconds + "s";
        }

        countdownElement.innerHTML = timeString;

        // Change color when time is running out
        if (distance < 5 * 60 * 1000) { // Less than 5 minutes
            countdownElement.classList.add('text-red-600', 'animate-pulse');
        } else if (distance < 15 * 60 * 1000) { // Less than 15 minutes
            countdownElement.classList.add('text-orange-600');
        }
    }, 1000);
}

function showExpirationModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-2xl p-8 max-w-md mx-4 text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="clock-x" class="w-8 h-8 text-red-600"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Waktu Pembayaran Habis</h3>
            <p class="text-gray-600 mb-6">Pesanan Anda telah kedaluwarsa. Silakan buat pesanan baru untuk melanjutkan.</p>
            <button onclick="window.location.href='{{ route('tickets.show', $order->event->slug) }}'"
                    class="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-primary-dark transition-colors">
                Buat Pesanan Baru
            </button>
        </div>
    `;
    document.body.appendChild(modal);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('Nomor rekening berhasil disalin!', 'success');

        // Visual feedback
        event.target.innerHTML = '<i data-lucide="check" class="w-3 h-3 mr-1"></i>Tersalin!';
        event.target.classList.add('text-green-600');

        setTimeout(() => {
            event.target.innerHTML = '<i data-lucide="copy" class="w-3 h-3 mr-1"></i>Salin Nomor';
            event.target.classList.remove('text-green-600');
        }, 2000);
    }).catch(function() {
        showNotification('Gagal menyalin. Silakan salin manual.', 'error');
    });
}

// Enhanced notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info'}" class="w-5 h-5"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Payment method card interactions
document.addEventListener('DOMContentLoaded', function() {
    startCountdown();

    // Add click handlers for popular payment method cards
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.addEventListener('click', function() {
            const method = this.dataset.method;
            const radioButton = document.querySelector(`input[value="${method}"]`);
            if (radioButton) {
                radioButton.checked = true;
                radioButton.dispatchEvent(new Event('change'));
            }
        });
    });

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
            if (!selectedMethod) {
                e.preventDefault();
                showNotification('Silakan pilih metode pembayaran terlebih dahulu.', 'error');
                return;
            }

            // Additional validation based on payment method
            if (selectedMethod.value === 'credit_card') {
                const cardNumber = document.querySelector('input[name="payment_details[card_number]"]').value;
                const expiry = document.querySelector('input[name="payment_details[expiry]"]').value;
                const cvv = document.querySelector('input[name="payment_details[cvv]"]').value;

                if (!cardNumber || !expiry || !cvv) {
                    e.preventDefault();
                    showNotification('Silakan lengkapi informasi kartu kredit.', 'error');
                    return;
                }
            }

            if (selectedMethod.value === 'e_wallet') {
                const provider = document.querySelector('input[name="payment_details[provider]"]:checked');
                if (!provider) {
                    e.preventDefault();
                    showNotification('Silakan pilih provider e-wallet.', 'error');
                    return;
                }
            }
        });
    }
});
</script>
@endpush
