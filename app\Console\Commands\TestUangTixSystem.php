<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UangTixBalance;
use App\Models\UangTixTransaction;
use App\Models\UangTixRequest;
use App\Models\UangTixExchangeRate;

class TestUangTixSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'uangtix:test-system';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test UangTix system connectivity and data integrity';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing UangTix System...');
        $this->newLine();

        // Test 1: Database Tables
        $this->info('📊 Testing Database Tables:');
        $tables = [
            'uangtix_balances' => UangTixBalance::count(),
            'uangtix_transactions' => UangTixTransaction::count(),
            'uangtix_requests' => UangTixRequest::count(),
            'uangtix_exchange_rates' => UangTixExchangeRate::count(),
        ];

        foreach ($tables as $table => $count) {
            $this->line("  ✓ {$table}: {$count} records");
        }
        $this->newLine();

        // Test 2: User-Balance Relationships
        $this->info('👥 Testing User-Balance Relationships:');
        $totalUsers = User::count();
        $usersWithBalance = User::whereHas('uangTixBalance')->count();
        $this->line("  ✓ Total Users: {$totalUsers}");
        $this->line("  ✓ Users with UangTix Balance: {$usersWithBalance}");
        
        if ($totalUsers === $usersWithBalance) {
            $this->line("  ✅ All users have UangTix balances!");
        } else {
            $this->warn("  ⚠️  Some users missing UangTix balances");
        }
        $this->newLine();

        // Test 3: Exchange Rate
        $this->info('💱 Testing Exchange Rate:');
        $exchangeRate = UangTixExchangeRate::current();
        if ($exchangeRate) {
            $this->line("  ✓ Exchange Rate loaded successfully");
            $this->line("  ✓ IDR to UangTix: {$exchangeRate->rate_idr_to_uangtix}");
            $this->line("  ✓ UangTix to IDR: {$exchangeRate->rate_uangtix_to_idr}");
            $this->line("  ✓ Deposits enabled: " . ($exchangeRate->deposits_enabled ? 'Yes' : 'No'));
            $this->line("  ✓ Withdrawals enabled: " . ($exchangeRate->withdrawals_enabled ? 'Yes' : 'No'));
        } else {
            $this->error("  ❌ Exchange Rate not found!");
        }
        $this->newLine();

        // Test 4: Sample Balance Data
        $this->info('💰 Testing Sample Balance Data:');
        $sampleBalances = UangTixBalance::with('user')->take(3)->get();
        
        foreach ($sampleBalances as $balance) {
            $this->line("  ✓ {$balance->user->name}:");
            $this->line("    - Balance: {$balance->formatted_balance}");
            $this->line("    - Balance IDR: {$balance->formatted_balance_idr}");
            $this->line("    - Total Earned: UTX " . number_format($balance->total_earned, 0, ',', '.'));
            $this->line("    - Status: " . ($balance->is_active ? 'Active' : 'Inactive'));
            $this->line("    - Avatar URL: " . (strlen($balance->user->avatar_url) > 50 ? 'Available' : 'Missing'));
        }
        $this->newLine();

        // Test 5: Admin Query Test
        $this->info('🔍 Testing Admin Query:');
        $adminQuery = UangTixBalance::with(['user'])
            ->where(function($q) {
                $q->where('balance', '>', 0)
                  ->orWhere('total_earned', '>', 0)
                  ->orWhere('total_spent', '>', 0)
                  ->orWhere('total_deposited', '>', 0)
                  ->orWhere('total_withdrawn', '>', 0);
            });

        $adminResults = $adminQuery->get();
        $this->line("  ✓ Admin query returned: {$adminResults->count()} balances");
        
        if ($adminResults->count() > 0) {
            $this->line("  ✅ Admin will see UangTix data!");
        } else {
            $this->warn("  ⚠️  Admin query returned no results");
        }
        $this->newLine();

        // Test 6: Recent Transactions
        $this->info('📈 Testing Recent Transactions:');
        $recentTransactions = UangTixTransaction::with('user')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        foreach ($recentTransactions as $transaction) {
            $this->line("  ✓ {$transaction->transaction_number}: {$transaction->user->name} - {$transaction->formatted_amount} ({$transaction->type})");
        }
        $this->newLine();

        // Test 7: System Health Summary
        $this->info('🏥 System Health Summary:');
        
        $health = [
            'Database Tables' => count($tables) === 4 && array_sum($tables) > 0,
            'User-Balance Coverage' => $totalUsers === $usersWithBalance,
            'Exchange Rate' => $exchangeRate !== null,
            'Admin Query' => $adminResults->count() > 0,
            'Recent Transactions' => $recentTransactions->count() > 0,
        ];

        foreach ($health as $check => $status) {
            $icon = $status ? '✅' : '❌';
            $this->line("  {$icon} {$check}");
        }

        $healthScore = (array_sum($health) / count($health)) * 100;
        $this->newLine();
        
        if ($healthScore === 100) {
            $this->info("🎉 UangTix System Health: {$healthScore}% - EXCELLENT!");
            $this->info("🚀 System is ready for production use!");
        } elseif ($healthScore >= 80) {
            $this->warn("⚠️  UangTix System Health: {$healthScore}% - GOOD with minor issues");
        } else {
            $this->error("❌ UangTix System Health: {$healthScore}% - NEEDS ATTENTION");
        }

        return Command::SUCCESS;
    }
}
