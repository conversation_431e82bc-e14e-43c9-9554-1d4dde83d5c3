<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UangTixBalance;
use App\Models\UangTixTransaction;
use App\Models\UangTixRequest;

class UangTixSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users to create sample data
        $users = User::whereIn('role', ['penjual', 'pembeli'])->take(10)->get();

        if ($users->isEmpty()) {
            $this->command->info('No users found. Creating sample users first...');
            
            // Create sample users if none exist
            $sampleUsers = [
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'role' => 'penjual',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ],
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'role' => 'pembeli',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ],
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'role' => 'penjual',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ],
                [
                    'name' => 'Alice Johnson',
                    'email' => '<EMAIL>',
                    'role' => 'pembeli',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ],
                [
                    'name' => 'Charlie Brown',
                    'email' => '<EMAIL>',
                    'role' => 'penjual',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ],
            ];

            foreach ($sampleUsers as $userData) {
                $users->push(User::create($userData));
            }
        }

        $this->command->info('Creating sample UangTix data...');

        foreach ($users as $user) {
            // Create UangTix balance for each user
            $balance = UangTixBalance::create([
                'user_id' => $user->id,
                'balance' => rand(10000, 500000), // Random balance between 10k - 500k UTX
                'total_earned' => rand(50000, 1000000),
                'total_spent' => rand(10000, 200000),
                'total_deposited' => rand(100000, 800000),
                'total_withdrawn' => rand(5000, 100000),
                'is_active' => true,
            ]);

            // Create some sample transactions
            $transactionTypes = ['deposit', 'purchase', 'transfer_in', 'transfer_out', 'admin_add'];
            
            for ($i = 0; $i < rand(3, 8); $i++) {
                $type = $transactionTypes[array_rand($transactionTypes)];
                $amount = rand(5000, 100000);
                
                // Make some transactions negative (outgoing)
                if (in_array($type, ['purchase', 'transfer_out', 'withdrawal'])) {
                    $amount = -$amount;
                }

                UangTixTransaction::create([
                    'transaction_number' => 'UTX-' . strtoupper(uniqid()),
                    'user_id' => $user->id,
                    'type' => $type,
                    'amount' => $amount,
                    'balance_before' => $balance->balance - $amount,
                    'balance_after' => $balance->balance,
                    'status' => 'completed',
                    'description' => $this->getTransactionDescription($type),
                    'metadata' => [
                        'sample_data' => true,
                        'created_by_seeder' => true,
                    ],
                    'created_at' => now()->subDays(rand(0, 30)),
                ]);
            }
        }

        // Create some sample deposit/withdrawal requests
        $requestUsers = $users->take(3);
        foreach ($requestUsers as $user) {
            // Create a pending deposit request
            UangTixRequest::create([
                'request_number' => 'UTX-DEP-' . strtoupper(uniqid()),
                'user_id' => $user->id,
                'type' => 'deposit',
                'amount_idr' => rand(50000, 500000),
                'amount_uangtix' => rand(50000, 500000), // 1:1 rate
                'fee_amount' => 0, // No deposit fee
                'final_amount' => rand(50000, 500000),
                'status' => 'pending',
                'payment_method' => 'bank_transfer',
                'payment_data' => [
                    'bank_name' => 'BCA',
                    'account_number' => '**********',
                    'account_name' => $user->name,
                ],
                'created_at' => now()->subHours(rand(1, 48)),
            ]);

            // Create a pending withdrawal request
            $withdrawalAmount = rand(20000, 100000);
            $fee = $withdrawalAmount * 0.025; // 2.5% fee
            $finalAmount = $withdrawalAmount - $fee;

            UangTixRequest::create([
                'request_number' => 'UTX-WD-' . strtoupper(uniqid()),
                'user_id' => $user->id,
                'type' => 'withdrawal',
                'amount_idr' => $finalAmount, // IDR amount to be received
                'amount_uangtix' => $withdrawalAmount,
                'fee_amount' => $fee,
                'final_amount' => $finalAmount,
                'status' => 'pending',
                'bank_name' => 'BCA',
                'bank_account_number' => '**********',
                'bank_account_name' => $user->name,
                'created_at' => now()->subHours(rand(1, 24)),
            ]);
        }

        $this->command->info('Sample UangTix data created successfully!');
        $this->command->info('Created balances for ' . $users->count() . ' users');
        $this->command->info('Created sample transactions and requests');
    }

    /**
     * Get transaction description based on type
     */
    private function getTransactionDescription(string $type): string
    {
        return match($type) {
            'deposit' => 'Deposit UangTix dari bank transfer',
            'purchase' => 'Pembelian tiket event',
            'transfer_in' => 'Transfer masuk dari pengguna lain',
            'transfer_out' => 'Transfer keluar ke pengguna lain',
            'admin_add' => 'Penambahan saldo oleh admin',
            'withdrawal' => 'Penarikan UangTix ke rekening bank',
            default => 'Transaksi UangTix'
        };
    }
}
