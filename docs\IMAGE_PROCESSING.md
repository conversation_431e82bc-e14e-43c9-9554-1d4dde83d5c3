# Image Processing Documentation

## Overview

This application uses Intervention Image v3 for image processing with a custom facade and service layer for easier management.

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
# Image Processing Configuration
IMAGE_DRIVER=gd                    # or 'imagick'
IMAGE_CACHE_ENABLED=true
IMAGE_CACHE_LIFETIME=60           # minutes
IMAGE_JPEG_QUALITY=85             # 0-100
IMAGE_PNG_QUALITY=9               # 0-9
IMAGE_WEBP_QUALITY=85             # 0-100
IMAGE_MAX_WIDTH=2048              # pixels
IMAGE_MAX_HEIGHT=2048             # pixels
IMAGE_MAX_FILE_SIZE=5120          # KB
IMAGE_WATERMARK_ENABLED=false
```

### Configuration File

The configuration is stored in `config/image.php` with predefined sizes for different image types:

- **Thumbnail**: 150x150px
- **Small**: 300x300px
- **Medium**: 600x600px
- **Large**: 1200x1200px
- **Poster**: 800x600px
- **Gallery**: 1200x800px
- **Avatar**: 200x200px

## Usage

### Using the Image Facade

```php
use App\Facades\Image;

// Read an image
$image = Image::read($file);

// Resize image
$image->scaleDown(800, 600);

// Convert to JPEG
$jpeg = $image->toJpeg(85);

// Save to storage
Storage::disk('public')->put('path/image.jpg', $jpeg);
```

### Using the ImageService

The `ImageService` provides convenient methods for common image operations:

#### Basic Upload

```php
use App\Services\ImageService;

$imageService = app(ImageService::class);

// Upload with custom settings
$path = $imageService->uploadImage(
    file: $uploadedFile,
    directory: 'uploads',
    maxWidth: 1200,
    maxHeight: 800,
    quality: 85,
    prefix: 'img'
);
```

#### Predefined Upload Methods

```php
// Upload poster (800x600, 85% quality)
$posterPath = $imageService->uploadPoster($file);

// Upload gallery image (1200x800, 85% quality)
$galleryPath = $imageService->uploadGallery($file);

// Upload avatar (200x200, 90% quality)
$avatarPath = $imageService->uploadAvatar($file);

// Upload thumbnail (150x150, 80% quality)
$thumbPath = $imageService->uploadThumbnail($file);
```

#### Multiple Sizes

```php
$sizes = [
    'small' => ['width' => 300, 'height' => 300, 'quality' => 80],
    'medium' => ['width' => 600, 'height' => 600, 'quality' => 85],
    'large' => ['width' => 1200, 'height' => 1200, 'quality' => 90],
];

$paths = $imageService->createMultipleSizes($file, 'products', $sizes);
// Returns: ['small' => 'path/to/small.jpg', 'medium' => 'path/to/medium.jpg', ...]
```

#### Image Validation

```php
$errors = $imageService->validateImage($file, [
    'max_size' => 2048, // KB
    'allowed_types' => ['jpeg', 'jpg', 'png'],
    'max_width' => 1920,
    'max_height' => 1080,
]);

if (empty($errors)) {
    // File is valid
    $path = $imageService->uploadImage($file, 'uploads');
} else {
    // Handle validation errors
    foreach ($errors as $error) {
        echo $error . "\n";
    }
}
```

#### Watermark

```php
// Add watermark to existing image
$watermarkedPath = $imageService->addWatermark('path/to/image.jpg');

// Add custom watermark
$watermarkedPath = $imageService->addWatermark(
    'path/to/image.jpg', 
    'path/to/watermark.png'
);
```

#### Image Information

```php
$info = $imageService->getImageInfo('path/to/image.jpg');
// Returns:
// [
//     'width' => 1200,
//     'height' => 800,
//     'type' => 2, // IMAGETYPE_JPEG
//     'mime' => 'image/jpeg',
//     'size' => 245760, // bytes
//     'size_formatted' => '240 KB',
//     'url' => 'http://example.com/storage/path/to/image.jpg'
// ]
```

#### Delete Image

```php
$success = $imageService->deleteImage('path/to/image.jpg');
```

#### Get Image URL

```php
$url = $imageService->getImageUrl('path/to/image.jpg');
```

## Controller Integration

### Dependency Injection

```php
use App\Services\ImageService;

class EventController extends Controller
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    public function store(Request $request)
    {
        if ($request->hasFile('poster')) {
            $posterPath = $this->imageService->uploadPoster($request->file('poster'));
        }

        if ($request->hasFile('gallery')) {
            $galleryPaths = [];
            foreach ($request->file('gallery') as $file) {
                $galleryPaths[] = $this->imageService->uploadGallery($file);
            }
        }
    }
}
```

## Error Handling

The ImageService throws exceptions for errors:

```php
try {
    $path = $imageService->uploadPoster($file);
} catch (\Exception $e) {
    // Handle error
    return back()->with('error', 'Failed to upload image: ' . $e->getMessage());
}
```

## Performance Tips

1. **Use appropriate image sizes**: Don't upload larger images than needed
2. **Enable caching**: Set `IMAGE_CACHE_ENABLED=true` in production
3. **Choose the right driver**: 
   - `gd`: Faster, lower memory usage, basic features
   - `imagick`: More features, better quality, higher memory usage
4. **Optimize quality settings**: Balance between file size and quality
5. **Use WebP format**: Better compression than JPEG/PNG (when supported)

## Troubleshooting

### Common Issues

1. **"Class 'Intervention\Image\Facades\Image' not found"**
   - Make sure `ImageServiceProvider` is registered in `config/app.php`
   - Clear config cache: `php artisan config:clear`

2. **"GD Library not found"**
   - Install PHP GD extension: `sudo apt-get install php-gd` (Ubuntu/Debian)
   - Or use Imagick driver if available

3. **Memory limit exceeded**
   - Increase PHP memory limit in `php.ini`
   - Use smaller image sizes
   - Process images in batches

4. **Permission denied**
   - Check storage directory permissions: `chmod 755 storage/app/public`
   - Ensure web server can write to storage

### Debugging

Enable debug mode to see detailed error messages:

```env
APP_DEBUG=true
LOG_LEVEL=debug
```

Check Laravel logs for image processing errors:

```bash
tail -f storage/logs/laravel.log
```

## Security Considerations

1. **Validate file types**: Always validate uploaded file extensions and MIME types
2. **Limit file sizes**: Set reasonable limits to prevent DoS attacks
3. **Sanitize filenames**: The service automatically generates safe filenames
4. **Store outside web root**: Use Laravel's storage system
5. **Scan for malware**: Consider integrating virus scanning for user uploads

## Examples

See the `EventController` for real-world usage examples of image upload and processing.
