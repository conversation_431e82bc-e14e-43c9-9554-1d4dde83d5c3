<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ForceGdDriver
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Force GD driver in configuration
        config(['image.driver' => 'gd']);
        config(['intervention.driver' => \Intervention\Image\Drivers\Gd\Driver::class]);
        
        // Set environment variable to ensure consistency
        putenv('IMAGE_DRIVER=gd');
        
        return $next($request);
    }
}
