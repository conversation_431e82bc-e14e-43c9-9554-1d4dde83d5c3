@echo off
echo Fresh Migration with Notifications Fix...

echo.
echo [1/8] Clearing all caches...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo.
echo [2/8] Dropping all tables and running fresh migration...
php artisan migrate:fresh

echo.
echo [3/8] Running the notifications fix migration...
php artisan migrate --path=database/migrations/2024_01_01_000008_fix_notifications_table.php

echo.
echo [4/8] Verifying notifications table structure...
php artisan tinker --execute="
try {
    \$exists = \Illuminate\Support\Facades\Schema::hasTable('notifications');
    echo 'Notifications table exists: ' . (\$exists ? 'YES' : 'NO') . PHP_EOL;

    if (\$exists) {
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('notifications');
        echo 'Columns: ' . implode(', ', \$columns) . PHP_EOL;

        \$hasUserId = \Illuminate\Support\Facades\Schema::hasColumn('notifications', 'user_id');
        echo 'Has user_id column: ' . (\$hasUserId ? 'YES' : 'NO') . PHP_EOL;

        if (\$hasUserId) {
            echo 'SUCCESS: Notifications table structure is correct!' . PHP_EOL;
        } else {
            echo 'ERROR: user_id column still missing!' . PHP_EOL;
        }
    }
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/8] Running seeders...
php artisan db:seed

echo.
echo [6/8] Testing notification functionality...
php artisan tinker --execute="
try {
    \$user = \App\Models\User::first();
    if (\$user) {
        \$notificationCount = \$user->notifications()->count();
        echo 'User notifications count: ' . \$notificationCount . PHP_EOL;

        \$unreadCount = \$user->unreadNotifications()->count();
        echo 'Unread notifications count: ' . \$unreadCount . PHP_EOL;

        if (\$notificationCount > 0) {
            echo 'SUCCESS: Notifications are working!' . PHP_EOL;
        } else {
            echo 'INFO: No notifications found (this is normal for fresh installation)' . PHP_EOL;
        }
    } else {
        echo 'No users found.' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error testing notifications: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [7/8] Creating a test notification...
php artisan tinker --execute="
try {
    \$user = \App\Models\User::first();
    if (\$user) {
        \$notification = \App\Models\Notification::create([
            'user_id' => \$user->id,
            'title' => 'Test Notification',
            'message' => 'This is a test notification to verify everything is working.',
            'type' => 'system',
            'priority' => 'normal'
        ]);
        echo 'SUCCESS: Test notification created with ID: ' . \$notification->id . PHP_EOL;

        // Test querying
        \$userNotifications = \$user->notifications()->count();
        echo 'User now has ' . \$userNotifications . ' notifications' . PHP_EOL;

        echo 'All notification functionality is working correctly!' . PHP_EOL;
    } else {
        echo 'No users found to test with.' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error creating test notification: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [8/8] Final verification...
php artisan route:list --name=notification

echo.
echo ========================================
echo Fresh migration with notifications completed!
echo ========================================
echo.
echo Database has been reset and notifications are working.
echo You can now use the application without the user_id error.
echo.
echo Test accounts:
echo - Admin: <EMAIL> / TiXara@2024
echo - Staff: <EMAIL> / Staff@2024
echo - Organizer: <EMAIL> / Penjual@2024
echo - User: <EMAIL> / Pembeli@2024
echo.
pause
