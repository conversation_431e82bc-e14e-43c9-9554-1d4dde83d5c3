/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
    './storage/framework/views/*.php',
    './resources/views/**/*.blade.php',
    './resources/js/**/*.js',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'primary': {
          50: '#f0f9f4',
          100: '#dcf2e4',
          200: '#bce5cc',
          300: '#8dd1a8',
          400: '#5bb67d',
          500: '#A8D5BA',  // Main hijau pasta
          600: '#8bc5a0',
          700: '#6fa082',
          800: '#5a8069',
          900: '#4a6856',
          DEFAULT: '#A8D5BA', // This is important for bg-primary to work
        },
        'secondary': {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#F7F7F7',
          600: '#e5e5e5',
          700: '#d4d4d4',
          800: '#a3a3a3',
          900: '#737373',
          DEFAULT: '#F7F7F7',
        },
        'tertiary': {
          50: '#f9f9f9',
          100: '#f0f0f0',
          200: '#e6e6e6',
          300: '#D6D6D6',
          400: '#c7c7c7',
          500: '#b8b8b8',
          600: '#a9a9a9',
          700: '#9a9a9a',
          800: '#8b8b8b',
          900: '#7c7c7c',
          DEFAULT: '#D6D6D6',
        },
        'accent': {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#C7EACB',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
          DEFAULT: '#C7EACB',
        },
        'pasta': {
          'cream': '#FFF8DC',
          'salmon': '#FFB6C1',
          'lavender': '#E6E6FA',
          'mint': '#F0FFF0',
          'peach': '#FFDAB9',
          'rose': '#FFE4E1',
          'sage': '#9CAF88',
          'butter': '#FFFACD',
        },
        'dark': {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          DEFAULT: '#1e293b',
        },
        'light': '#F7F7F7',
        'success': '#48bb78',
        'warning': '#ed8936',
        'error': '#f56565',
        'info': '#4299e1',
      },
      fontFamily: {
        'sans': ['Poppins', 'DM Sans', 'sans-serif'],
        'poppins': ['Poppins', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-in-out',
        'slide-down': 'slideDown 0.3s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      spacing: {
        '18': '4.5rem',
        '112': '28rem',
        '128': '32rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '2rem',
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
      },
    },
  },
  plugins: [
    // Note: @tailwindcss/forms might need to be imported differently for ES modules
    // For now, we'll comment it out to avoid issues
    // import forms from '@tailwindcss/forms'
    // forms,
  ],
}