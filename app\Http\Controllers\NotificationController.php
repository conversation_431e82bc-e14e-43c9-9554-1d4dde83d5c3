<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get user's notifications
     */
    public function index(Request $request)
    {
        $notifications = Auth::user()->notifications()
            ->latest()
            ->limit(50)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'type' => $notification->type,
                    'priority' => $notification->priority,
                    'icon' => $notification->icon,
                    'color' => $notification->color,
                    'action_url' => $notification->action_url,
                    'is_read' => $notification->isRead(),
                    'created_at' => $notification->created_at->diffForHumans(),
                    'created_at_iso' => $notification->created_at->toISOString(),
                ];
            });

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => Auth::user()->unreadNotifications()->count(),
        ]);
    }

    /**
     * Get unread notifications count
     */
    public function unreadCount()
    {
        return response()->json([
            'count' => Auth::user()->unreadNotifications()->count()
        ]);
    }

    /**
     * Get latest notifications (for polling)
     */
    public function latest(Request $request)
    {
        $lastCheck = $request->get('last_check');
        $query = Auth::user()->notifications()->latest();

        if ($lastCheck) {
            $query->where('created_at', '>', $lastCheck);
        } else {
            $query->limit(10);
        }

        $notifications = $query->get()->map(function ($notification) {
            return [
                'id' => $notification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'priority' => $notification->priority,
                'icon' => $notification->icon,
                'color' => $notification->color,
                'action_url' => $notification->action_url,
                'is_read' => $notification->isRead(),
                'created_at' => $notification->created_at->diffForHumans(),
                'created_at_iso' => $notification->created_at->toISOString(),
            ];
        });

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => Auth::user()->unreadNotifications()->count(),
            'has_new' => $notifications->count() > 0,
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Notification $notification)
    {
        // Check if user owns this notification
        if ($notification->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'unread_count' => Auth::user()->unreadNotifications()->count(),
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        Auth::user()->unreadNotifications()->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'unread_count' => 0,
        ]);
    }

    /**
     * Delete notification
     */
    public function destroy(Notification $notification)
    {
        // Check if user owns this notification
        if ($notification->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'unread_count' => Auth::user()->unreadNotifications()->count(),
        ]);
    }

    /**
     * Test notification (for development)
     */
    public function test()
    {
        $types = ['system', 'event', 'payment', 'order', 'ticket'];
        $priorities = ['low', 'normal', 'high', 'urgent'];

        $type = $types[array_rand($types)];
        $priority = $priorities[array_rand($priorities)];

        $messages = [
            'system' => 'Sistem akan maintenance dalam 30 menit',
            'event' => 'Event baru "Jakarta Music Festival" telah tersedia',
            'payment' => 'Pembayaran Anda telah berhasil diproses',
            'order' => 'Pesanan Anda telah dikonfirmasi',
            'ticket' => 'Tiket Anda siap diunduh',
        ];

        $titles = [
            'system' => 'Pemberitahuan Sistem',
            'event' => 'Event Baru',
            'payment' => 'Pembayaran Berhasil',
            'order' => 'Pesanan Dikonfirmasi',
            'ticket' => 'Tiket Siap',
        ];

        $notification = Notification::create([
            'user_id' => Auth::id(),
            'title' => $titles[$type],
            'message' => $messages[$type],
            'type' => $type,
            'priority' => $priority,
            'data' => ['test' => true, 'timestamp' => now()->toISOString()],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Test notification created',
            'notification' => [
                'id' => $notification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'priority' => $notification->priority,
                'created_at' => $notification->created_at->diffForHumans(),
            ]
        ]);
    }
}
