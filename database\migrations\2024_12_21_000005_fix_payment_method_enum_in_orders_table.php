<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's check current enum values
        $currentEnum = DB::select("SHOW COLUMNS FROM orders WHERE Field = 'payment_method'")[0]->Type ?? '';
        
        // If it's still the old enum, update it
        if (strpos($currentEnum, 'qris') === false || strpos($currentEnum, 'virtual_account') === false) {
            // Temporarily change to varchar to avoid enum constraints
            DB::statement("ALTER TABLE orders MODIFY COLUMN payment_method VARCHAR(50) NULL");
            
            // Now change back to enum with all required values
            DB::statement("ALTER TABLE orders MODIFY COLUMN payment_method ENUM(
                'bank_transfer',
                'credit_card', 
                'e_wallet',
                'qris',
                'virtual_account',
                'cash'
            ) NULL");
            
            echo "✅ Updated payment_method enum to include qris and virtual_account\n";
        } else {
            echo "ℹ️ payment_method enum already contains all required values\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to original enum values
        DB::statement("ALTER TABLE orders MODIFY COLUMN payment_method VARCHAR(50) NULL");
        
        DB::statement("ALTER TABLE orders MODIFY COLUMN payment_method ENUM(
            'bank_transfer',
            'credit_card',
            'e_wallet', 
            'cash'
        ) NULL");
    }
};
