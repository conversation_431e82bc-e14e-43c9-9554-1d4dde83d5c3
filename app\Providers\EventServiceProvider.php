<?php

namespace App\Providers;

use Illuminate\Auth\Ticket\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\TicketServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class TicketServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * Register any tickets for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if tickets and listeners should be automatically discovered.
     */
    public function shouldDiscoverTickets(): bool
    {
        return false;
    }
}
