<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Ad extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'position',
        'image_url',
        'click_url',
        'targeting',
        'cost_per_click',
        'cost_per_impression',
        'daily_budget',
        'total_budget',
        'spent_amount',
        'impressions',
        'clicks',
        'priority',
        'is_active',
        'is_approved',
        'start_date',
        'end_date',
        'advertiser_id',
        'event_id',
        'status',
        'admin_notes',
    ];

    protected $casts = [
        'targeting' => 'array',
        'cost_per_click' => 'decimal:2',
        'cost_per_impression' => 'decimal:2',
        'daily_budget' => 'decimal:2',
        'total_budget' => 'decimal:2',
        'spent_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'is_approved' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    /**
     * Get the advertiser (user) that owns the ad
     */
    public function advertiser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'advertiser_id');
    }

    /**
     * Get the event associated with the ad (if any)
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the analytics for this ad
     */
    public function analytics(): HasMany
    {
        return $this->hasMany(AdAnalytic::class);
    }

    /**
     * Scope for active ads
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('is_approved', true)
                    ->where('status', 'approved')
                    ->where(function ($q) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    /**
     * Scope for ads by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for ads by position
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope for priority ordering
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Check if ad is currently running
     */
    public function isRunning(): bool
    {
        if (!$this->is_active || !$this->is_approved || $this->status !== 'approved') {
            return false;
        }

        $now = now();
        
        if ($this->start_date && $this->start_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        return true;
    }

    /**
     * Check if ad budget is exhausted
     */
    public function isBudgetExhausted(): bool
    {
        if ($this->total_budget <= 0) {
            return false; // Unlimited budget
        }

        return $this->spent_amount >= $this->total_budget;
    }

    /**
     * Get click-through rate
     */
    public function getCtrAttribute(): float
    {
        if ($this->impressions == 0) {
            return 0;
        }

        return round(($this->clicks / $this->impressions) * 100, 2);
    }

    /**
     * Get cost per click
     */
    public function getCpcAttribute(): float
    {
        if ($this->clicks == 0) {
            return 0;
        }

        return round($this->spent_amount / $this->clicks, 2);
    }

    /**
     * Get cost per mille (1000 impressions)
     */
    public function getCpmAttribute(): float
    {
        if ($this->impressions == 0) {
            return 0;
        }

        return round(($this->spent_amount / $this->impressions) * 1000, 2);
    }

    /**
     * Record an impression
     */
    public function recordImpression($deviceType = null, $location = null, $referrer = null): void
    {
        $this->increment('impressions');
        
        $cost = $this->cost_per_impression;
        if ($cost > 0) {
            $this->increment('spent_amount', $cost);
        }

        // Record in analytics
        $this->recordAnalytics('impression', $deviceType, $location, $referrer);
    }

    /**
     * Record a click
     */
    public function recordClick($deviceType = null, $location = null, $referrer = null): void
    {
        $this->increment('clicks');
        
        $cost = $this->cost_per_click;
        if ($cost > 0) {
            $this->increment('spent_amount', $cost);
        }

        // Record in analytics
        $this->recordAnalytics('click', $deviceType, $location, $referrer);
    }

    /**
     * Record analytics data
     */
    private function recordAnalytics($type, $deviceType = null, $location = null, $referrer = null): void
    {
        $today = now()->format('Y-m-d');
        
        $analytic = AdAnalytic::firstOrCreate(
            ['ad_id' => $this->id, 'date' => $today],
            [
                'impressions' => 0,
                'clicks' => 0,
                'cost' => 0,
                'device_type' => $deviceType,
                'location' => $location,
                'referrer' => $referrer,
            ]
        );

        if ($type === 'impression') {
            $analytic->increment('impressions');
            if ($this->cost_per_impression > 0) {
                $analytic->increment('cost', $this->cost_per_impression);
            }
        } elseif ($type === 'click') {
            $analytic->increment('clicks');
            if ($this->cost_per_click > 0) {
                $analytic->increment('cost', $this->cost_per_click);
            }
        }

        // Update calculated fields
        $analytic->update([
            'ctr' => $analytic->impressions > 0 ? ($analytic->clicks / $analytic->impressions) * 100 : 0,
            'cpc' => $analytic->clicks > 0 ? $analytic->cost / $analytic->clicks : 0,
            'cpm' => $analytic->impressions > 0 ? ($analytic->cost / $analytic->impressions) * 1000 : 0,
        ]);
    }

    /**
     * Get ads for display based on criteria
     */
    public static function getAdsForDisplay($type = null, $position = null, $limit = 5)
    {
        $query = static::active()->byPriority();

        if ($type) {
            $query->byType($type);
        }

        if ($position) {
            $query->byPosition($position);
        }

        return $query->limit($limit)->get()->filter(function ($ad) {
            return !$ad->isBudgetExhausted();
        });
    }
}
