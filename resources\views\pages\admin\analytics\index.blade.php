@extends('layouts.admin')

@section('title', 'Analytics')
@section('subtitle', 'Platform performance insights and metrics')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header with Controls -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Analytics Dashboard</h1>
            <p class="text-gray-600 dark:text-gray-400">Comprehensive platform performance metrics</p>
        </div>
        
        <div class="mt-4 md:mt-0 flex items-center space-x-3">
            <!-- Date Range Selector -->
            <select id="dateRange" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                <option value="7" {{ $dateRange == 7 ? 'selected' : '' }}>Last 7 Days</option>
                <option value="30" {{ $dateRange == 30 ? 'selected' : '' }}>Last 30 Days</option>
                <option value="90" {{ $dateRange == 90 ? 'selected' : '' }}>Last 90 Days</option>
                <option value="365" {{ $dateRange == 365 ? 'selected' : '' }}>Last Year</option>
            </select>
            
            <!-- Export Button -->
            <x-button variant="outline" icon="download" onclick="exportAnalytics()">
                Export
            </x-button>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <x-stats-card 
            title="Total Revenue"
            :value="'Rp ' . number_format($revenueData['total_revenue'] ?? 0, 0, ',', '.')"
            icon="dollar-sign"
            icon-color="green"
            :change="($revenueData['growth_rate'] ?? 0) . '%'"
            :change-type="($revenueData['growth_rate'] ?? 0) >= 0 ? 'positive' : 'negative'"
            change-label="vs last period"
            :trend="($revenueData['growth_rate'] ?? 0) >= 0 ? 'up' : 'down'"
            aos="fade-up"
            aos-delay="100"
        />
        
        <x-stats-card 
            title="Total Users"
            :value="number_format($userAnalytics['total_users'] ?? 0, 0, ',', '.')"
            icon="users"
            icon-color="blue"
            :change="'+' . number_format($userAnalytics['total_users'] ?? 0, 0, ',', '.')"
            change-type="positive"
            change-label="new users"
            trend="up"
            aos="fade-up"
            aos-delay="200"
        />
        
        <x-stats-card 
            title="Total Events"
            :value="number_format($eventAnalytics['total_events'] ?? 0, 0, ',', '.')"
            icon="calendar"
            icon-color="purple"
            :change="number_format($eventAnalytics['published_events'] ?? 0, 0, ',', '.') . ' published'"
            change-type="neutral"
            aos="fade-up"
            aos-delay="300"
        />
        
        <x-stats-card 
            title="Tickets Sold"
            :value="number_format($eventAnalytics['total_tickets_sold'] ?? 0, 0, ',', '.')"
            icon="ticket"
            icon-color="orange"
            aos="fade-up"
            aos-delay="400"
        />
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Chart -->
        <x-modern-card title="Revenue Trends" icon="trending-up" icon-color="green" aos="fade-up" aos-delay="500">
            <div class="h-80">
                <canvas id="revenueChart"></canvas>
            </div>
        </x-modern-card>

        <!-- User Growth Chart -->
        <x-modern-card title="User Growth" icon="users" icon-color="blue" aos="fade-up" aos-delay="600">
            <div class="h-80">
                <canvas id="userChart"></canvas>
            </div>
        </x-modern-card>
    </div>

    <!-- Additional Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Top Categories -->
        <x-modern-card title="Top Categories" icon="tag" icon-color="purple" aos="fade-up" aos-delay="700">
            <div class="space-y-4">
                @if(isset($categoryPerformance) && count($categoryPerformance) > 0)
                    @foreach($categoryPerformance->take(5) as $category)
                    <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                                <span class="text-white text-sm font-bold">{{ substr($category->name ?? 'N/A', 0, 2) }}</span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ $category->name ?? 'Unknown' }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $category->events_count ?? 0 }} events</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white">Rp {{ number_format($category->total_revenue ?? 0, 0, ',', '.') }}</p>
                        </div>
                    </div>
                    @endforeach
                @else
                    <x-empty-state 
                        icon="tag"
                        title="No category data"
                        description="Category performance data will appear here"
                        size="sm"
                    />
                @endif
            </div>
        </x-modern-card>

        <!-- Geographic Distribution -->
        <x-modern-card title="Top Locations" icon="map-pin" icon-color="orange" aos="fade-up" aos-delay="800">
            <div class="space-y-4">
                @if(isset($geographicData) && count($geographicData) > 0)
                    @foreach($geographicData->take(5) as $location)
                    <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
                                <i data-lucide="map-pin" class="w-4 h-4 text-orange-600 dark:text-orange-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ $location->location ?? 'Unknown' }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white">{{ $location->events_count ?? 0 }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">events</p>
                        </div>
                    </div>
                    @endforeach
                @else
                    <x-empty-state 
                        icon="map"
                        title="No location data"
                        description="Geographic data will appear here"
                        size="sm"
                    />
                @endif
            </div>
        </x-modern-card>

        <!-- Quick Stats -->
        <x-modern-card title="Quick Stats" icon="activity" icon-color="indigo" aos="fade-up" aos-delay="900">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Active Users</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ number_format($userAnalytics['active_users'] ?? 0, 0, ',', '.') }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Organizers</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ number_format($userAnalytics['total_organizers'] ?? 0, 0, ',', '.') }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Published Events</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ number_format($eventAnalytics['published_events'] ?? 0, 0, ',', '.') }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Avg Revenue/Event</span>
                    <span class="font-semibold text-gray-900 dark:text-white">
                        Rp {{ number_format(($eventAnalytics['total_events'] ?? 0) > 0 ? ($revenueData['total_revenue'] ?? 0) / ($eventAnalytics['total_events'] ?? 1) : 0, 0, ',', '.') }}
                    </span>
                </div>
            </div>
        </x-modern-card>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: @json($revenueData['daily_revenue']->pluck('date') ?? []),
            datasets: [{
                label: 'Revenue',
                data: @json($revenueData['daily_revenue']->pluck('revenue') ?? []),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // User Growth Chart
    const userCtx = document.getElementById('userChart').getContext('2d');
    const userChart = new Chart(userCtx, {
        type: 'bar',
        data: {
            labels: @json($userAnalytics['daily_registrations']->pluck('date') ?? []),
            datasets: [{
                label: 'New Users',
                data: @json($userAnalytics['daily_registrations']->pluck('registrations') ?? []),
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Date range change handler
    document.getElementById('dateRange').addEventListener('change', function() {
        window.location.href = '{{ route("admin.analytics.index") }}?range=' + this.value;
    });
});

function exportAnalytics() {
    const range = document.getElementById('dateRange').value;
    window.location.href = '{{ route("admin.analytics.export") }}?range=' + range;
}
</script>
@endpush
