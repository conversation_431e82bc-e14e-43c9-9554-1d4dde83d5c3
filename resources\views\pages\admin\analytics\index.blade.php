@extends('layouts.admin')

@section('title', 'Analytics Dashboard')
@section('subtitle', 'Platform performance insights and metrics')

@push('styles')
<style>
/* Custom styles for Analytics Dashboard */
.analytics-card {
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.chart-container {
    position: relative;
    height: 320px;
}

.metric-trend {
    transition: all 0.2s ease;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .chart-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Chart loading state */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 320px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="bar-chart-3" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Analytics Dashboard</h1>
                            <p class="text-gray-600 dark:text-gray-400">Comprehensive platform performance metrics</p>
                        </div>
                    </div>
                </div>

                <!-- Controls -->
                <div class="flex flex-wrap items-center gap-3">
                    <!-- Date Range Selector -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 shadow-sm">
                            <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                            <span id="selectedRange">Last {{ $dateRange ?? 30 }} Days</span>
                            <i data-lucide="chevron-down" class="w-4 h-4 ml-2"></i>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                            <div class="py-1">
                                <a href="?range=7" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    Last 7 Days
                                </a>
                                <a href="?range=30" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    Last 30 Days
                                </a>
                                <a href="?range=90" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    Last 90 Days
                                </a>
                                <a href="?range=365" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    Last Year
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Export Button -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-sm">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Export
                            <i data-lucide="chevron-down" class="w-4 h-4 ml-2"></i>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                            <div class="py-1">
                                <a href="#" onclick="exportAnalytics('pdf')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Export PDF
                                </a>
                                <a href="#" onclick="exportAnalytics('excel')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-spreadsheet" class="w-4 h-4 mr-3"></i>
                                    Export Excel
                                </a>
                                <a href="#" onclick="exportAnalytics('csv')"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Export CSV
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Refresh Button -->
                    <button onclick="refreshAnalytics()"
                            class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stats-grid">
            <!-- Total Revenue -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 analytics-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Revenue</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            Rp {{ number_format($revenueData['total_revenue'] ?? 0, 0, ',', '.') }}
                        </p>
                        <div class="flex items-center mt-2">
                            @php
                                $growthRate = $revenueData['growth_rate'] ?? 0;
                                $isPositive = $growthRate >= 0;
                            @endphp
                            <span class="text-xs {{ $isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }} metric-trend">
                                <i data-lucide="{{ $isPositive ? 'trending-up' : 'trending-down' }}" class="w-3 h-3 inline mr-1"></i>
                                {{ $isPositive ? '+' : '' }}{{ number_format($growthRate, 1) }}% vs last period
                            </span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="dollar-sign" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Total Users -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 analytics-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Users</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($userAnalytics['total_users'] ?? 0, 0, ',', '.') }}
                        </p>
                        <div class="flex items-center mt-2">
                            <span class="text-xs text-blue-600 dark:text-blue-400 metric-trend">
                                <i data-lucide="user-plus" class="w-3 h-3 inline mr-1"></i>
                                +{{ number_format($userAnalytics['new_users'] ?? 0, 0, ',', '.') }} new users
                            </span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>

            <!-- Total Events -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 analytics-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Events</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($eventAnalytics['total_events'] ?? 0, 0, ',', '.') }}
                        </p>
                        <div class="flex items-center mt-2">
                            <span class="text-xs text-purple-600 dark:text-purple-400 metric-trend">
                                <i data-lucide="calendar-check" class="w-3 h-3 inline mr-1"></i>
                                {{ number_format($eventAnalytics['published_events'] ?? 0, 0, ',', '.') }} published
                            </span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="calendar" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
            </div>

            <!-- Tickets Sold -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 analytics-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Tickets Sold</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($eventAnalytics['total_tickets_sold'] ?? 0, 0, ',', '.') }}
                        </p>
                        <div class="flex items-center mt-2">
                            <span class="text-xs text-orange-600 dark:text-orange-400 metric-trend">
                                <i data-lucide="ticket" class="w-3 h-3 inline mr-1"></i>
                                {{ number_format($eventAnalytics['tickets_today'] ?? 0, 0, ',', '.') }} today
                            </span>
                        </div>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="ticket" class="w-6 h-6 text-orange-600 dark:text-orange-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 chart-grid">
            <!-- Revenue Chart -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden analytics-card">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                            <i data-lucide="trending-up" class="w-5 h-5 text-green-600 dark:text-green-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Revenue Trends</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- User Growth Chart -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden analytics-card">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                            <i data-lucide="users" class="w-5 h-5 text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">User Growth</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="chart-container">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 analytics-grid">
            <!-- Top Categories -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden analytics-card">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                            <i data-lucide="tag" class="w-5 h-5 text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Top Categories</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @if(isset($categoryPerformance) && count($categoryPerformance) > 0)
                            @foreach($categoryPerformance->take(5) as $category)
                            <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                                        <span class="text-white text-sm font-bold">{{ substr($category->name ?? 'N/A', 0, 2) }}</span>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $category->name ?? 'Unknown' }}</p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $category->events_count ?? 0 }} events</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-gray-900 dark:text-white">Rp {{ number_format($category->total_revenue ?? 0, 0, ',', '.') }}</p>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="tag" class="w-8 h-8 text-gray-400"></i>
                                </div>
                                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No category data</h4>
                                <p class="text-gray-600 dark:text-gray-400">Category performance data will appear here</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

        <!-- Geographic Distribution -->
        <x-modern-card title="Top Locations" icon="map-pin" icon-color="orange" aos="fade-up" aos-delay="800">
            <div class="space-y-4">
                @if(isset($geographicData) && count($geographicData) > 0)
                    @foreach($geographicData->take(5) as $location)
                    <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
                                <i data-lucide="map-pin" class="w-4 h-4 text-orange-600 dark:text-orange-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ $location->location ?? 'Unknown' }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white">{{ $location->events_count ?? 0 }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">events</p>
                        </div>
                    </div>
                    @endforeach
                @else
                    <x-empty-state 
                        icon="map"
                        title="No location data"
                        description="Geographic data will appear here"
                        size="sm"
                    />
                @endif
            </div>
        </x-modern-card>

        <!-- Quick Stats -->
        <x-modern-card title="Quick Stats" icon="activity" icon-color="indigo" aos="fade-up" aos-delay="900">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Active Users</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ number_format($userAnalytics['active_users'] ?? 0, 0, ',', '.') }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Organizers</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ number_format($userAnalytics['total_organizers'] ?? 0, 0, ',', '.') }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Published Events</span>
                    <span class="font-semibold text-gray-900 dark:text-white">{{ number_format($eventAnalytics['published_events'] ?? 0, 0, ',', '.') }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Avg Revenue/Event</span>
                    <span class="font-semibold text-gray-900 dark:text-white">
                        Rp {{ number_format(($eventAnalytics['total_events'] ?? 0) > 0 ? ($revenueData['total_revenue'] ?? 0) / ($eventAnalytics['total_events'] ?? 1) : 0, 0, ',', '.') }}
                    </span>
                </div>
            </div>
        </x-modern-card>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: @json($revenueData['daily_revenue']->pluck('date') ?? []),
            datasets: [{
                label: 'Revenue',
                data: @json($revenueData['daily_revenue']->pluck('revenue') ?? []),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // User Growth Chart
    const userCtx = document.getElementById('userChart').getContext('2d');
    const userChart = new Chart(userCtx, {
        type: 'bar',
        data: {
            labels: @json($userAnalytics['daily_registrations']->pluck('date') ?? []),
            datasets: [{
                label: 'New Users',
                data: @json($userAnalytics['daily_registrations']->pluck('registrations') ?? []),
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Date range change handler
    document.getElementById('dateRange').addEventListener('change', function() {
        window.location.href = '{{ route("admin.analytics.index") }}?range=' + this.value;
    });
});

// Export functions
function exportAnalytics(format = 'pdf') {
    const urlParams = new URLSearchParams(window.location.search);
    const range = urlParams.get('range') || '30';

    const exportUrl = `{{ route("admin.analytics.export") }}?range=${range}&format=${format}`;

    // Show loading state
    showNotification('Export Started', `Preparing ${format.toUpperCase()} export...`, 'info');

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `analytics_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Refresh analytics data
function refreshAnalytics() {
    showNotification('Refreshing', 'Updating analytics data...', 'info');

    // Add loading spinner to refresh button
    const refreshBtn = document.querySelector('[onclick="refreshAnalytics()"]');
    const originalContent = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>Refreshing...';
    refreshBtn.disabled = true;

    // Simulate refresh (in real app, this would be an AJAX call)
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Notification system
function showNotification(title, message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

    const colors = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-white',
        info: 'bg-blue-500 text-white'
    };

    notification.className += ` ${colors[type] || colors.info}`;

    notification.innerHTML = `
        <div class="flex items-start">
            <div class="flex-1">
                <h4 class="font-medium">${title}</h4>
                <p class="text-sm opacity-90">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Add chart loading states
    const chartContainers = document.querySelectorAll('.chart-container canvas');
    chartContainers.forEach(canvas => {
        const container = canvas.parentElement;
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'chart-loading absolute inset-0 flex items-center justify-center';
        loadingDiv.innerHTML = `
            <div class="text-center">
                <div class="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-2"></div>
                <p class="text-sm text-gray-500">Loading chart...</p>
            </div>
        `;
        container.appendChild(loadingDiv);

        // Remove loading state after chart is rendered
        setTimeout(() => {
            loadingDiv.remove();
        }, 2000);
    });
});
</script>
@endpush
