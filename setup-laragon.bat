@echo off
echo Setting up Laragon for TiXara...

echo.
echo [1/6] Checking Laragon installation...
if exist "C:\laragon" (
    echo ✓ Laragon found at C:\laragon
) else (
    echo ✗ Laragon not found! Please install Laragon first.
    echo Download from: https://laragon.org/download/
    pause
    exit /b 1
)

echo.
echo [2/6] Checking current project location...
echo Current directory: %CD%
if "%CD%" == "C:\laragon\www\Project-tixara.my.id" (
    echo ✓ Project is in correct Laragon directory
) else (
    echo ⚠ Project might not be in standard Laragon directory
    echo Expected: C:\laragon\www\Project-tixara.my.id
    echo Current:  %CD%
)

echo.
echo [3/6] Creating virtual host configuration...
if exist "laragon-vhost-config.conf" (
    echo ✓ Virtual host config file found
    echo.
    echo To apply this configuration:
    echo 1. Copy laragon-vhost-config.conf to C:\laragon\etc\apache2\sites-enabled\tixara.conf
    echo 2. Restart Laragon
    echo 3. Access your site at: http://tixara.test/tickets/my-tickets
) else (
    echo ✗ Virtual host config file not found!
)

echo.
echo [4/6] Checking .env configuration...
if exist ".env" (
    echo ✓ .env file exists
    php artisan tinker --execute="
    echo 'APP_URL: ' . config('app.url') . PHP_EOL;
    echo 'APP_ENV: ' . config('app.env') . PHP_EOL;
    "
) else (
    echo ✗ .env file not found! Please copy .env.example to .env
)

echo.
echo [5/6] Testing Laravel installation...
php artisan --version
if %ERRORLEVEL% EQU 0 (
    echo ✓ Laravel is working
) else (
    echo ✗ Laravel has issues
)

echo.
echo [6/6] Generating application key if needed...
php artisan key:generate --show

echo.
echo ========================================
echo Laragon Setup Instructions
echo ========================================
echo.
echo 1. COPY VIRTUAL HOST CONFIG:
echo    Copy: laragon-vhost-config.conf
echo    To:   C:\laragon\etc\apache2\sites-enabled\tixara.conf
echo.
echo 2. UPDATE .ENV FILE:
echo    Set APP_URL=http://tixara.test
echo.
echo 3. RESTART LARAGON:
echo    Stop and start Laragon services
echo.
echo 4. ACCESS YOUR SITE:
echo    ✓ http://tixara.test/tickets/my-tickets
echo    ✓ http://tixara.test/my-tickets
echo    ✗ http://tixara.test/public/tickets/my-tickets (wrong!)
echo.
echo 5. ALTERNATIVE (if virtual host doesn't work):
echo    Use: php artisan serve
echo    Then: http://localhost:8000/tickets/my-tickets
echo.
echo ========================================
echo Common Issues and Solutions
echo ========================================
echo.
echo ISSUE: Still getting /public/ in URL
echo SOLUTION: 
echo - Check virtual host DocumentRoot points to 'public' folder
echo - Make sure .htaccess files are working
echo - Clear browser cache
echo.
echo ISSUE: 404 errors
echo SOLUTION:
echo - Run: php artisan route:clear
echo - Check if mod_rewrite is enabled in Apache
echo - Verify .htaccess files exist and are readable
echo.
echo ISSUE: Permission errors
echo SOLUTION:
echo - Run Laragon as Administrator
echo - Check folder permissions
echo - Make sure storage and bootstrap/cache are writable
echo.
pause
