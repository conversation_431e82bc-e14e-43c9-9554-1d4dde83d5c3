<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Ad;
use App\Models\AdSubscription;
use App\Models\AdAnalytic;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class AdController extends Controller
{
    /**
     * Display organizer's ads
     */
    public function index(Request $request)
    {
        $query = auth()->user()->ads()->with('event');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $ads = $query->orderBy('created_at', 'desc')->paginate(10);

        // Statistics
        $stats = [
            'total_ads' => auth()->user()->ads()->count(),
            'active_ads' => auth()->user()->ads()->where('status', 'approved')->where('is_active', true)->count(),
            'pending_ads' => auth()->user()->ads()->where('status', 'pending')->count(),
            'total_spent' => auth()->user()->ads()->sum('spent_amount'),
            'total_impressions' => auth()->user()->ads()->sum('impressions'),
            'total_clicks' => auth()->user()->ads()->sum('clicks'),
        ];

        return view('pages.organizer.ads.index', compact('ads', 'stats'));
    }

    /**
     * Show create ad form
     */
    public function create()
    {
        // Check if user has active subscription
        $subscription = auth()->user()->activeAdSubscription();
        
        if (!$subscription) {
            return redirect()->route('organizer.ads.subscriptions')
                ->with('error', 'Anda perlu berlangganan paket iklan terlebih dahulu.');
        }

        // Check if user has reached ad limit
        $currentAdsCount = auth()->user()->ads()->where('status', '!=', 'rejected')->count();
        if ($currentAdsCount >= $subscription->max_ads) {
            return redirect()->route('organizer.ads.index')
                ->with('error', 'Anda telah mencapai batas maksimal iklan untuk paket Anda.');
        }

        $events = auth()->user()->organizedEvents()
            ->where('status', 'published')
            ->select('id', 'title')
            ->get();

        return view('pages.organizer.ads.create', compact('events', 'subscription'));
    }

    /**
     * Store new ad
     */
    public function store(Request $request)
    {
        $subscription = auth()->user()->activeAdSubscription();
        
        if (!$subscription) {
            return back()->with('error', 'Anda perlu berlangganan paket iklan terlebih dahulu.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:banner,sponsored_event,popup,sidebar',
            'position' => 'required|in:top,bottom,sidebar,popup,between_events',
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'click_url' => 'nullable|url',
            'event_id' => 'nullable|exists:events,id',
            'daily_budget' => 'required|numeric|min:10000',
            'total_budget' => 'required|numeric|min:50000',
            'start_date' => 'nullable|date|after:now',
            'end_date' => 'nullable|date|after:start_date',
            'targeting' => 'nullable|array',
        ]);

        try {
            // Upload image
            $imagePath = null;
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('ads', 'public');
            }

            $ad = Ad::create([
                'title' => $request->title,
                'description' => $request->description,
                'type' => $request->type,
                'position' => $request->position,
                'image_url' => $imagePath,
                'click_url' => $request->click_url,
                'targeting' => $request->targeting,
                'daily_budget' => $request->daily_budget,
                'total_budget' => $request->total_budget,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'advertiser_id' => auth()->id(),
                'event_id' => $request->event_id,
                'priority' => $subscription->priority_placement ? 5 : 1,
                'status' => 'pending',
            ]);

            return redirect()->route('organizer.ads.show', $ad)
                ->with('success', 'Iklan berhasil dibuat dan menunggu persetujuan admin.');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat membuat iklan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show ad details
     */
    public function show(Ad $ad)
    {
        $this->authorize('view', $ad);
        
        $ad->load('event');
        
        // Get analytics
        $analytics = AdAnalytic::getSummaryForAd($ad->id, now()->subDays(30), now());
        
        return view('pages.organizer.ads.show', compact('ad', 'analytics'));
    }

    /**
     * Show edit form
     */
    public function edit(Ad $ad)
    {
        $this->authorize('update', $ad);

        if ($ad->status === 'approved') {
            return back()->with('error', 'Iklan yang sudah disetujui tidak dapat diedit.');
        }

        $events = auth()->user()->organizedEvents()
            ->where('status', 'published')
            ->select('id', 'title')
            ->get();

        return view('pages.organizer.ads.edit', compact('ad', 'events'));
    }

    /**
     * Update ad
     */
    public function update(Request $request, Ad $ad)
    {
        $this->authorize('update', $ad);

        if ($ad->status === 'approved') {
            return back()->with('error', 'Iklan yang sudah disetujui tidak dapat diedit.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:banner,sponsored_event,popup,sidebar',
            'position' => 'required|in:top,bottom,sidebar,popup,between_events',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'click_url' => 'nullable|url',
            'event_id' => 'nullable|exists:events,id',
            'daily_budget' => 'required|numeric|min:10000',
            'total_budget' => 'required|numeric|min:50000',
            'start_date' => 'nullable|date|after:now',
            'end_date' => 'nullable|date|after:start_date',
            'targeting' => 'nullable|array',
        ]);

        try {
            $updateData = [
                'title' => $request->title,
                'description' => $request->description,
                'type' => $request->type,
                'position' => $request->position,
                'click_url' => $request->click_url,
                'targeting' => $request->targeting,
                'daily_budget' => $request->daily_budget,
                'total_budget' => $request->total_budget,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'event_id' => $request->event_id,
                'status' => 'pending', // Reset to pending after edit
            ];

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($ad->image_url) {
                    Storage::disk('public')->delete($ad->image_url);
                }
                
                $updateData['image_url'] = $request->file('image')->store('ads', 'public');
            }

            $ad->update($updateData);

            return redirect()->route('organizer.ads.show', $ad)
                ->with('success', 'Iklan berhasil diperbarui dan menunggu persetujuan admin.');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat memperbarui iklan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Delete ad
     */
    public function destroy(Ad $ad)
    {
        $this->authorize('delete', $ad);

        try {
            // Delete image
            if ($ad->image_url) {
                Storage::disk('public')->delete($ad->image_url);
            }

            $ad->delete();

            return redirect()->route('organizer.ads.index')
                ->with('success', 'Iklan berhasil dihapus.');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat menghapus iklan: ' . $e->getMessage());
        }
    }

    /**
     * Pause ad
     */
    public function pause(Ad $ad)
    {
        $this->authorize('update', $ad);

        $ad->update(['is_active' => false]);

        return back()->with('success', 'Iklan berhasil dijeda.');
    }

    /**
     * Resume ad
     */
    public function resume(Ad $ad)
    {
        $this->authorize('update', $ad);

        if ($ad->status !== 'approved') {
            return back()->with('error', 'Hanya iklan yang disetujui yang dapat dilanjutkan.');
        }

        $ad->update(['is_active' => true]);

        return back()->with('success', 'Iklan berhasil dilanjutkan.');
    }

    /**
     * Show subscriptions
     */
    public function subscriptions()
    {
        $currentSubscription = auth()->user()->activeAdSubscription();
        $subscriptionHistory = auth()->user()->adSubscriptions()
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $plans = AdSubscription::getPlans();

        return view('pages.organizer.ads.subscriptions', compact(
            'currentSubscription', 
            'subscriptionHistory', 
            'plans'
        ));
    }

    /**
     * Subscribe to ad plan
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'plan_type' => 'required|in:basic,premium,enterprise',
            'billing_cycle' => 'required|in:monthly,yearly',
        ]);

        $plans = AdSubscription::getPlans();
        $plan = $plans[$request->plan_type];

        $price = $request->billing_cycle === 'yearly' ? $plan['yearly_price'] : $plan['monthly_price'];
        $duration = $request->billing_cycle === 'yearly' ? 12 : 1;

        try {
            // Cancel existing subscription if any
            $existingSubscription = auth()->user()->activeAdSubscription();
            if ($existingSubscription) {
                $existingSubscription->cancel('Upgraded to new plan');
            }

            $subscription = AdSubscription::create([
                'user_id' => auth()->id(),
                'plan_type' => $request->plan_type,
                'plan_name' => $plan['name'],
                'plan_description' => $plan['description'],
                'monthly_price' => $plan['monthly_price'],
                'yearly_price' => $plan['yearly_price'],
                'billing_cycle' => $request->billing_cycle,
                'max_ads' => $plan['max_ads'],
                'max_impressions_per_day' => $plan['max_impressions_per_day'],
                'max_clicks_per_day' => $plan['max_clicks_per_day'],
                'features' => $plan['features'],
                'priority_placement' => $plan['priority_placement'],
                'analytics_access' => $plan['analytics_access'],
                'custom_targeting' => $plan['custom_targeting'],
                'starts_at' => now(),
                'expires_at' => now()->addMonths($duration),
                'amount_paid' => $price,
                'last_payment_at' => now(),
                'next_payment_at' => now()->addMonths($duration),
                'status' => 'active',
            ]);

            return redirect()->route('organizer.ads.subscriptions')
                ->with('success', 'Berhasil berlangganan paket ' . $plan['name'] . '!');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat berlangganan: ' . $e->getMessage());
        }
    }
}
