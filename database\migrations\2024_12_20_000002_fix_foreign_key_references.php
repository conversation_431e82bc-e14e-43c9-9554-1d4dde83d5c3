<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            echo "Fixing foreign key references...\n";

            // Fix orders table - change tiket_id to event_id
            if (Schema::hasTable('orders')) {
                echo "Fixing orders table foreign keys...\n";

                // Drop existing foreign key if exists
                $this->dropForeignKeyIfExists('orders', 'orders_tiket_id_foreign');

                // Check if tiket_id column exists
                if (Schema::hasColumn('orders', 'tiket_id')) {
                    // Rename tiket_id to event_id
                    Schema::table('orders', function (Blueprint $table) {
                        $table->renameColumn('tiket_id', 'event_id');
                    });
                    echo "Renamed tiket_id to event_id in orders table\n";
                }

                // Add correct foreign key
                if (Schema::hasTable('events') && Schema::hasColumn('orders', 'event_id')) {
                    Schema::table('orders', function (Blueprint $table) {
                        $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
                    });
                    echo "Added correct foreign key for orders.event_id\n";
                }
            }

            // Fix tickets table - change tiket_id to event_id
            if (Schema::hasTable('tickets')) {
                echo "Fixing tickets table foreign keys...\n";

                // Drop existing foreign key if exists
                $this->dropForeignKeyIfExists('tickets', 'tickets_tiket_id_foreign');

                // Check if tiket_id column exists
                if (Schema::hasColumn('tickets', 'tiket_id')) {
                    // Rename tiket_id to event_id
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->renameColumn('tiket_id', 'event_id');
                    });
                    echo "Renamed tiket_id to event_id in tickets table\n";
                }

                // Add correct foreign key
                if (Schema::hasTable('events') && Schema::hasColumn('tickets', 'event_id')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
                    });
                    echo "Added correct foreign key for tickets.event_id\n";
                }
            }

            // Fix any remaining foreign key issues
            $this->fixRemainingForeignKeys();

        } catch (Exception $e) {
            echo "Error during foreign key fix: " . $e->getMessage() . "\n";
            // Don't throw the exception to prevent migration failure
        } finally {
            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration fixes critical issues, reversing could cause problems
        // Only reverse if absolutely necessary

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            // Reverse orders table changes
            if (Schema::hasTable('orders') && Schema::hasColumn('orders', 'event_id')) {
                $this->dropForeignKeyIfExists('orders', 'orders_event_id_foreign');

                Schema::table('orders', function (Blueprint $table) {
                    $table->renameColumn('event_id', 'tiket_id');
                });
            }

            // Reverse tickets table changes
            if (Schema::hasTable('tickets') && Schema::hasColumn('tickets', 'event_id')) {
                $this->dropForeignKeyIfExists('tickets', 'tickets_event_id_foreign');

                Schema::table('tickets', function (Blueprint $table) {
                    $table->renameColumn('event_id', 'tiket_id');
                });
            }

        } catch (Exception $e) {
            echo "Error during rollback: " . $e->getMessage() . "\n";
        } finally {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }

    /**
     * Drop foreign key constraint if it exists
     */
    private function dropForeignKeyIfExists(string $table, string $constraintName): void
    {
        try {
            $exists = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.TABLE_CONSTRAINTS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = ?
                AND CONSTRAINT_NAME = ?
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            ", [$table, $constraintName]);

            if (count($exists) > 0) {
                DB::statement("ALTER TABLE `{$table}` DROP FOREIGN KEY `{$constraintName}`");
                echo "Dropped foreign key: {$constraintName}\n";
            }
        } catch (Exception $e) {
            echo "Could not drop foreign key {$constraintName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Fix remaining foreign key constraints
     */
    private function fixRemainingForeignKeys(): void
    {
        try {
            // Ensure all other foreign keys are correct

            // Fix orders table foreign keys
            if (Schema::hasTable('orders')) {
                // User foreign key
                if (!$this->foreignKeyExists('orders', 'orders_user_id_foreign')) {
                    Schema::table('orders', function (Blueprint $table) {
                        $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }
            }

            // Fix tickets table foreign keys
            if (Schema::hasTable('tickets')) {
                // Buyer foreign key
                if (!$this->foreignKeyExists('tickets', 'tickets_buyer_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('buyer_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }

                // Order foreign key
                if (Schema::hasTable('orders') && !$this->foreignKeyExists('tickets', 'tickets_order_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
                    });
                }

                // Validated by foreign key
                if (!$this->foreignKeyExists('tickets', 'tickets_validated_by_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('validated_by')->references('id')->on('users');
                    });
                }
            }

            // Fix tickets table foreign keys
            if (Schema::hasTable('tickets')) {
                // Category foreign key
                if (Schema::hasTable('categories') && !$this->foreignKeyExists('tickets', 'tickets_category_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
                    });
                }

                // Organizer foreign key
                if (!$this->foreignKeyExists('tickets', 'tickets_organizer_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('organizer_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }
            }

        } catch (Exception $e) {
            echo "Error fixing remaining foreign keys: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Check if foreign key constraint exists
     */
    private function foreignKeyExists(string $table, string $constraintName): bool
    {
        try {
            $result = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.TABLE_CONSTRAINTS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = ?
                AND CONSTRAINT_NAME = ?
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            ", [$table, $constraintName]);

            return count($result) > 0;
        } catch (Exception $e) {
            return false;
        }
    }
};
