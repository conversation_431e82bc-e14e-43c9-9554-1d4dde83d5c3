@extends('layouts.app')

@section('title', 'Staff Dashboard - TiXara')

@push('styles')
<style>
.scanner-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}

.scanner-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.pulse-dot {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.validation-status {
    position: relative;
}

.validation-status::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transform: translateY(-50%);
}

.validation-status.valid::before {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.validation-status.invalid::before {
    background: #ef4444;
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.event-card {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.event-card.active {
    border-left-color: #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.05) 0%, transparent 100%);
}

.event-card.upcoming {
    border-left-color: #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.05) 0%, transparent 100%);
}
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Dashboard Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">👥 Staff Dashboard</h1>
                <p class="text-gray-600">Kelola validasi tiket dan pantau aktivitas event secara real-time</p>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <!-- Live Status -->
                <div class="flex items-center px-4 py-2 bg-green-100 rounded-lg">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2 pulse-dot"></div>
                    <span class="text-green-700 font-medium text-sm">Online</span>
                </div>

                <!-- Current Time -->
                <div class="flex items-center px-4 py-2 bg-gray-100 rounded-lg">
                    <i data-lucide="clock" class="w-4 h-4 mr-2 text-gray-600"></i>
                    <span class="text-gray-700 font-medium text-sm" id="currentTime">{{ now()->format('H:i:s') }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- QR Scanner -->
        <div class="scanner-card rounded-xl shadow-lg p-6 text-white" data-aos="fade-up" data-aos-delay="100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-white/20 rounded-lg backdrop-blur-sm">
                    <i data-lucide="scan" class="w-6 h-6"></i>
                </div>
                <div class="text-right">
                    <p class="text-white/80 text-sm">QR Scanner</p>
                    <p class="text-xl font-bold">Validasi Tiket</p>
                </div>
            </div>
            <a href="{{ route('staff.scanner') }}"
               class="block w-full bg-white/20 backdrop-blur-sm text-white text-center py-3 rounded-lg hover:bg-white/30 transition-all duration-300 font-semibold">
                <i data-lucide="camera" class="w-4 h-4 inline mr-2"></i>
                Buka Scanner
            </a>
        </div>

        <!-- Today's Validations -->
        <div class="stat-card bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-100 rounded-lg">
                    <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Validasi Hari Ini</p>
                    <p class="text-3xl font-bold text-gray-900" id="todayValidations">{{ $stats['today_validations'] ?? 0 }}</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-green-600 font-medium">{{ $stats['valid_tickets'] ?? 0 }} Valid</span>
                </div>
                <span class="text-red-600">{{ $stats['invalid_tickets'] ?? 0 }} Invalid</span>
            </div>
            <div class="mt-4">
                <div class="bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 rounded-full h-2 transition-all duration-500" style="width: {{ $stats['today_validations'] > 0 ? ($stats['valid_tickets'] / $stats['today_validations']) * 100 : 0 }}%"></div>
                </div>
                <p class="text-xs text-gray-600 mt-1">{{ $stats['today_validations'] > 0 ? round(($stats['valid_tickets'] / $stats['today_validations']) * 100, 1) : 0 }}% Success Rate</p>
            </div>
        </div>

        <!-- Active Events -->
        <div class="stat-card bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <i data-lucide="calendar-check" class="w-6 h-6 text-blue-600"></i>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Event Aktif</p>
                    <p class="text-3xl font-bold text-gray-900">{{ $stats['active_events'] ?? 0 }}</p>
                </div>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-2 pulse-dot"></div>
                    <span class="text-blue-600 font-medium">Berlangsung</span>
                </div>
                <span class="text-gray-600">{{ $stats['upcoming_events'] ?? 0 }} Upcoming</span>
            </div>
        </div>

        <!-- Performance Score -->
        <div class="stat-card bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="400">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-purple-100 rounded-lg">
                    <i data-lucide="award" class="w-6 h-6 text-purple-600"></i>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Performance</p>
                    <p class="text-3xl font-bold text-gray-900">{{ $stats['performance_score'] ?? 95 }}%</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                <span class="text-green-600 font-medium">Excellent</span>
            </div>
            <div class="mt-4">
                <div class="bg-gray-200 rounded-full h-2">
                    <div class="bg-purple-500 rounded-full h-2 transition-all duration-500" style="width: {{ $stats['performance_score'] ?? 95 }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Bar -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8" data-aos="fade-up" data-aos-delay="500">
        <button onclick="openQuickScanner()" class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <i data-lucide="scan-line" class="w-5 h-5 text-primary mr-2 group-hover:scale-110 transition-transform"></i>
            <span class="font-medium text-gray-700">Quick Scan</span>
        </button>

        <button onclick="showTodayEvents()" class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <i data-lucide="calendar-days" class="w-5 h-5 text-blue-600 mr-2 group-hover:scale-110 transition-transform"></i>
            <span class="font-medium text-gray-700">Today's Events</span>
        </button>

        <button onclick="showValidationHistory()" class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <i data-lucide="history" class="w-5 h-5 text-green-600 mr-2 group-hover:scale-110 transition-transform"></i>
            <span class="font-medium text-gray-700">History</span>
        </button>

        <button onclick="showHelp()" class="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group">
            <i data-lucide="help-circle" class="w-5 h-5 text-purple-600 mr-2 group-hover:scale-110 transition-transform"></i>
            <span class="font-medium text-gray-700">Help</span>
        </button>
    </div>

    <!-- Recent Validations -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-8" data-aos="fade-up" data-aos-delay="400">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Validasi Terbaru</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-primary text-white rounded-lg">Hari Ini</button>
                    <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">Minggu Ini</button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tiket
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Event
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pembeli
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Waktu
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                <p>Belum ada validasi tiket hari ini</p>
                                <p class="text-sm mt-1">Gunakan scanner untuk memvalidasi tiket</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Today's Tickets -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="500">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Event Hari Ini</h2>
                <span class="text-sm text-gray-600">{{ now()->format('d M Y') }}</span>
            </div>

            <div class="space-y-4">
                <div class="text-center py-12">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <p class="text-gray-600">Tidak ada event yang berlangsung hari ini</p>
                    <p class="text-sm text-gray-500 mt-1">Event akan muncul di sini saat jadwal dimulai</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Help -->
    <div class="mt-8 bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6" data-aos="fade-up" data-aos-delay="600">
        <div class="flex items-start space-x-4">
            <div class="p-2 bg-primary/20 rounded-lg">
                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
            <div class="flex-1">
                <h3 class="font-semibold text-gray-900 mb-2">Panduan Cepat</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-1">Validasi Tiket:</h4>
                        <ul class="space-y-1">
                            <li>• Buka scanner QR code</li>
                            <li>• Arahkan kamera ke QR tiket</li>
                            <li>• Sistem akan otomatis memvalidasi</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 mb-1">Tips Penting:</h4>
                        <ul class="space-y-1">
                            <li>• Pastikan pencahayaan cukup</li>
                            <li>• QR code dalam kondisi baik</li>
                            <li>• Periksa status tiket sebelum validasi</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scanner Modal (if needed) -->
<div id="scanner-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">QR Scanner</h3>
                <button onclick="closeScanner()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div id="scanner-container" class="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Scanner akan dimuat di sini</p>
            </div>
            <div class="mt-4 text-center">
                <p class="text-sm text-gray-600">Arahkan kamera ke QR code tiket</p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update time every second
    updateTime();
    setInterval(updateTime, 1000);

    // Auto-refresh stats every 30 seconds
    setInterval(refreshStats, 30000);

    // Initialize Lucide icons
    if (window.lucide) {
        window.lucide.createIcons();
    }
});

// Update current time
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('currentTime').textContent = timeString;
}

// Refresh dashboard stats
async function refreshStats() {
    try {
        const response = await fetch('/staff/dashboard/stats');
        const data = await response.json();

        // Update validation count
        document.getElementById('todayValidations').textContent = data.today_validations;

        // Update other stats if needed
        console.log('Stats refreshed:', data);
    } catch (error) {
        console.error('Error refreshing stats:', error);
    }
}

// Quick scanner function
function openQuickScanner() {
    // Open scanner modal or redirect to scanner page
    window.location.href = '{{ route("staff.scanner") }}';
}

// Show today's events
function showTodayEvents() {
    const modal = document.getElementById('eventsModal');
    if (modal) {
        modal.classList.remove('hidden');
        loadTodayEvents();
    }
}

// Show validation history
function showValidationHistory() {
    const modal = document.getElementById('historyModal');
    if (modal) {
        modal.classList.remove('hidden');
        loadValidationHistory();
    }
}

// Show help
function showHelp() {
    const modal = document.getElementById('helpModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

// Load today's events
async function loadTodayEvents() {
    try {
        const response = await fetch('/staff/dashboard/today-events');
        const events = await response.json();

        const container = document.getElementById('todayEventsContainer');
        if (container) {
            container.innerHTML = events.map(event => `
                <div class="event-card p-4 rounded-lg border ${(event.status || 'upcoming') === 'active' ? 'active' : 'upcoming'}">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold text-gray-900">${event.title || 'Unknown Event'}</h4>
                            <p class="text-sm text-gray-600">${event.venue_name || 'Unknown Venue'}</p>
                            <p class="text-xs text-gray-500">${event.start_time || '00:00'} - ${event.end_time || '00:00'}</p>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 text-xs rounded-full ${(event.status || 'upcoming') === 'active' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}">
                                ${(event.status || 'upcoming') === 'active' ? 'Active' : 'Upcoming'}
                            </span>
                            <p class="text-sm text-gray-600 mt-1">${event.attendees || 0}/${event.capacity || 0}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    } catch (error) {
        console.error('Error loading events:', error);
    }
}

// Load validation history
async function loadValidationHistory() {
    try {
        const response = await fetch('/staff/dashboard/validation-history');
        const validations = await response.json();

        const container = document.getElementById('validationHistoryContainer');
        if (container) {
            container.innerHTML = validations.map(validation => `
                <div class="flex items-center justify-between p-3 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-center">
                        <div class="validation-status ${validation.status || 'unknown'} mr-3">
                            <span class="text-sm font-medium">${validation.ticket_code || 'Unknown'}</span>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">${validation.event_title || 'Unknown Event'}</p>
                            <p class="text-sm text-gray-600">${validation.customer_name || 'Unknown Customer'}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="px-2 py-1 text-xs rounded-full ${(validation.status || 'unknown') === 'valid' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}">
                            ${(validation.status || 'unknown') === 'valid' ? 'Valid' : 'Invalid'}
                        </span>
                        <p class="text-xs text-gray-500 mt-1">${validation.validated_at || 'Unknown time'}</p>
                    </div>
                </div>
            `).join('');
        }
    } catch (error) {
        console.error('Error loading validation history:', error);
    }
}

// Close modal functions
function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Scanner functions
function openScanner() {
    document.getElementById('scanner-modal').classList.remove('hidden');
    // Initialize scanner here
}

function closeScanner() {
    document.getElementById('scanner-modal').classList.add('hidden');
    // Stop scanner here
}

// Real-time notifications (if WebSocket is available)
if (window.Echo) {
    window.Echo.channel('staff-dashboard')
        .listen('TicketValidated', (e) => {
            // Update stats in real-time
            refreshStats();

            // Show notification
            showNotification(`Tiket ${e.ticket_code} berhasil divalidasi`, 'success');
        });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endpush

@endsection
