<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BalanceTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_number',
        'user_id',
        'type',
        'amount',
        'balance_before',
        'balance_after',
        'status',
        'description',
        'metadata',
        'payment_method',
        'payment_reference',
        'payment_data',
        'approved_by',
        'approved_at',
        'admin_notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'metadata' => 'array',
        'payment_data' => 'array',
        'approved_at' => 'datetime',
    ];

    // Transaction types
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_EARNING = 'earning';
    const TYPE_FEE = 'fee';
    const TYPE_REFUND = 'refund';
    const TYPE_ADJUSTMENT = 'adjustment';

    // Transaction statuses
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Approved by admin relationship
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if transaction is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if transaction is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if transaction is withdrawal
     */
    public function isWithdrawal(): bool
    {
        return $this->type === self::TYPE_WITHDRAWAL;
    }

    /**
     * Check if transaction is deposit
     */
    public function isDeposit(): bool
    {
        return $this->type === self::TYPE_DEPOSIT;
    }

    /**
     * Approve withdrawal transaction
     */
    public function approve(User $admin, string $notes = null): bool
    {
        if (!$this->isPending() || !$this->isWithdrawal()) {
            return false;
        }

        $userBalance = $this->user->userBalance;

        // Deduct from actual balance and pending balance
        $userBalance->decrement('balance', abs($this->amount));
        $userBalance->decrement('pending_balance', abs($this->amount));
        $userBalance->increment('total_withdrawn', abs($this->amount));

        $this->update([
            'status' => self::STATUS_COMPLETED,
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'admin_notes' => $notes,
            'balance_after' => $userBalance->fresh()->balance,
        ]);

        return true;
    }

    /**
     * Reject withdrawal transaction
     */
    public function reject(User $admin, string $reason = null): bool
    {
        if (!$this->isPending() || !$this->isWithdrawal()) {
            return false;
        }

        $userBalance = $this->user->userBalance;

        // Return pending balance
        $userBalance->decrement('pending_balance', abs($this->amount));

        $this->update([
            'status' => self::STATUS_CANCELLED,
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'admin_notes' => $reason,
        ]);

        return true;
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        $prefix = $this->amount >= 0 ? '+' : '';
        return $prefix . 'Rp ' . number_format(abs($this->amount), 0, ',', '.');
    }

    /**
     * Get transaction type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            self::TYPE_DEPOSIT => 'Deposit',
            self::TYPE_WITHDRAWAL => 'Penarikan',
            self::TYPE_EARNING => 'Pendapatan',
            self::TYPE_FEE => 'Biaya Platform',
            self::TYPE_REFUND => 'Refund',
            self::TYPE_ADJUSTMENT => 'Penyesuaian',
            default => 'Transaksi'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Menunggu',
            self::STATUS_COMPLETED => 'Selesai',
            self::STATUS_FAILED => 'Gagal',
            self::STATUS_CANCELLED => 'Dibatalkan',
            default => 'Unknown'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_CANCELLED => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for withdrawals
     */
    public function scopeWithdrawals($query)
    {
        return $query->where('type', self::TYPE_WITHDRAWAL);
    }

    /**
     * Scope for deposits
     */
    public function scopeDeposits($query)
    {
        return $query->where('type', self::TYPE_DEPOSIT);
    }
}
