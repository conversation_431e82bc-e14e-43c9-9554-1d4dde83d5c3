<?php $__env->startSection('title', '<PERSON><PERSON><PERSON>'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Reports Styles */
.reports-hero {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    position: relative;
    overflow: hidden;
}

.reports-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="reports" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23reports)"/></svg>');
    opacity: 0.3;
}

.report-card {
    background: white;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.report-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc2626, #b91c1c);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.report-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #dc2626;
}

.report-card:hover::before {
    transform: scaleX(1);
}

.form-control {
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #dc2626;
    box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
}

.btn-report {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    padding: 16px 24px;
    border: none;
    border-radius: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn-report:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(220, 38, 38, 0.3);
    color: white;
}

.priority-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.low {
    background: #dcfce7;
    color: #166534;
}

.priority-badge.medium {
    background: #fef3c7;
    color: #92400e;
}

.priority-badge.high {
    background: #fee2e2;
    color: #991b1b;
}

.priority-badge.urgent {
    background: #1f2937;
    color: #f9fafb;
}

@media (max-width: 768px) {
    .report-card {
        padding: 24px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="reports-hero text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8 backdrop-blur-sm">
                <i data-lucide="flag" class="w-10 h-10 text-white"></i>
            </div>
            <h1 class="text-5xl md:text-6xl font-bold mb-6">
                Laporan Pengaduan
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                Laporkan masalah atau pelanggaran di TiXara
            </p>
            <p class="text-lg mb-12 max-w-2xl mx-auto opacity-80">
                Bantu kami menjaga keamanan dan kualitas platform dengan melaporkan masalah yang Anda temui.
            </p>
        </div>
    </div>
</div>

<!-- Report Form -->
<div class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="report-card">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Buat Laporan Baru
                </h2>
                <p class="text-gray-600 dark:text-gray-300">
                    Isi form di bawah ini untuk melaporkan masalah atau pelanggaran
                </p>
            </div>

            <form id="reportForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="reportType" class="form-label">Jenis Laporan</label>
                        <select class="form-control" id="reportType" name="type" required>
                            <option value="">Pilih jenis laporan</option>
                            <option value="bug">Bug/Error Sistem</option>
                            <option value="security">Masalah Keamanan</option>
                            <option value="content">Konten Tidak Pantas</option>
                            <option value="user">Perilaku Pengguna</option>
                            <option value="event">Masalah Event</option>
                            <option value="payment">Masalah Pembayaran</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div>
                        <label for="reportPriority" class="form-label">Prioritas</label>
                        <select class="form-control" id="reportPriority" name="priority" required>
                            <option value="">Pilih prioritas</option>
                            <option value="low">Rendah</option>
                            <option value="medium" selected>Sedang</option>
                            <option value="high">Tinggi</option>
                            <option value="urgent">Mendesak</option>
                        </select>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="reportTitle" class="form-label">Judul Laporan</label>
                    <input type="text" class="form-control" id="reportTitle" name="title" 
                           placeholder="Ringkasan singkat masalah" required>
                </div>

                <div class="mb-6">
                    <label for="reportDescription" class="form-label">Deskripsi Detail</label>
                    <textarea class="form-control" id="reportDescription" name="description" 
                              rows="6" placeholder="Jelaskan masalah secara detail..." required></textarea>
                    <div class="form-text">Sertakan langkah-langkah untuk mereproduksi masalah jika memungkinkan</div>
                </div>

                <div class="mb-6">
                    <label for="reportUrl" class="form-label">URL Terkait (Opsional)</label>
                    <input type="url" class="form-control" id="reportUrl" name="url" 
                           placeholder="https://tixara.my.id/halaman-bermasalah">
                </div>

                <div class="mb-6">
                    <label for="reportScreenshot" class="form-label">Screenshot (Opsional)</label>
                    <input type="file" class="form-control" id="reportScreenshot" name="screenshot" 
                           accept="image/*">
                    <div class="form-text">Upload screenshot untuk membantu kami memahami masalah (Max: 5MB)</div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="contactEmail" class="form-label">Email Kontak</label>
                        <input type="email" class="form-control" id="contactEmail" name="contact_email" 
                               value="<?php echo e(auth()->check() ? auth()->user()->email : ''); ?>"
                               placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="contactPhone" class="form-label">Nomor Telepon (Opsional)</label>
                        <input type="tel" class="form-control" id="contactPhone" name="contact_phone" 
                               placeholder="+62 812-3456-7890">
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Penting untuk Diketahui:</h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Laporan akan ditinjau dalam 24-48 jam</li>
                                <li>• Berikan informasi yang akurat dan lengkap</li>
                                <li>• Laporan palsu dapat mengakibatkan sanksi</li>
                                <li>• Kami akan menghubungi Anda jika diperlukan informasi tambahan</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4">
                    <button type="button" class="btn btn-secondary flex-1" onclick="history.back()">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                        Kembali
                    </button>
                    <button type="submit" class="btn-report flex-1">
                        <i data-lucide="send" class="w-5 h-5"></i>
                        Kirim Laporan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Report Types Info -->
<div class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Jenis Laporan
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Pilih jenis laporan yang sesuai dengan masalah yang Anda alami
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="report-card text-center">
                <div class="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="bug" class="w-8 h-8 text-red-600 dark:text-red-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Bug/Error Sistem</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Laporkan error, bug, atau masalah teknis pada website
                </p>
            </div>

            <div class="report-card text-center">
                <div class="w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="shield-alert" class="w-8 h-8 text-orange-600 dark:text-orange-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Masalah Keamanan</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Laporkan celah keamanan atau aktivitas mencurigakan
                </p>
            </div>

            <div class="report-card text-center">
                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="eye-off" class="w-8 h-8 text-purple-600 dark:text-purple-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Konten Tidak Pantas</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Laporkan konten yang melanggar kebijakan platform
                </p>
            </div>

            <div class="report-card text-center">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="user-x" class="w-8 h-8 text-blue-600 dark:text-blue-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Perilaku Pengguna</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Laporkan perilaku tidak pantas dari pengguna lain
                </p>
            </div>

            <div class="report-card text-center">
                <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="calendar-x" class="w-8 h-8 text-green-600 dark:text-green-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Masalah Event</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Laporkan masalah terkait event atau organizer
                </p>
            </div>

            <div class="report-card text-center">
                <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="credit-card" class="w-8 h-8 text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Masalah Pembayaran</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    Laporkan masalah transaksi atau pembayaran
                </p>
            </div>
        </div>
    </div>
</div>

<?php if(auth()->check()): ?>
<!-- My Reports Link -->
<div class="py-12 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Lihat Laporan Anda
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-8">
            Pantau status laporan yang telah Anda kirimkan
        </p>
        <a href="<?php echo e(route('reports.my-reports')); ?>" class="btn-report inline-flex">
            <i data-lucide="list" class="w-5 h-5"></i>
            Laporan Saya
        </a>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
/*
 * Reports JavaScript
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Report form handling
    document.getElementById('reportForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Mengirim...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("reports.store")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                this.reset();
                
                // Redirect to my reports if user is logged in
                <?php if(auth()->check()): ?>
                setTimeout(() => {
                    window.location.href = '<?php echo e(route("reports.my-reports")); ?>';
                }, 2000);
                <?php endif; ?>
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat mengirim laporan');
                
                if (data.errors) {
                    Object.keys(data.errors).forEach(key => {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field) {
                            field.classList.add('is-invalid');
                            
                            // Remove existing error message
                            const existingError = field.parentNode.querySelector('.invalid-feedback');
                            if (existingError) {
                                existingError.remove();
                            }
                            
                            // Add new error message
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'invalid-feedback';
                            errorDiv.textContent = data.errors[key][0];
                            field.parentNode.appendChild(errorDiv);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Report submission error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    });

    // Clear validation errors on input
    document.querySelectorAll('.form-control').forEach(field => {
        field.addEventListener('input', function() {
            this.classList.remove('is-invalid');
            const errorDiv = this.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        });
    });
});

function showToast(type, message) {
    // Remove existing toasts
    document.querySelectorAll('.toast-notification').forEach(toast => toast.remove());
    
    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/reports/index.blade.php ENDPATH**/ ?>