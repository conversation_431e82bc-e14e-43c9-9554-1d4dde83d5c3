@props(['user', 'size' => 'md', 'showTooltip' => true])

@php
    $levelConfig = $user->getLevelConfig();
    $displayName = $user->getLevelDisplayName();
    $color = $user->getLevelColor();
    $background = $user->getLevelBackground();
    $icon = $levelConfig['icon'] ?? 'fas fa-star';
    $description = $levelConfig['description'] ?? '';
    
    // Size classes
    $sizeClasses = [
        'xs' => 'px-2 py-1 text-xs',
        'sm' => 'px-2 py-1 text-sm',
        'md' => 'px-3 py-1 text-sm',
        'lg' => 'px-4 py-2 text-base',
        'xl' => 'px-6 py-3 text-lg'
    ];
    
    $iconSizes = [
        'xs' => 'w-3 h-3',
        'sm' => 'w-3 h-3',
        'md' => 'w-4 h-4',
        'lg' => 'w-5 h-5',
        'xl' => 'w-6 h-6'
    ];
    
    $sizeClass = $sizeClasses[$size] ?? $sizeClasses['md'];
    $iconSize = $iconSizes[$size] ?? $iconSizes['md'];
@endphp

<div class="inline-flex items-center {{ $sizeClass }} rounded-full font-semibold text-white shadow-sm relative group"
     style="background: {{ $background }}; color: white;"
     @if($showTooltip) 
         title="{{ $description }}"
         data-tooltip="{{ $description }}"
     @endif>
    
    <!-- Icon -->
    @if(str_contains($icon, 'fas') || str_contains($icon, 'far') || str_contains($icon, 'fab'))
        <i class="{{ $icon }} {{ $iconSize }} mr-1"></i>
    @else
        <svg class="{{ $iconSize }} mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
        </svg>
    @endif
    
    <!-- Level Name -->
    <span>{{ $displayName }}</span>
    
    <!-- Tooltip (if enabled) -->
    @if($showTooltip && $description)
        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            {{ $description }}
            <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
    @endif
</div>

@push('styles')
<style>
    .user-level-badge {
        position: relative;
        display: inline-flex;
        align-items: center;
        border-radius: 9999px;
        font-weight: 600;
        color: white;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }
    
    .user-level-badge.star {
        background: linear-gradient(135deg, #FFA500, #FFD700);
    }
    
    .user-level-badge.star_plus {
        background: linear-gradient(135deg, #FF6B6B, #FF8E53);
    }
    
    .user-level-badge.premium {
        background: linear-gradient(135deg, #667eea, #764ba2);
    }
    
    .user-level-badge.platinum {
        background: linear-gradient(135deg, #C0C0C0, #E5E5E5);
        color: #333 !important;
    }
</style>
@endpush
