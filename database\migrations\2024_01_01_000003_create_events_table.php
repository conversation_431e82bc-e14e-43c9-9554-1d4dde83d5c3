<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('organizer_id')->constrained('users')->onDelete('cascade');

            // Event Details
            $table->string('venue_name');
            $table->text('venue_address');
            $table->string('city');
            $table->string('province');
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();

            // Date & Time
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->string('timezone')->default('Asia/Jakarta');

            // Pricing & Capacity
            $table->decimal('price', 12, 2);
            $table->decimal('original_price', 12, 2)->nullable(); // For discount display
            $table->integer('total_capacity');
            $table->integer('available_capacity');
            $table->integer('min_purchase')->default(1);
            $table->integer('max_purchase')->default(10);

            // Media
            $table->string('poster')->nullable();
            $table->json('gallery')->nullable(); // Array of image paths

            // Status & Settings
            $table->enum('status', ['draft', 'published', 'cancelled', 'completed'])->default('draft');
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_free')->default(false);
            $table->boolean('requires_approval')->default(false);

            // SEO & Meta
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->json('tags')->nullable(); // Array of tags

            // Sales Period
            $table->datetime('sale_start_date')->nullable();
            $table->datetime('sale_end_date')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['status', 'start_date']);
            $table->index(['category_id', 'status']);
            $table->index(['organizer_id', 'status']);
            $table->index(['city', 'status']);
            $table->index('is_featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
