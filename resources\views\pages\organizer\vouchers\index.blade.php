@extends('layouts.organizer')

@section('title', 'Daftar Voucher')

@push('styles')
<style>
/* Custom styles for Organizer Voucher Management */
.voucher-card {
    transition: all 0.3s ease;
}

.voucher-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.voucher-type-badge {
    transition: all 0.2s ease;
}

.voucher-usage-bar {
    transition: width 0.3s ease;
}

.voucher-status {
    position: relative;
}

.voucher-status::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    border-radius: 2px;
}

.status-active::before {
    background: #10b981;
}

.status-inactive::before {
    background: #6b7280;
}

.status-expired::before {
    background: #ef4444;
}

.status-exhausted::before {
    background: #f59e0b;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .voucher-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="percent" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Daftar Voucher</h1>
                            <p class="text-gray-600 dark:text-gray-400">Kelola voucher diskon untuk event Anda</p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="{{ route('organizer.vouchers.create') }}" 
                       class="inline-flex items-center px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Buat Voucher
                    </a>
                    
                    <a href="{{ route('organizer.dashboard') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Voucher Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stats-grid">
            <!-- Total Vouchers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Voucher</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_vouchers'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-emerald-600 dark:text-emerald-400 mt-1">
                            <i data-lucide="percent" class="w-3 h-3 inline mr-1"></i>
                            Semua voucher
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="percent" class="w-6 h-6 text-emerald-600 dark:text-emerald-400"></i>
                    </div>
                </div>
            </div>

            <!-- Active Vouchers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Voucher Aktif</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['active_vouchers'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                            <i data-lucide="check-circle" class="w-3 h-3 inline mr-1"></i>
                            Dapat digunakan
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="check-circle" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Used Vouchers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Penggunaan</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_usage'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            <i data-lucide="shopping-cart" class="w-3 h-3 inline mr-1"></i>
                            Voucher terpakai
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="shopping-cart" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>

            <!-- Total Discount -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 voucher-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Diskon</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            Rp {{ number_format($stats['total_discount'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-purple-600 dark:text-purple-400 mt-1">
                            <i data-lucide="tag" class="w-3 h-3 inline mr-1"></i>
                            Diskon diberikan
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="tag" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters & Search -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4 flex-1">
                    <!-- Search -->
                    <div class="relative flex-1 max-w-md">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-5 h-5 text-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="searchInput"
                               placeholder="Cari voucher..." 
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200">
                    </div>
                    
                    <!-- Type Filter -->
                    <select id="typeFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200">
                        <option value="">Semua Tipe</option>
                        <option value="percentage">Persentase</option>
                        <option value="fixed">Nominal Tetap</option>
                    </select>
                    
                    <!-- Status Filter -->
                    <select id="statusFilter" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200">
                        <option value="">Semua Status</option>
                        <option value="active">Aktif</option>
                        <option value="inactive">Tidak Aktif</option>
                        <option value="expired">Kadaluarsa</option>
                        <option value="exhausted">Habis</option>
                    </select>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                    <button onclick="refreshData()" 
                            class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Vouchers Table -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Daftar Voucher</h2>

                    <!-- Bulk Actions -->
                    <div class="flex items-center gap-3">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="selectAll"
                                   class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                            <label for="selectAll" class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                                Pilih Semua
                            </label>
                        </div>

                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="inline-flex items-center px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                                <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                            </button>

                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                                <div class="py-1">
                                    <a href="#" onclick="bulkActivate()"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <i data-lucide="check-circle" class="w-4 h-4 mr-3"></i>
                                        Aktifkan Selected
                                    </a>
                                    <a href="#" onclick="bulkDeactivate()"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <i data-lucide="x-circle" class="w-4 h-4 mr-3"></i>
                                        Nonaktifkan Selected
                                    </a>
                                    <a href="#" onclick="bulkDelete()"
                                       class="flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20">
                                        <i data-lucide="trash-2" class="w-4 h-4 mr-3"></i>
                                        Hapus Selected
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table Content -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Voucher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Event
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Tipe & Nilai
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Penggunaan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Berlaku Hingga
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Aksi
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($vouchers ?? [] as $voucher)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox"
                                       value="{{ $voucher->id ?? '' }}"
                                       class="voucher-checkbox rounded border-gray-300 text-emerald-600 focus:ring-emerald-500">
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-start">
                                    <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-sm mr-4 flex-shrink-0">
                                        <i data-lucide="percent" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white voucher-status {{ 'status-' . ($voucher->status ?? 'active') }}">
                                            {{ $voucher->code ?? 'VOUCHER-CODE' }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            {{ $voucher->name ?? 'Voucher Name' }}
                                        </div>
                                        @if($voucher->description ?? false)
                                        <div class="text-xs text-gray-400 dark:text-gray-500 mt-1 line-clamp-1">
                                            {{ Str::limit($voucher->description, 50) }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $voucher->event->title ?? 'All Events' }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $voucher->applicable_events ?? 'Semua event' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $type = $voucher->type ?? 'percentage';
                                    $value = $voucher->value ?? 0;
                                @endphp
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    @if($type === 'percentage')
                                        {{ $value }}% OFF
                                    @else
                                        Rp {{ number_format($value, 0, ',', '.') }}
                                    @endif
                                </div>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $type === 'percentage' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' }}">
                                    {{ $type === 'percentage' ? 'Persentase' : 'Nominal' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $used = $voucher->used_count ?? 0;
                                    $limit = $voucher->usage_limit ?? 0;
                                    $percentage = $limit > 0 ? ($used / $limit) * 100 : 0;
                                @endphp
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $used }}{{ $limit > 0 ? '/' . $limit : '' }} digunakan
                                </div>
                                @if($limit > 0)
                                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                                    <div class="voucher-usage-bar bg-emerald-600 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    {{ number_format($percentage, 1) }}% terpakai
                                </div>
                                @else
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    Unlimited
                                </div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $status = $voucher->status ?? 'active';
                                    $statusColors = [
                                        'active' => 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
                                        'inactive' => 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
                                        'expired' => 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
                                        'exhausted' => 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
                                    ];
                                    $statusIcons = [
                                        'active' => 'check-circle',
                                        'inactive' => 'x-circle',
                                        'expired' => 'clock',
                                        'exhausted' => 'alert-circle'
                                    ];
                                @endphp
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$status] ?? $statusColors['active'] }}">
                                    <i data-lucide="{{ $statusIcons[$status] ?? $statusIcons['active'] }}" class="w-3 h-3 mr-1"></i>
                                    {{ ucfirst($status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                @if($voucher->expires_at ?? false)
                                    {{ \Carbon\Carbon::parse($voucher->expires_at)->format('d M Y') }}
                                    <div class="text-xs {{ \Carbon\Carbon::parse($voucher->expires_at)->isPast() ? 'text-red-500' : 'text-gray-400' }}">
                                        {{ \Carbon\Carbon::parse($voucher->expires_at)->diffForHumans() }}
                                    </div>
                                @else
                                    <span class="text-gray-400">Tidak ada batas</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center gap-2 voucher-actions">
                                    <a href="{{ route('organizer.vouchers.show', $voucher) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200">
                                        <i data-lucide="eye" class="w-4 h-4 mr-1"></i>
                                        Detail
                                    </a>

                                    <a href="{{ route('organizer.vouchers.edit', $voucher) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors duration-200">
                                        <i data-lucide="edit" class="w-4 h-4 mr-1"></i>
                                        Edit
                                    </a>

                                    @if($voucher->status === 'active')
                                    <button onclick="toggleVoucherStatus('{{ $voucher->id }}', 'inactive')"
                                            class="inline-flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200">
                                        <i data-lucide="pause" class="w-4 h-4 mr-1"></i>
                                        Nonaktif
                                    </button>
                                    @else
                                    <button onclick="toggleVoucherStatus('{{ $voucher->id }}', 'active')"
                                            class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors duration-200">
                                        <i data-lucide="play" class="w-4 h-4 mr-1"></i>
                                        Aktifkan
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                        <i data-lucide="percent" class="w-8 h-8 text-gray-400"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Tidak ada voucher</h3>
                                    <p class="text-gray-500 dark:text-gray-400 mb-4">Belum ada voucher yang dibuat.</p>
                                    <a href="{{ route('organizer.vouchers.create') }}"
                                       class="inline-flex items-center px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors duration-200">
                                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                        Buat Voucher Pertama
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($vouchers) && $vouchers->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $vouchers->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Organizer Voucher Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Bulk selection functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const voucherCheckboxes = document.querySelectorAll('.voucher-checkbox');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            voucherCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                applyFilters();
            }, 300);
        });
    }

    // Filter change handlers
    const typeFilter = document.getElementById('typeFilter');
    const statusFilter = document.getElementById('statusFilter');

    if (typeFilter) {
        typeFilter.addEventListener('change', applyFilters);
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
});

// Apply filters
function applyFilters() {
    const search = document.getElementById('searchInput').value;
    const type = document.getElementById('typeFilter').value;
    const status = document.getElementById('statusFilter').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (type) params.append('type', type);
    if (status) params.append('status', status);

    const url = new URL(window.location);
    url.search = params.toString();

    showNotification('Filtering', 'Menerapkan filter...', 'info');
    window.location.href = url.toString();
}

// Toggle voucher status
function toggleVoucherStatus(id, newStatus) {
    const action = newStatus === 'active' ? 'aktifkan' : 'nonaktifkan';

    if (confirm(`Yakin ingin ${action} voucher ini?`)) {
        showNotification('Processing', `${action.charAt(0).toUpperCase() + action.slice(1)} voucher...`, 'info');

        // Implement status toggle
        setTimeout(() => {
            showNotification('Success', `Voucher berhasil di${action}`, 'success');
            window.location.reload();
        }, 1000);
    }
}

// Bulk actions
function bulkActivate() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Error', 'Pilih voucher terlebih dahulu', 'error');
        return;
    }

    if (confirm(`Aktifkan ${selectedIds.length} voucher yang dipilih?`)) {
        showNotification('Processing', `Mengaktifkan ${selectedIds.length} voucher...`, 'info');
        setTimeout(() => {
            showNotification('Success', `${selectedIds.length} voucher berhasil diaktifkan`, 'success');
            window.location.reload();
        }, 1000);
    }
}

function bulkDeactivate() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Error', 'Pilih voucher terlebih dahulu', 'error');
        return;
    }

    if (confirm(`Nonaktifkan ${selectedIds.length} voucher yang dipilih?`)) {
        showNotification('Processing', `Menonaktifkan ${selectedIds.length} voucher...`, 'info');
        setTimeout(() => {
            showNotification('Success', `${selectedIds.length} voucher berhasil dinonaktifkan`, 'success');
            window.location.reload();
        }, 1000);
    }
}

function bulkDelete() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Error', 'Pilih voucher terlebih dahulu', 'error');
        return;
    }

    if (confirm(`Hapus ${selectedIds.length} voucher yang dipilih? Tindakan ini tidak dapat dibatalkan.`)) {
        showNotification('Deleting', `Menghapus ${selectedIds.length} voucher...`, 'info');
        setTimeout(() => {
            showNotification('Success', `${selectedIds.length} voucher berhasil dihapus`, 'success');
            window.location.reload();
        }, 1000);
    }
}

function getSelectedIds() {
    return Array.from(document.querySelectorAll('.voucher-checkbox:checked'))
                .map(checkbox => checkbox.value);
}

// Refresh data
function refreshData() {
    showNotification('Refreshing', 'Memperbarui data voucher...', 'info');
    setTimeout(() => window.location.reload(), 500);
}

// Notification system
function showNotification(title, message, type = 'info') {
    // Use the global notification system from organizer layout
    if (typeof showToast !== 'undefined') {
        showToast(type, title, message);
    } else {
        alert(`${title}: ${message}`);
    }
}
</script>
@endpush
