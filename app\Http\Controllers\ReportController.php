<?php

/**
 * Report Controller
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Report;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ReportController extends Controller
{
    /**
     * Show reports page
     */
    public function index()
    {
        return view('pages.reports.index');
    }

    /**
     * Submit a new report
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:bug,security,content,user,event,payment,other',
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:2000',
            'priority' => 'required|string|in:low,medium,high,urgent',
            'url' => 'nullable|url|max:500',
            'screenshot' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $reportData = [
                'report_number' => $this->generateReportNumber(),
                'user_id' => Auth::id(),
                'type' => $request->type,
                'title' => $request->title,
                'description' => $request->description,
                'priority' => $request->priority,
                'url' => $request->url,
                'contact_email' => $request->contact_email ?? (Auth::check() ? Auth::user()->email : null),
                'contact_phone' => $request->contact_phone,
                'status' => 'open',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];

            // Handle screenshot upload
            if ($request->hasFile('screenshot')) {
                $screenshot = $request->file('screenshot');
                $filename = 'reports/' . time() . '_' . $screenshot->getClientOriginalName();
                $path = $screenshot->storeAs('public', $filename);
                $reportData['screenshot_path'] = $filename;
            }

            $report = Report::create($reportData);

            // Log the report
            \Log::info('New report submitted', [
                'report_id' => $report->id,
                'report_number' => $report->report_number,
                'type' => $report->type,
                'priority' => $report->priority,
                'user_id' => $report->user_id,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Laporan berhasil dikirim. Nomor laporan: ' . $report->report_number,
                'report_number' => $report->report_number
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to submit report', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengirim laporan. Silakan coba lagi.'
            ], 500);
        }
    }

    /**
     * Show user's reports
     */
    public function myReports()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('message', 'Silakan login untuk melihat laporan Anda.');
        }

        $reports = Report::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('pages.reports.my-reports', compact('reports'));
    }

    /**
     * Show specific report
     */
    public function show(Report $report)
    {
        // Check if user can view this report
        if (!Auth::check() || (Auth::id() !== $report->user_id && !Auth::user()->isAdmin())) {
            abort(403, 'Anda tidak memiliki akses ke laporan ini.');
        }

        return view('pages.reports.show', compact('report'));
    }

    /**
     * Generate unique report number
     */
    private function generateReportNumber(): string
    {
        do {
            $reportNumber = 'RPT-' . date('Ymd') . '-' . strtoupper(\Str::random(6));
        } while (Report::where('report_number', $reportNumber)->exists());

        return $reportNumber;
    }
}
