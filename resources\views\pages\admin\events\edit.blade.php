@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">Edit Event</h1>
                <p class="text-gray-600">Edit informasi event: {{ $event->title }}</p>
            </div>
            <a href="{{ route('admin.tickets.index') }}" 
               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                Kembali
            </a>
        </div>
    </div>

    <!-- Edit Event Form -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="100">
        <form action="{{ route('admin.tickets.update', $event) }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf
            @method('PUT')
            
            <!-- Basic Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Event Title -->
                <div class="lg:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Judul Event</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           value="{{ old('title', $event->title) }}"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           placeholder="Masukkan judul event"
                           required>
                    @error('title')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                    <select id="category_id" 
                            name="category_id" 
                            class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                            required>
                        <option value="">Pilih Kategori</option>
                        @foreach($categories as $category)
                        <option value="{{ $category->id }}" 
                                {{ old('category_id', $event->category_id) == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                        @endforeach
                    </select>
                    @error('category_id')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Organizer -->
                <div>
                    <label for="organizer_id" class="block text-sm font-medium text-gray-700 mb-2">Organizer</label>
                    <select id="organizer_id" 
                            name="organizer_id" 
                            class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                            required>
                        <option value="">Pilih Organizer</option>
                        @foreach($organizers as $organizer)
                        <option value="{{ $organizer->id }}" 
                                {{ old('organizer_id', $event->organizer_id) == $organizer->id ? 'selected' : '' }}>
                            {{ $organizer->name }}
                        </option>
                        @endforeach
                    </select>
                    @error('organizer_id')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                <textarea id="description" 
                          name="description" 
                          rows="4" 
                          class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                          placeholder="Masukkan deskripsi event">{{ old('description', $event->description) }}</textarea>
                @error('description')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Venue Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Venue Name -->
                <div>
                    <label for="venue_name" class="block text-sm font-medium text-gray-700 mb-2">Nama Venue</label>
                    <input type="text" 
                           id="venue_name" 
                           name="venue_name" 
                           value="{{ old('venue_name', $event->venue_name) }}"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           placeholder="Contoh: Gedung Serbaguna"
                           required>
                    @error('venue_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- City -->
                <div>
                    <label for="city" class="block text-sm font-medium text-gray-700 mb-2">Kota</label>
                    <input type="text" 
                           id="city" 
                           name="city" 
                           value="{{ old('city', $event->city) }}"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           placeholder="Contoh: Jakarta"
                           required>
                    @error('city')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Venue Address -->
                <div class="lg:col-span-2">
                    <label for="venue_address" class="block text-sm font-medium text-gray-700 mb-2">Alamat Venue</label>
                    <textarea id="venue_address" 
                              name="venue_address" 
                              rows="2" 
                              class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                              placeholder="Masukkan alamat lengkap venue">{{ old('venue_address', $event->venue_address) }}</textarea>
                    @error('venue_address')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Date & Time -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Start Date -->
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Tanggal & Waktu Mulai</label>
                    <input type="datetime-local" 
                           id="start_date" 
                           name="start_date" 
                           value="{{ old('start_date', $event->start_date ? $event->start_date->format('Y-m-d\TH:i') : '') }}"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           required>
                    @error('start_date')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- End Date -->
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">Tanggal & Waktu Selesai</label>
                    <input type="datetime-local" 
                           id="end_date" 
                           name="end_date" 
                           value="{{ old('end_date', $event->end_date ? $event->end_date->format('Y-m-d\TH:i') : '') }}"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           required>
                    @error('end_date')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Pricing & Capacity -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Price -->
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Harga Tiket (Rp)</label>
                    <input type="number" 
                           id="price" 
                           name="price" 
                           value="{{ old('price', $event->price) }}"
                           min="0"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           placeholder="0"
                           required>
                    @error('price')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Total Capacity -->
                <div>
                    <label for="total_capacity" class="block text-sm font-medium text-gray-700 mb-2">Kapasitas Total</label>
                    <input type="number" 
                           id="total_capacity" 
                           name="total_capacity" 
                           value="{{ old('total_capacity', $event->total_capacity) }}"
                           min="1"
                           class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                           placeholder="100"
                           required>
                    @error('total_capacity')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Free Event -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_free" 
                           name="is_free" 
                           value="1"
                           {{ old('is_free', $event->is_free) ? 'checked' : '' }}
                           class="rounded border-gray-300 text-primary focus:ring-primary">
                    <label for="is_free" class="ml-2 text-sm text-gray-700">Event Gratis</label>
                </div>
            </div>

            <!-- Current Poster -->
            @if($event->poster)
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Poster Saat Ini</label>
                <img src="{{ $event->poster_url }}" alt="Current Poster" class="w-32 h-32 object-cover rounded-lg">
            </div>
            @endif

            <!-- Poster Upload -->
            <div>
                <label for="poster" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ $event->poster ? 'Ganti Poster Event' : 'Poster Event' }}
                </label>
                <input type="file" 
                       id="poster" 
                       name="poster" 
                       accept="image/*"
                       class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                <p class="mt-1 text-sm text-gray-500">Format: JPG, PNG. Maksimal 2MB. Kosongkan jika tidak ingin mengubah poster.</p>
                @error('poster')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.tickets.index') }}" 
                   class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" 
                        class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                    Update Event
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
