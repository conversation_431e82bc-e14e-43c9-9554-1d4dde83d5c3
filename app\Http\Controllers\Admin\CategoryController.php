<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories
     */
    public function index(Request $request)
    {
        $query = Category::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sort
        $sortBy = $request->get('sort', 'sort_order');
        $sortDirection = $request->get('direction', 'asc');
        
        if (in_array($sortBy, ['name', 'sort_order', 'created_at', 'is_active'])) {
            $query->orderBy($sortBy, $sortDirection);
        } else {
            $query->orderBy('sort_order')->orderBy('name');
        }

        $categories = $query->withCount(['tickets as events_count'])
                           ->paginate(15)
                           ->withQueryString();

        return view('pages.admin.categories.index', [
            'title' => 'Kelola Kategori',
            'categories' => $categories,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new category
     */
    public function create()
    {
        return view('pages.admin.categories.create', [
            'title' => 'Tambah Kategori Baru',
        ]);
    }

    /**
     * Store a newly created category
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:categories,name',
            'slug' => 'nullable|string|max:255|unique:categories,slug',
            'description' => 'required|string|max:1000',
            'icon' => 'required|string|max:100',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ], [
            'name.required' => 'Nama kategori wajib diisi.',
            'name.unique' => 'Nama kategori sudah digunakan.',
            'description.required' => 'Deskripsi kategori wajib diisi.',
            'icon.required' => 'Icon kategori wajib dipilih.',
            'color.required' => 'Warna kategori wajib dipilih.',
            'color.regex' => 'Format warna tidak valid. Gunakan format hex (#RRGGBB).',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $validated = $validator->validated();

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Set default sort order
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = Category::max('sort_order') + 1;
        }

        $category = Category::create($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Kategori berhasil ditambahkan!');
    }

    /**
     * Display the specified category
     */
    public function show(Category $category)
    {
        $category->load(['tickets' => function ($query) {
            $query->with('organizer')->latest()->limit(10);
        }]);

        $stats = [
            'total_events' => $category->tickets()->count(),
            'published_events' => $category->tickets()->where('status', 'published')->count(),
            'draft_events' => $category->tickets()->where('status', 'draft')->count(),
            'total_revenue' => $category->tickets()
                ->join('orders', 'events.id', '=', 'orders.event_id')
                ->where('orders.payment_status', 'paid')
                ->sum('orders.total_amount'),
            'total_tickets_sold' => $category->tickets()
                ->join('orders', 'events.id', '=', 'orders.event_id')
                ->where('orders.payment_status', 'paid')
                ->sum('orders.quantity'),
        ];

        return view('pages.admin.categories.show', [
            'title' => "Detail Kategori: {$category->name}",
            'category' => $category,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified category
     */
    public function edit(Category $category)
    {
        return view('pages.admin.categories.edit', [
            'title' => "Edit Kategori: {$category->name}",
            'category' => $category,
        ]);
    }

    /**
     * Update the specified category
     */
    public function update(Request $request, Category $category)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'slug' => 'nullable|string|max:255|unique:categories,slug,' . $category->id,
            'description' => 'required|string|max:1000',
            'icon' => 'required|string|max:100',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ], [
            'name.required' => 'Nama kategori wajib diisi.',
            'name.unique' => 'Nama kategori sudah digunakan.',
            'description.required' => 'Deskripsi kategori wajib diisi.',
            'icon.required' => 'Icon kategori wajib dipilih.',
            'color.required' => 'Warna kategori wajib dipilih.',
            'color.regex' => 'Format warna tidak valid. Gunakan format hex (#RRGGBB).',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $validated = $validator->validated();

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $category->update($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Kategori berhasil diperbarui!');
    }

    /**
     * Remove the specified category
     */
    public function destroy(Category $category)
    {
        // Check if category has events
        if ($category->tickets()->exists()) {
            return back()->with('error', 'Kategori tidak dapat dihapus karena masih memiliki event.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'Kategori berhasil dihapus!');
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(Category $category)
    {
        $category->update([
            'is_active' => !$category->is_active
        ]);

        $status = $category->is_active ? 'diaktifkan' : 'dinonaktifkan';
        
        return response()->json([
            'success' => true,
            'message' => "Kategori berhasil {$status}!",
            'is_active' => $category->is_active,
        ]);
    }

    /**
     * Bulk actions for categories
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'categories' => 'required|array|min:1',
            'categories.*' => 'exists:categories,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $action = $request->action;
        $categoryIds = $request->categories;
        $categories = Category::whereIn('id', $categoryIds);

        switch ($action) {
            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'Kategori terpilih berhasil diaktifkan!';
                break;
                
            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'Kategori terpilih berhasil dinonaktifkan!';
                break;
                
            case 'delete':
                // Check if any category has events
                $categoriesWithEvents = $categories->withCount('tickets')
                    ->get()
                    ->filter(function ($category) {
                        return $category->tickets_count > 0;
                    });

                if ($categoriesWithEvents->isNotEmpty()) {
                    $names = $categoriesWithEvents->pluck('name')->join(', ');
                    return back()->with('error', "Kategori berikut tidak dapat dihapus karena masih memiliki event: {$names}");
                }

                $categories->delete();
                $message = 'Kategori terpilih berhasil dihapus!';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Update sort order for categories
     */
    public function updateSortOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid.',
                'errors' => $validator->errors(),
            ], 422);
        }

        foreach ($request->categories as $categoryData) {
            Category::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Urutan kategori berhasil diperbarui!',
        ]);
    }

    /**
     * Get category analytics data
     */
    public function analytics(Category $category)
    {
        $analytics = [
            'events_by_month' => $this->getEventsByMonth($category),
            'revenue_by_month' => $this->getRevenueByMonth($category),
            'top_organizers' => $this->getTopOrganizers($category),
            'price_distribution' => $this->getPriceDistribution($category),
            'location_distribution' => $this->getLocationDistribution($category),
        ];

        return response()->json($analytics);
    }

    /**
     * Get events by month for analytics
     */
    private function getEventsByMonth(Category $category)
    {
        return $category->tickets()
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupByRaw('YEAR(created_at), MONTH(created_at)')
            ->orderByRaw('YEAR(created_at), MONTH(created_at)')
            ->get();
    }

    /**
     * Get revenue by month for analytics
     */
    private function getRevenueByMonth(Category $category)
    {
        return $category->tickets()
            ->join('orders', 'events.id', '=', 'orders.event_id')
            ->where('orders.payment_status', 'paid')
            ->where('orders.created_at', '>=', now()->subMonths(12))
            ->selectRaw('MONTH(orders.created_at) as month, YEAR(orders.created_at) as year, SUM(orders.total_amount) as revenue')
            ->groupByRaw('YEAR(orders.created_at), MONTH(orders.created_at)')
            ->orderByRaw('YEAR(orders.created_at), MONTH(orders.created_at)')
            ->get();
    }

    /**
     * Get top organizers for category
     */
    private function getTopOrganizers(Category $category)
    {
        return $category->tickets()
            ->join('users', 'events.organizer_id', '=', 'users.id')
            ->where('events.status', 'published')
            ->select('users.name', 'users.email', \DB::raw('COUNT(events.id) as events_count'))
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderByDesc('events_count')
            ->limit(10)
            ->get();
    }

    /**
     * Get price distribution for category
     */
    private function getPriceDistribution(Category $category)
    {
        return $category->tickets()
            ->where('status', 'published')
            ->selectRaw('
                CASE 
                    WHEN price = 0 THEN "Gratis"
                    WHEN price < 100000 THEN "< 100K"
                    WHEN price BETWEEN 100000 AND 500000 THEN "100K - 500K"
                    WHEN price > 500000 THEN "> 500K"
                END as price_range,
                COUNT(*) as count
            ')
            ->groupByRaw('
                CASE 
                    WHEN price = 0 THEN "Gratis"
                    WHEN price < 100000 THEN "< 100K"
                    WHEN price BETWEEN 100000 AND 500000 THEN "100K - 500K"
                    WHEN price > 500000 THEN "> 500K"
                END
            ')
            ->get();
    }

    /**
     * Get location distribution for category
     */
    private function getLocationDistribution(Category $category)
    {
        return $category->tickets()
            ->where('status', 'published')
            ->select('city', \DB::raw('COUNT(*) as count'))
            ->groupBy('city')
            ->orderByDesc('count')
            ->limit(10)
            ->get();
    }
}
