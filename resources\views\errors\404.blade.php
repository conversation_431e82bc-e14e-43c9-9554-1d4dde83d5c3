@extends('layouts.main')

@section('title', 'Halaman Tidak Ditemukan - 404')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-pasta-cream via-white to-pasta-mint flex items-center justify-center px-4">
    <div class="max-w-2xl mx-auto text-center">
        <!-- 404 Illustration -->
        <div class="mb-8" data-aos="fade-up">
            <div class="relative">
                <!-- Large 404 Text -->
                <h1 class="text-9xl md:text-[12rem] font-bold text-primary/20 leading-none select-none">
                    404
                </h1>

                <!-- Floating Elements -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="relative">
                        <!-- Ticket Icon -->
                        <div class="w-24 h-24 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center shadow-lg animate-bounce">
                            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                            </svg>
                        </div>

                        <!-- Floating Dots -->
                        <div class="absolute -top-4 -right-4 w-4 h-4 bg-pasta-salmon rounded-full animate-pulse"></div>
                        <div class="absolute -bottom-4 -left-4 w-3 h-3 bg-pasta-peach rounded-full animate-pulse delay-300"></div>
                        <div class="absolute top-8 -left-8 w-2 h-2 bg-pasta-butter rounded-full animate-pulse delay-500"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div class="mb-8" data-aos="fade-up" data-aos-delay="200">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Oops! Halaman Tidak Ditemukan
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                Halaman yang Anda cari tidak dapat ditemukan. Mungkin URL salah atau halaman telah dipindahkan.
            </p>

            @if(isset($requestedUrl))
                <div class="bg-pasta-cream/30 dark:bg-dark-800 rounded-xl p-4 mb-6">
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                        <span class="font-medium">URL yang diminta:</span><br>
                        <code class="text-primary bg-white dark:bg-dark-700 px-2 py-1 rounded text-xs break-all">{{ $requestedUrl }}</code>
                    </p>

                    @if(str_contains($requestedUrl, '/public/'))
                        <div class="mt-3 p-3 bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-300 dark:border-yellow-700 rounded-lg">
                            <div class="flex items-start space-x-2">
                                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                                <div>
                                    <h4 class="font-medium text-yellow-800 dark:text-yellow-200">Masalah URL Terdeteksi</h4>
                                    <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                                        URL mengandung <code>/public/</code> yang tidak seharusnya ada.
                                        @php
                                            $correctedUrl = str_replace('/public/', '/', $requestedUrl);
                                        @endphp
                                        Coba akses: <a href="{{ $correctedUrl }}" class="underline hover:no-underline font-medium">{{ $correctedUrl }}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            @endif
        </div>

        <!-- Suggested Actions -->
        <div class="mb-8" data-aos="fade-up" data-aos-delay="400">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Apa yang bisa Anda lakukan?
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="bg-white dark:bg-dark-800 rounded-xl p-4 shadow-sm border border-pasta-cream/50 dark:border-dark-600">
                    <div class="flex items-center space-x-3 mb-2">
                        <div class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h4 class="font-medium text-gray-900 dark:text-gray-100">Periksa URL</h4>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Pastikan URL yang Anda ketik sudah benar dan tidak ada typo.
                    </p>
                </div>

                <div class="bg-white dark:bg-dark-800 rounded-xl p-4 shadow-sm border border-pasta-cream/50 dark:border-dark-600">
                    <div class="flex items-center space-x-3 mb-2">
                        <div class="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                        </div>
                        <h4 class="font-medium text-gray-900 dark:text-gray-100">Refresh Halaman</h4>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Coba refresh halaman atau kembali ke halaman sebelumnya.
                    </p>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        @if(isset($suggestedRoutes))
            <div class="mb-8" data-aos="fade-up" data-aos-delay="600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Atau kunjungi halaman berikut:
                </h3>

                <div class="flex flex-wrap justify-center gap-3">
                    @foreach($suggestedRoutes as $name => $url)
                        <a href="{{ $url }}"
                           class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary to-accent text-white rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                            <span>{{ $name }}</span>
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                            </svg>
                        </a>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Back Button -->
        <div data-aos="fade-up" data-aos-delay="800">
            <button onclick="history.back()"
                    class="inline-flex items-center px-6 py-3 bg-pasta-cream/50 dark:bg-dark-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-pasta-mint/50 dark:hover:bg-dark-600 transition-all duration-300 group">
                <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                </svg>
                <span>Kembali</span>
            </button>
        </div>

        <!-- Help Text -->
        <div class="mt-12 pt-8 border-t border-pasta-cream/50 dark:border-dark-600" data-aos="fade-up" data-aos-delay="1000">
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Jika masalah terus berlanjut, silakan hubungi
                <a href="mailto:<EMAIL>" class="text-primary hover:text-accent transition-colors duration-300">
                    tim support kami
                </a>
            </p>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Auto redirect for common misrouted URLs
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;

    // Check for common URL mistakes and suggest corrections
    if (currentPath.includes('/public/')) {
        const correctedPath = currentPath.replace('/public/', '/');

        // Show suggestion to user
        const suggestion = document.createElement('div');
        suggestion.className = 'fixed top-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg shadow-lg z-50';
        suggestion.innerHTML = `
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
                <div>
                    <p class="font-medium">URL mungkin salah</p>
                    <p class="text-sm">Coba: <a href="${correctedPath}" class="underline hover:no-underline">${correctedPath}</a></p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        `;

        document.body.appendChild(suggestion);

        // Auto remove after 10 seconds
        setTimeout(() => {
            if (suggestion.parentElement) {
                suggestion.remove();
            }
        }, 10000);
    }
});
</script>
@endpush
@endsection
