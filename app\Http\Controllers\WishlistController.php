<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    /**
     * Display user's wishlist
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login')->with('error', 'Silakan login terlebih dahulu');
        }

        $query = $user->wishlist()
            ->with(['category', 'organizer'])
            ->where('status', 'published')
            ->where('start_date', '>', now());

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // City filter
        if ($request->filled('city')) {
            $query->where('city', 'like', "%{$request->city}%");
        }

        // Date filter
        if ($request->filled('date_range')) {
            switch ($request->date_range) {
                case 'this_week':
                    $query->whereBetween('start_date', [now(), now()->addWeek()]);
                    break;
                case 'this_month':
                    $query->whereBetween('start_date', [now(), now()->addMonth()]);
                    break;
                case 'next_month':
                    $query->whereBetween('start_date', [now()->addMonth(), now()->addMonths(2)]);
                    break;
            }
        }

        // Sort
        $sortBy = $request->get('sort', 'date_asc');
        switch ($sortBy) {
            case 'date_desc':
                $query->orderBy('start_date', 'desc');
                break;
            case 'price_asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            case 'name_asc':
                $query->orderBy('title', 'asc');
                break;
            case 'name_desc':
                $query->orderBy('title', 'desc');
                break;
            default:
                $query->orderBy('start_date', 'asc');
        }

        $wishlistEvents = $query->paginate(12)->withQueryString();

        // Get categories for filter
        $categories = \App\Models\Category::orderBy('name')->get();

        // Get popular cities from user's wishlist
        $popularCities = $user->wishlist()
            ->select('city')
            ->groupBy('city')
            ->orderByRaw('count(*) desc')
            ->limit(10)
            ->pluck('city');

        return view('wishlist.index', compact(
            'wishlistEvents',
            'categories',
            'popularCities'
        ));
    }

    /**
     * Remove event from wishlist
     */
    public function remove(Request $request, $eventId)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user->wishlist()->detach($eventId);

        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Event dihapus dari wishlist',
                'success' => true
            ]);
        }

        return redirect()->back()->with('success', 'Event dihapus dari wishlist');
    }

    /**
     * Clear all wishlist
     */
    public function clear()
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login')->with('error', 'Silakan login terlebih dahulu');
        }

        $user->wishlist()->detach();

        return redirect()->back()->with('success', 'Semua wishlist berhasil dihapus');
    }

    /**
     * Get wishlist count for user
     */
    public function count()
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json(['count' => 0]);
        }

        $count = $user->wishlist()
            ->where('status', 'published')
            ->where('start_date', '>', now())
            ->count();

        return response()->json(['count' => $count]);
    }
}
