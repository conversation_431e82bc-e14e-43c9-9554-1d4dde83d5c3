@extends('layouts.organizer')

@section('title', 'Manaj<PERSON><PERSON> Notifikasi')

@push('styles')
<style>
/* Custom styles for Organizer Notification Management */
.notification-card {
    transition: all 0.3s ease;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.notification-type-badge {
    transition: all 0.2s ease;
}

.notification-priority {
    position: relative;
}

.notification-priority::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    border-radius: 2px;
}

.priority-high::before {
    background: #ef4444;
}

.priority-medium::before {
    background: #f59e0b;
}

.priority-low::before {
    background: #10b981;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .notification-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="bell" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Manajemen Notifikasi</h1>
                            <p class="text-gray-600 dark:text-gray-400">Kelola notifikasi untuk event Anda</p>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="{{ route('organizer.notifications.create') }}" 
                       class="inline-flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Buat Notifikasi
                    </a>
                    
                    <a href="{{ route('organizer.dashboard') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Notification Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stats-grid">
            <!-- Total Notifications -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 notification-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Notifikasi</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_notifications'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-red-600 dark:text-red-400 mt-1">
                            <i data-lucide="bell" class="w-3 h-3 inline mr-1"></i>
                            Semua notifikasi
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="bell" class="w-6 h-6 text-red-600 dark:text-red-400"></i>
                    </div>
                </div>
            </div>

            <!-- Sent Notifications -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 notification-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Terkirim</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['sent_notifications'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                            <i data-lucide="check-circle" class="w-3 h-3 inline mr-1"></i>
                            Berhasil dikirim
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="send" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Notifications -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 notification-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Pending</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['pending_notifications'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">
                            <i data-lucide="clock" class="w-3 h-3 inline mr-1"></i>
                            Menunggu dikirim
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="clock" class="w-6 h-6 text-orange-600 dark:text-orange-400"></i>
                    </div>
                </div>
            </div>

            <!-- Read Rate -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 notification-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Tingkat Baca</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['read_rate'] ?? 0, 1) }}%
                        </p>
                        <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            <i data-lucide="eye" class="w-3 h-3 inline mr-1"></i>
                            Notifikasi dibaca
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="eye" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters & Search -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4 flex-1">
                    <!-- Search -->
                    <div class="relative flex-1 max-w-md">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="w-5 h-5 text-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="searchInput"
                               placeholder="Cari notifikasi..." 
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors duration-200">
                    </div>
                    
                    <!-- Type Filter -->
                    <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors duration-200">
                        <option value="">Semua Tipe</option>
                        <option value="info">Info</option>
                        <option value="warning">Warning</option>
                        <option value="success">Success</option>
                        <option value="error">Error</option>
                    </select>
                    
                    <!-- Status Filter -->
                    <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors duration-200">
                        <option value="">Semua Status</option>
                        <option value="sent">Terkirim</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Gagal</option>
                    </select>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                    <button onclick="refreshData()" 
                            class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Notifications Table -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Daftar Notifikasi</h2>

                    <!-- Bulk Actions -->
                    <div class="flex items-center gap-3">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="selectAll"
                                   class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                            <label for="selectAll" class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                                Pilih Semua
                            </label>
                        </div>

                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="inline-flex items-center px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                                <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                            </button>

                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                                <div class="py-1">
                                    <a href="#" onclick="bulkSend()"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <i data-lucide="send" class="w-4 h-4 mr-3"></i>
                                        Kirim Selected
                                    </a>
                                    <a href="#" onclick="bulkDelete()"
                                       class="flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20">
                                        <i data-lucide="trash-2" class="w-4 h-4 mr-3"></i>
                                        Hapus Selected
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table Content -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Notifikasi
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Event
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Tipe
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Dibuat
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Aksi
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($notifications ?? [] as $notification)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox"
                                       value="{{ $notification->id ?? '' }}"
                                       class="notification-checkbox rounded border-gray-300 text-red-600 focus:ring-red-500">
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-start">
                                    <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center shadow-sm mr-4 flex-shrink-0">
                                        <i data-lucide="bell" class="w-5 h-5 text-white"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white notification-priority {{ $notification->priority ?? 'priority-medium' }}">
                                            {{ $notification->title ?? 'No Title' }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                                            {{ Str::limit($notification->message ?? 'No message', 100) }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white">
                                    {{ $notification->event->title ?? 'All Events' }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $notification->recipient_count ?? 0 }} penerima
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $type = $notification->type ?? 'info';
                                    $typeColors = [
                                        'info' => 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
                                        'success' => 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
                                        'warning' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
                                        'error' => 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                    ];
                                    $typeIcons = [
                                        'info' => 'info',
                                        'success' => 'check-circle',
                                        'warning' => 'alert-triangle',
                                        'error' => 'x-circle'
                                    ];
                                @endphp
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $typeColors[$type] ?? $typeColors['info'] }}">
                                    <i data-lucide="{{ $typeIcons[$type] ?? $typeIcons['info'] }}" class="w-3 h-3 mr-1"></i>
                                    {{ ucfirst($type) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $status = $notification->status ?? 'pending';
                                    $statusColors = [
                                        'sent' => 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
                                        'pending' => 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
                                        'failed' => 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                    ];
                                    $statusIcons = [
                                        'sent' => 'check-circle',
                                        'pending' => 'clock',
                                        'failed' => 'x-circle'
                                    ];
                                @endphp
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$status] ?? $statusColors['pending'] }}">
                                    <i data-lucide="{{ $statusIcons[$status] ?? $statusIcons['pending'] }}" class="w-3 h-3 mr-1"></i>
                                    {{ ucfirst($status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $notification->created_at ? $notification->created_at->format('d M Y H:i') : 'Unknown' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center gap-2 notification-actions">
                                    <a href="{{ route('organizer.notifications.show', $notification) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200">
                                        <i data-lucide="eye" class="w-4 h-4 mr-1"></i>
                                        Detail
                                    </a>

                                    @if($notification->status === 'pending')
                                    <button onclick="sendNotification('{{ $notification->id }}')"
                                            class="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors duration-200">
                                        <i data-lucide="send" class="w-4 h-4 mr-1"></i>
                                        Kirim
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                        <i data-lucide="bell" class="w-8 h-8 text-gray-400"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Tidak ada notifikasi</h3>
                                    <p class="text-gray-500 dark:text-gray-400 mb-4">Belum ada notifikasi yang dibuat.</p>
                                    <a href="{{ route('organizer.notifications.create') }}"
                                       class="inline-flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200">
                                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                        Buat Notifikasi Pertama
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($notifications) && $notifications->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $notifications->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Organizer Notification Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Bulk selection functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const notificationCheckboxes = document.querySelectorAll('.notification-checkbox');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            notificationCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Implement search functionality
                console.log('Searching for:', this.value);
            }, 300);
        });
    }
});

// Send notification
function sendNotification(id) {
    if (confirm('Kirim notifikasi ini sekarang?')) {
        showNotification('Sending', 'Mengirim notifikasi...', 'info');

        // Implement send notification
        setTimeout(() => {
            showNotification('Success', 'Notifikasi berhasil dikirim', 'success');
            window.location.reload();
        }, 1000);
    }
}

// Bulk actions
function bulkSend() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Error', 'Pilih notifikasi terlebih dahulu', 'error');
        return;
    }

    if (confirm(`Kirim ${selectedIds.length} notifikasi yang dipilih?`)) {
        showNotification('Sending', `Mengirim ${selectedIds.length} notifikasi...`, 'info');
        setTimeout(() => {
            showNotification('Success', `${selectedIds.length} notifikasi berhasil dikirim`, 'success');
            window.location.reload();
        }, 1500);
    }
}

function bulkDelete() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Error', 'Pilih notifikasi terlebih dahulu', 'error');
        return;
    }

    if (confirm(`Hapus ${selectedIds.length} notifikasi yang dipilih?`)) {
        showNotification('Deleting', `Menghapus ${selectedIds.length} notifikasi...`, 'info');
        setTimeout(() => {
            showNotification('Success', `${selectedIds.length} notifikasi berhasil dihapus`, 'success');
            window.location.reload();
        }, 1000);
    }
}

function getSelectedIds() {
    return Array.from(document.querySelectorAll('.notification-checkbox:checked'))
                .map(checkbox => checkbox.value);
}

// Refresh data
function refreshData() {
    showNotification('Refreshing', 'Memperbarui data notifikasi...', 'info');
    setTimeout(() => window.location.reload(), 500);
}

// Notification system
function showNotification(title, message, type = 'info') {
    // Use the global notification system from organizer layout
    if (typeof showToast !== 'undefined') {
        showToast(type, title, message);
    } else {
        alert(`${title}: ${message}`);
    }
}
</script>
@endpush
