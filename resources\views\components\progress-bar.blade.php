@props([
    'value' => 0,
    'max' => 100,
    'color' => 'blue',
    'size' => 'md', // xs, sm, md, lg
    'label' => null,
    'showPercentage' => false,
    'animated' => false,
    'striped' => false
])

@php
    $percentage = $max > 0 ? min(100, ($value / $max) * 100) : 0;
    
    $colors = [
        'blue' => 'bg-blue-600',
        'green' => 'bg-green-600',
        'yellow' => 'bg-yellow-600',
        'red' => 'bg-red-600',
        'purple' => 'bg-purple-600',
        'orange' => 'bg-orange-600',
        'pink' => 'bg-pink-600',
        'indigo' => 'bg-indigo-600',
        'gray' => 'bg-gray-600',
    ];
    
    $sizes = [
        'xs' => 'h-1',
        'sm' => 'h-2',
        'md' => 'h-3',
        'lg' => 'h-4'
    ];
    
    $colorClass = $colors[$color] ?? $colors['blue'];
    $sizeClass = $sizes[$size] ?? $sizes['md'];
    
    $progressClasses = collect([
        $colorClass,
        $sizeClass,
        'transition-all duration-500 ease-out',
        $striped ? 'bg-stripes' : '',
        $animated ? 'animate-pulse' : ''
    ])->filter()->implode(' ');
@endphp

<div {{ $attributes->merge(['class' => 'w-full']) }}>
    @if($label || $showPercentage)
        <div class="flex justify-between items-center mb-2">
            @if($label)
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $label }}</span>
            @endif
            @if($showPercentage)
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ number_format($percentage, 1) }}%</span>
            @endif
        </div>
    @endif
    
    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full {{ $sizeClass }} overflow-hidden">
        <div class="{{ $progressClasses }} rounded-full" 
             style="width: {{ $percentage }}%"
             role="progressbar" 
             aria-valuenow="{{ $value }}" 
             aria-valuemin="0" 
             aria-valuemax="{{ $max }}">
        </div>
    </div>
</div>

@once
    @push('styles')
    <style>
        .bg-stripes {
            background-image: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.15) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.15) 50%,
                rgba(255, 255, 255, 0.15) 75%,
                transparent 75%,
                transparent
            );
            background-size: 1rem 1rem;
        }
        
        .animate-stripes {
            animation: stripes 1s linear infinite;
        }
        
        @keyframes stripes {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 1rem 0;
            }
        }
    </style>
    @endpush
@endonce
