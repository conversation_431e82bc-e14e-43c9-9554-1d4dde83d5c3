<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Voucher;
use App\Models\User;
use App\Models\Event;
use App\Models\Category;
use Carbon\Carbon;

class VoucherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('role', 'admin')->first();
        $events = Event::take(3)->pluck('id')->toArray();
        $categories = Category::take(2)->pluck('id')->toArray();

        $vouchers = [
            [
                'code' => 'WELCOME10',
                'name' => 'Welcome Discount 10%',
                'description' => 'Diskon 10% untuk pengguna baru',
                'type' => 'percentage',
                'value' => 10,
                'min_order_amount' => 50000,
                'max_discount_amount' => 100000,
                'usage_limit' => 100,
                'usage_limit_per_user' => 1,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(3),
                'applicable_events' => null,
                'applicable_categories' => null,
                'applicable_user_roles' => ['pembeli'],
                'min_tickets' => 1,
                'max_tickets' => 5,
                'is_active' => true,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
            [
                'code' => 'NEWYEAR2024',
                'name' => 'New Year Special',
                'description' => 'Diskon spesial tahun baru 2024',
                'type' => 'percentage',
                'value' => 25,
                'min_order_amount' => 100000,
                'max_discount_amount' => 250000,
                'usage_limit' => 50,
                'usage_limit_per_user' => 1,
                'starts_at' => now(),
                'expires_at' => now()->addMonth(),
                'applicable_events' => null,
                'applicable_categories' => null,
                'applicable_user_roles' => null,
                'min_tickets' => 1,
                'max_tickets' => null,
                'is_active' => true,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
            [
                'code' => 'EARLYBIRD',
                'name' => 'Early Bird Discount',
                'description' => 'Diskon untuk pembelian tiket lebih awal',
                'type' => 'fixed',
                'value' => 50000,
                'min_order_amount' => 200000,
                'max_discount_amount' => null,
                'usage_limit' => 200,
                'usage_limit_per_user' => 2,
                'starts_at' => now(),
                'expires_at' => now()->addWeeks(2),
                'applicable_events' => $events,
                'applicable_categories' => null,
                'applicable_user_roles' => null,
                'min_tickets' => 2,
                'max_tickets' => 10,
                'is_active' => true,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
            [
                'code' => 'STUDENT15',
                'name' => 'Student Discount',
                'description' => 'Diskon khusus untuk pelajar dan mahasiswa',
                'type' => 'percentage',
                'value' => 15,
                'min_order_amount' => 30000,
                'max_discount_amount' => 75000,
                'usage_limit' => null, // unlimited
                'usage_limit_per_user' => 3,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(6),
                'applicable_events' => null,
                'applicable_categories' => $categories,
                'applicable_user_roles' => ['pembeli'],
                'min_tickets' => 1,
                'max_tickets' => 3,
                'is_active' => true,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
            [
                'code' => 'BULK20',
                'name' => 'Bulk Purchase Discount',
                'description' => 'Diskon untuk pembelian tiket dalam jumlah banyak',
                'type' => 'percentage',
                'value' => 20,
                'min_order_amount' => 500000,
                'max_discount_amount' => 500000,
                'usage_limit' => 30,
                'usage_limit_per_user' => 1,
                'starts_at' => now(),
                'expires_at' => now()->addMonths(2),
                'applicable_events' => null,
                'applicable_categories' => null,
                'applicable_user_roles' => null,
                'min_tickets' => 5,
                'max_tickets' => null,
                'is_active' => true,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
            [
                'code' => 'WEEKEND50K',
                'name' => 'Weekend Special',
                'description' => 'Diskon khusus untuk event weekend',
                'type' => 'fixed',
                'value' => 50000,
                'min_order_amount' => 150000,
                'max_discount_amount' => null,
                'usage_limit' => 75,
                'usage_limit_per_user' => 2,
                'starts_at' => now(),
                'expires_at' => now()->addDays(30),
                'applicable_events' => null,
                'applicable_categories' => null,
                'applicable_user_roles' => null,
                'min_tickets' => 1,
                'max_tickets' => 4,
                'is_active' => true,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
            [
                'code' => 'EXPIRED10',
                'name' => 'Expired Voucher (Test)',
                'description' => 'Voucher yang sudah kedaluwarsa untuk testing',
                'type' => 'percentage',
                'value' => 10,
                'min_order_amount' => 50000,
                'max_discount_amount' => 50000,
                'usage_limit' => 10,
                'usage_limit_per_user' => 1,
                'starts_at' => now()->subDays(10),
                'expires_at' => now()->subDays(1),
                'applicable_events' => null,
                'applicable_categories' => null,
                'applicable_user_roles' => null,
                'min_tickets' => 1,
                'max_tickets' => null,
                'is_active' => true,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
            [
                'code' => 'INACTIVE20',
                'name' => 'Inactive Voucher (Test)',
                'description' => 'Voucher tidak aktif untuk testing',
                'type' => 'percentage',
                'value' => 20,
                'min_order_amount' => 100000,
                'max_discount_amount' => 100000,
                'usage_limit' => 20,
                'usage_limit_per_user' => 1,
                'starts_at' => now(),
                'expires_at' => now()->addMonth(),
                'applicable_events' => null,
                'applicable_categories' => null,
                'applicable_user_roles' => null,
                'min_tickets' => 1,
                'max_tickets' => null,
                'is_active' => false,
                'is_public' => true,
                'created_by_type' => 'admin',
                'created_by_id' => $admin->id,
            ],
        ];

        foreach ($vouchers as $voucherData) {
            Voucher::create($voucherData);
        }

        $this->command->info('✅ Voucher seeder completed successfully!');
        $this->command->info('📊 Created ' . count($vouchers) . ' sample vouchers');
        $this->command->info('🎫 Available voucher codes:');
        foreach ($vouchers as $voucher) {
            $status = $voucher['is_active'] ? '✅' : '❌';
            $this->command->info("   {$status} {$voucher['code']} - {$voucher['name']}");
        }
    }
}
