@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Back Button -->
    <a href="{{ route('home') }}" class="inline-flex items-center text-gray-600 hover:text-primary mb-4">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Kembali
    </a>

    <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
        <!-- PWA Install Prompt -->
        @if($showInstallPrompt)
        <div class="bg-primary-100 p-4 flex items-center justify-between" id="pwa-install-prompt">
            <div class="flex items-center space-x-3">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                </svg>
                <span class="text-sm">Install TiXara untuk akses tiket offline</span>
            </div>
            <button onclick="installPWA()" class="px-4 py-2 bg-primary-500 text-white rounded-lg text-sm hover:bg-primary-600 transition-colors">
                Install
            </button>
        </div>
        @endif
        <!-- Event Header Image -->
        <div class="relative h-64 sm:h-96" data-aos="fade-up">
            <img src="{{ $event->poster_url }}" alt="{{ $event->title }}" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
            <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                <div class="flex items-center space-x-2 mb-2">
                    <span class="px-3 py-1 bg-primary-500/90 rounded-full text-sm">
                        {{ $event->category->name }}
                    </span>
                </div>
                <h1 class="text-2xl sm:text-3xl font-bold mb-2">{{ $event->title }}</h1>
                <div class="flex items-center space-x-4 text-sm">
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <span>{{ $event->start_date->format('d M Y - H:i') }}</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <span>{{ $event->location }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Details -->
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Content -->
                <div class="lg:col-span-2 space-y-6" data-aos="fade-up" data-aos-delay="100">
                    <div>
                        <h2 class="text-xl font-semibold mb-4">Tentang Event</h2>
                        <div class="prose max-w-none">
                            {!! $event->description !!}
                        </div>
                    </div>

                    <div>
                        <h2 class="text-xl font-semibold mb-4">Syarat & Ketentuan</h2>
                        <ul class="space-y-2 text-gray-600">
                            @foreach($event->terms as $term)
                            <li class="flex items-start space-x-2">
                                <svg class="w-5 h-5 text-primary-600 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span>{{ $term }}</span>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>

                <!-- Right Content - Ticket Purchase -->
                <div data-aos="fade-up" data-aos-delay="200">
                    <div class="bg-white rounded-xl p-6 sticky top-6 shadow-lg border border-gray-100">
                        <livewire:ticket-purchase :event="$event" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection