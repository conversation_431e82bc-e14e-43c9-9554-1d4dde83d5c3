@extends('layouts.admin')

@section('title', 'Order Details - Admin TiXara')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Order Details</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Order #{{ $order->order_number }}</p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="openStatusModal({{ $order->id }}, '{{ $order->status }}')" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                        Update Status
                    </button>
                    <a href="{{ route('admin.orders.index') }}" 
                       class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                        Back to Orders
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Order Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Order Information</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Order Number</label>
                            <p class="text-sm text-gray-900 dark:text-white font-mono">{{ $order->order_number }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Order Date</label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ $order->created_at->format('M d, Y H:i:s') }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                            @if($order->status == 'completed')
                                <span class="px-3 py-1 text-sm font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 rounded-full">Completed</span>
                            @elseif($order->status == 'pending')
                                <span class="px-3 py-1 text-sm font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400 rounded-full">Pending</span>
                            @elseif($order->status == 'processing')
                                <span class="px-3 py-1 text-sm font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 rounded-full">Processing</span>
                            @else
                                <span class="px-3 py-1 text-sm font-medium bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400 rounded-full">{{ ucfirst($order->status) }}</span>
                            @endif
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Payment Status</label>
                            @if($order->payment_status == 'paid')
                                <span class="px-3 py-1 text-sm font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 rounded-full">Paid</span>
                            @elseif($order->payment_status == 'pending')
                                <span class="px-3 py-1 text-sm font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400 rounded-full">Pending</span>
                            @elseif($order->payment_status == 'failed')
                                <span class="px-3 py-1 text-sm font-medium bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400 rounded-full">Failed</span>
                            @else
                                <span class="px-3 py-1 text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full">{{ ucfirst($order->payment_status) }}</span>
                            @endif
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantity</label>
                            <p class="text-sm text-gray-900 dark:text-white">{{ $order->quantity }} tickets</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Total Amount</label>
                            <p class="text-lg font-semibold text-gray-900 dark:text-white">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</p>
                        </div>
                    </div>

                    @if($order->admin_notes)
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Admin Notes</label>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                <p class="text-sm text-gray-900 dark:text-white">{{ $order->admin_notes }}</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Customer Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Customer Information</h2>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                            <span class="text-white text-lg font-semibold">
                                {{ substr($order->user->name, 0, 1) }}
                            </span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $order->user->name }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $order->user->email }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ ucfirst($order->user->role) }}</p>
                        </div>
                    </div>
                </div>

                <!-- Event Information -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Event Information</h2>
                    
                    <div class="flex items-start space-x-4">
                        <img class="w-20 h-20 rounded-lg object-cover" 
                             src="{{ $order->event->poster_url }}" 
                             alt="{{ $order->event->title }}">
                        <div class="flex-1">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $order->event->title }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ $order->event->venue_name }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $order->event->venue_address }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                <i data-lucide="calendar" class="w-4 h-4 inline mr-1"></i>
                                {{ $order->event->start_date->format('M d, Y H:i') }}
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <i data-lucide="user" class="w-4 h-4 inline mr-1"></i>
                                Organized by {{ $order->event->organizer->name }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Tickets -->
                @if($order->tickets->count() > 0)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Tickets</h2>
                        
                        <div class="space-y-4">
                            @foreach($order->tickets as $ticket)
                                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">Ticket #{{ $ticket->ticket_number }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $ticket->buyer->name ?? 'N/A' }}</p>
                                        </div>
                                        <div class="text-right">
                                            @if($ticket->status == 'active')
                                                <span class="px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 rounded-full">Active</span>
                                            @elseif($ticket->status == 'used')
                                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 rounded-full">Used</span>
                                            @else
                                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full">{{ ucfirst($ticket->status) }}</span>
                                            @endif
                                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Rp {{ number_format($ticket->price, 0, ',', '.') }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <button onclick="openStatusModal({{ $order->id }}, '{{ $order->status }}')" 
                                class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                            Update Status
                        </button>
                        
                        <button onclick="openPaymentModal({{ $order->id }}, '{{ $order->payment_status }}')" 
                                class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            <i data-lucide="credit-card" class="w-4 h-4 inline mr-2"></i>
                            Update Payment
                        </button>
                        
                        <a href="mailto:{{ $order->user->email }}" 
                           class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors inline-block text-center">
                            <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                            Email Customer
                        </a>
                    </div>
                </div>

                <!-- Order Timeline -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Timeline</h3>
                    
                    <div class="space-y-4">
                        @foreach($activities as $activity)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center
                                    @if($activity['color'] == 'green') bg-green-100 dark:bg-green-900/20
                                    @elseif($activity['color'] == 'blue') bg-blue-100 dark:bg-blue-900/20
                                    @elseif($activity['color'] == 'red') bg-red-100 dark:bg-red-900/20
                                    @else bg-gray-100 dark:bg-gray-700 @endif">
                                    <i data-lucide="{{ $activity['icon'] }}" class="w-4 h-4 
                                        @if($activity['color'] == 'green') text-green-600 dark:text-green-400
                                        @elseif($activity['color'] == 'blue') text-blue-600 dark:text-blue-400
                                        @elseif($activity['color'] == 'red') text-red-600 dark:text-red-400
                                        @else text-gray-600 dark:text-gray-400 @endif"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $activity['title'] }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $activity['description'] }}</p>
                                    <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">{{ $activity['timestamp']->format('M d, Y H:i') }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Update Order Status</h3>
                <form id="statusForm" method="POST">
                    @csrf
                    @method('PATCH')
                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                        <select id="modalStatus" name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes (Optional)</label>
                        <textarea id="notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeStatusModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark">
                            Update Status
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Payment Status Update Modal -->
<div id="paymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Update Payment Status</h3>
                <form id="paymentForm" method="POST">
                    @csrf
                    @method('PATCH')
                    <div class="mb-4">
                        <label for="payment_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Payment Status</label>
                        <select id="modalPaymentStatus" name="payment_status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                            <option value="pending">Pending</option>
                            <option value="paid">Paid</option>
                            <option value="failed">Failed</option>
                            <option value="refunded">Refunded</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="payment_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Payment Notes (Optional)</label>
                        <textarea id="payment_notes" name="payment_notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closePaymentModal()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark">
                            Update Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Status Modal Functions
function openStatusModal(orderId, currentStatus) {
    document.getElementById('statusForm').action = `/admin/orders/${orderId}/status`;
    document.getElementById('modalStatus').value = currentStatus;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

// Payment Modal Functions
function openPaymentModal(orderId, currentPaymentStatus) {
    document.getElementById('paymentForm').action = `/admin/orders/${orderId}/payment-status`;
    document.getElementById('modalPaymentStatus').value = currentPaymentStatus;
    document.getElementById('paymentModal').classList.remove('hidden');
}

function closePaymentModal() {
    document.getElementById('paymentModal').classList.add('hidden');
}
</script>
@endpush
