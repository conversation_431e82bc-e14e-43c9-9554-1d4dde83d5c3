@extends('layouts.organizer')

@section('title', 'My Tickets - Organizer Dashboard')

@section('content')
<div class="min-h-screen bg-dynamic-secondary py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-dynamic-primary">My Tickets</h1>
                    <p class="mt-2 text-dynamic-secondary">Manage your event tickets and track sales</p>
                </div>
                <a href="{{ route('organizer.tickets.create') }}" 
                   class="btn-theme-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create New Ticket
                </a>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8" data-aos="fade-up">
            <!-- Total Tickets -->
            <div class="card-theme p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-dynamic-secondary">Total Tickets</p>
                        <p class="text-2xl font-bold text-dynamic-primary">{{ $stats['total_tickets'] ?? 0 }}</p>
                    </div>
                    <div class="p-3 theme-bg-secondary rounded-lg">
                        <i data-lucide="ticket" class="w-6 h-6 theme-primary"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-green-600">+12% from last month</span>
                </div>
            </div>

            <!-- Active Tickets -->
            <div class="card-theme p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-dynamic-secondary">Active Tickets</p>
                        <p class="text-2xl font-bold text-dynamic-primary">{{ $stats['active_tickets'] ?? 0 }}</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-lg">
                        <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-green-600">{{ $stats['active_percentage'] ?? 0 }}% of total</span>
                </div>
            </div>

            <!-- Sold Tickets -->
            <div class="card-theme p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-dynamic-secondary">Tickets Sold</p>
                        <p class="text-2xl font-bold text-dynamic-primary">{{ $stats['sold_tickets'] ?? 0 }}</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <i data-lucide="shopping-cart" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-blue-600">{{ $stats['sold_percentage'] ?? 0 }}% sold rate</span>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="card-theme p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-dynamic-secondary">Total Revenue</p>
                        <p class="text-2xl font-bold text-dynamic-primary">Rp {{ number_format($stats['total_revenue'] ?? 0, 0, ',', '.') }}</p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <i data-lucide="dollar-sign" class="w-6 h-6 text-yellow-600"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-yellow-600">+8% from last month</span>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card-theme p-6 mb-8" data-aos="fade-up" data-aos-delay="100">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-dynamic-primary mb-2">Search</label>
                    <div class="relative">
                        <input type="text" id="search" placeholder="Search tickets..."
                               class="input-theme w-full pl-10">
                        <i data-lucide="search" class="absolute left-3 top-3 h-4 w-4 text-dynamic-secondary"></i>
                    </div>
                </div>
                
                <div>
                    <label for="event_filter" class="block text-sm font-medium text-dynamic-primary mb-2">Event</label>
                    <select id="event_filter" class="input-theme w-full">
                        <option value="">All Events</option>
                        @if(isset($events))
                            @foreach($events as $event)
                                <option value="{{ $event->id }}">{{ $event->title }}</option>
                            @endforeach
                        @endif
                    </select>
                </div>
                
                <div>
                    <label for="status_filter" class="block text-sm font-medium text-dynamic-primary mb-2">Status</label>
                    <select id="status_filter" class="input-theme w-full">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="sold_out">Sold Out</option>
                    </select>
                </div>
                
                <div>
                    <label for="type_filter" class="block text-sm font-medium text-dynamic-primary mb-2">Type</label>
                    <select id="type_filter" class="input-theme w-full">
                        <option value="">All Types</option>
                        <option value="regular">Regular</option>
                        <option value="vip">VIP</option>
                        <option value="vvip">VVIP</option>
                        <option value="early_bird">Early Bird</option>
                        <option value="student">Student</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Tickets Table -->
        <div class="card-theme overflow-hidden" data-aos="fade-up" data-aos-delay="200">
            <div class="px-6 py-4 border-b border-dynamic">
                <h3 class="text-lg font-semibold text-dynamic-primary">Tickets List</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-dynamic">
                    <thead class="bg-dynamic-secondary">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-dynamic-secondary uppercase tracking-wider">
                                Ticket
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-dynamic-secondary uppercase tracking-wider">
                                Event
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-dynamic-secondary uppercase tracking-wider">
                                Price
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-dynamic-secondary uppercase tracking-wider">
                                Sold/Total
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-dynamic-secondary uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-dynamic-secondary uppercase tracking-wider">
                                Sale Period
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-dynamic-secondary uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-dynamic-primary divide-y divide-dynamic">
                        @forelse($tickets ?? [] as $ticket)
                        <tr class="hover:bg-dynamic-secondary transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-lg theme-bg-secondary flex items-center justify-center">
                                            <i data-lucide="ticket" class="h-5 w-5 theme-primary"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-dynamic-primary">{{ $ticket->name ?? 'Ticket Name' }}</div>
                                        <div class="text-sm text-dynamic-secondary">{{ ucfirst($ticket->ticket_type ?? 'regular') }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-dynamic-primary">{{ $ticket->event->title ?? 'Event Title' }}</div>
                                <div class="text-sm text-dynamic-secondary">{{ $ticket->event->date ? $ticket->event->date->format('M d, Y') : 'No date' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-dynamic-primary">Rp {{ number_format($ticket->price ?? 0, 0, ',', '.') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-dynamic-primary">
                                    {{ $ticket->sold_count ?? 0 }}/{{ $ticket->quantity ?? 0 }}
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="theme-bg-primary h-2 rounded-full" 
                                         style="width: {{ $ticket->quantity > 0 ? (($ticket->sold_count ?? 0) / $ticket->quantity) * 100 : 0 }}%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $statusColors = [
                                        'active' => 'bg-green-100 text-green-800',
                                        'inactive' => 'bg-gray-100 text-gray-800',
                                        'sold_out' => 'bg-red-100 text-red-800'
                                    ];
                                    $status = $ticket->status ?? 'active';
                                @endphp
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $statusColors[$status] ?? 'bg-gray-100 text-gray-800' }}">
                                    {{ ucfirst($status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-dynamic-secondary">
                                <div>{{ $ticket->sale_start ? $ticket->sale_start->format('M d') : 'Not set' }}</div>
                                <div>{{ $ticket->sale_end ? $ticket->sale_end->format('M d') : 'Not set' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('organizer.tickets.show', $ticket->id ?? 1) }}" 
                                       class="text-blue-600 hover:text-blue-900 transition-colors">
                                        <i data-lucide="eye" class="h-4 w-4"></i>
                                    </a>
                                    <a href="{{ route('organizer.tickets.edit', $ticket->id ?? 1) }}" 
                                       class="text-yellow-600 hover:text-yellow-900 transition-colors">
                                        <i data-lucide="edit" class="h-4 w-4"></i>
                                    </a>
                                    <button onclick="deleteTicket({{ $ticket->id ?? 1 }})" 
                                            class="text-red-600 hover:text-red-900 transition-colors">
                                        <i data-lucide="trash-2" class="h-4 w-4"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <i data-lucide="ticket" class="w-12 h-12 text-gray-400 mb-4"></i>
                                    <h3 class="text-lg font-medium text-dynamic-primary mb-2">No tickets found</h3>
                                    <p class="text-dynamic-secondary mb-4">Get started by creating your first ticket.</p>
                                    <a href="{{ route('organizer.tickets.create') }}" class="btn-theme-primary">
                                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                        Create Ticket
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($tickets) && method_exists($tickets, 'links'))
                <div class="px-6 py-4 border-t border-dynamic">
                    {{ $tickets->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('search');
    const eventFilter = document.getElementById('event_filter');
    const statusFilter = document.getElementById('status_filter');
    const typeFilter = document.getElementById('type_filter');
    
    // Add event listeners for filters
    [searchInput, eventFilter, statusFilter, typeFilter].forEach(element => {
        element.addEventListener('input', filterTickets);
    });
    
    function filterTickets() {
        // This would typically make an AJAX request to filter tickets
        // For now, we'll implement client-side filtering
        const searchTerm = searchInput.value.toLowerCase();
        const eventId = eventFilter.value;
        const status = statusFilter.value;
        const type = typeFilter.value;
        
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            if (row.querySelector('td[colspan]')) return; // Skip empty state row
            
            const ticketName = row.querySelector('td:first-child .text-sm.font-medium').textContent.toLowerCase();
            const ticketType = row.querySelector('td:first-child .text-sm.text-dynamic-secondary').textContent.toLowerCase();
            const eventName = row.querySelector('td:nth-child(2) .text-sm.text-dynamic-primary').textContent.toLowerCase();
            const ticketStatus = row.querySelector('td:nth-child(5) span').textContent.toLowerCase();
            
            let show = true;
            
            if (searchTerm && !ticketName.includes(searchTerm) && !eventName.includes(searchTerm)) {
                show = false;
            }
            
            if (status && !ticketStatus.includes(status)) {
                show = false;
            }
            
            if (type && !ticketType.includes(type)) {
                show = false;
            }
            
            row.style.display = show ? '' : 'none';
        });
    }
});

function deleteTicket(ticketId) {
    if (confirm('Are you sure you want to delete this ticket? This action cannot be undone.')) {
        // Make AJAX request to delete ticket
        fetch(`/organizer/tickets/${ticketId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting ticket: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting ticket');
        });
    }
}
</script>
@endpush
@endsection
