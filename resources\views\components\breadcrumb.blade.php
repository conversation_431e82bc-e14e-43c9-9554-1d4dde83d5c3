@props([
    'items' => [],
    'separator' => 'chevron-right',
    'home' => true,
    'homeUrl' => '/',
    'homeIcon' => 'home'
])

@php
    $breadcrumbItems = collect($items);
@endphp

<nav {{ $attributes->merge(['class' => 'flex items-center space-x-2 text-sm']) }} aria-label="Breadcrumb">
    <ol class="flex items-center space-x-2">
        @if($home)
            <li>
                <a href="{{ $homeUrl }}" 
                   class="flex items-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-200">
                    <i data-lucide="{{ $homeIcon }}" class="w-4 h-4"></i>
                    <span class="sr-only">Home</span>
                </a>
            </li>
            
            @if($breadcrumbItems->isNotEmpty())
                <li>
                    <i data-lucide="{{ $separator }}" class="w-4 h-4 text-gray-400"></i>
                </li>
            @endif
        @endif
        
        @foreach($breadcrumbItems as $index => $item)
            <li class="flex items-center">
                @if($index > 0)
                    <i data-lucide="{{ $separator }}" class="w-4 h-4 text-gray-400 mr-2"></i>
                @endif
                
                @if(isset($item['url']) && !$loop->last)
                    <a href="{{ $item['url'] }}" 
                       class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-200">
                        @if(isset($item['icon']))
                            <i data-lucide="{{ $item['icon'] }}" class="w-4 h-4 mr-1 inline"></i>
                        @endif
                        {{ $item['label'] }}
                    </a>
                @else
                    <span class="text-gray-900 dark:text-white font-medium">
                        @if(isset($item['icon']))
                            <i data-lucide="{{ $item['icon'] }}" class="w-4 h-4 mr-1 inline"></i>
                        @endif
                        {{ $item['label'] }}
                    </span>
                @endif
            </li>
        @endforeach
    </ol>
</nav>
