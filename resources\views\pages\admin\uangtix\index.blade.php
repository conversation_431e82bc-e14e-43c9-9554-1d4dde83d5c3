@extends('layouts.admin')

@section('title', 'Manajemen UangTix')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-coins text-warning me-2"></i>
                Manajemen UangTix
            </h1>
            <p class="text-muted">Kelola sistem mata uang lokal UangTix</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.uangtix.settings') }}" class="btn btn-primary">
                <i class="fas fa-cog"></i> Pengaturan
            </a>
            <a href="{{ route('admin.uangtix.requests') }}" class="btn btn-warning">
                <i class="fas fa-clock"></i> <PERSON><PERSON><PERSON><PERSON>
                @if($stats['pending_deposits'] + $stats['pending_withdrawals'] > 0)
                    <span class="badge bg-danger ms-1">{{ $stats['pending_deposits'] + $stats['pending_withdrawals'] }}</span>
                @endif
            </a>
            <div class="dropdown">
                <button class="btn btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> Export
                </button>
                <ul class="dropdown-menu">
                    <li><h6 class="dropdown-header">Format Export</h6></li>
                    <li><a class="dropdown-item" href="#" onclick="exportData('balances', 'json')">
                        <i class="fas fa-file-code me-2"></i>JSON
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportData('balances', 'csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="exportData('transactions', 'json')">
                        <i class="fas fa-exchange-alt me-2"></i>Transaksi (JSON)
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportData('transactions', 'csv')">
                        <i class="fas fa-exchange-alt me-2"></i>Transaksi (CSV)
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Exchange Rate Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-left-warning shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-warning font-weight-bold">Kurs Saat Ini</h6>
                            <div class="small">
                                <div>{{ $exchangeRate->formatted_rates['idr_to_uangtix'] }}</div>
                                <div>{{ $exchangeRate->formatted_rates['uangtix_to_idr'] }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-info font-weight-bold">Batas Transaksi</h6>
                            <div class="small">
                                <div>Min Deposit: {{ $exchangeRate->formatted_limits['min_deposit'] }}</div>
                                <div>Max Deposit: {{ $exchangeRate->formatted_limits['max_deposit'] }}</div>
                                <div>Min Penarikan: {{ $exchangeRate->formatted_limits['min_withdrawal'] }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-success font-weight-bold">Biaya Transaksi</h6>
                            <div class="small">
                                <div>Biaya Deposit: {{ $exchangeRate->formatted_fees['deposit_fee'] }}</div>
                                <div>Biaya Penarikan: {{ $exchangeRate->formatted_fees['withdrawal_fee'] }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-primary font-weight-bold">Status Layanan</h6>
                            <div class="small">
                                <div>
                                    <span class="badge badge-{{ $exchangeRate->deposits_enabled ? 'success' : 'danger' }}">
                                        Deposit {{ $exchangeRate->deposits_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-{{ $exchangeRate->withdrawals_enabled ? 'success' : 'danger' }}">
                                        Penarikan {{ $exchangeRate->withdrawals_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-{{ $exchangeRate->transfers_enabled ? 'success' : 'danger' }}">
                                        Transfer {{ $exchangeRate->transfers_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total UangTix
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_balances'], 0, ',', '.') }} UTX
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Pengguna Aktif
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_users'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Transaksi Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_transactions_today'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Volume Hari Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_volume_today'], 0, ',', '.') }} UTX
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter & Pencarian</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.uangtix.index') }}" class="row">
                <div class="col-md-4 mb-3">
                    <label for="search" class="form-label">Pencarian</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Nama atau email pengguna...">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="min_balance" class="form-label">Saldo Minimum (UTX)</label>
                    <input type="number" class="form-control" id="min_balance" name="min_balance" 
                           value="{{ request('min_balance') }}" placeholder="0">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="max_balance" class="form-label">Saldo Maksimum (UTX)</label>
                    <input type="number" class="form-control" id="max_balance" name="max_balance" 
                           value="{{ request('max_balance') }}" placeholder="1000000">
                </div>
                <div class="col-md-2 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ route('admin.uangtix.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- UangTix Balances Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Saldo UangTix Pengguna</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Pengguna</th>
                            <th>Saldo UangTix</th>
                            <th>Setara IDR</th>
                            <th>Total Earned</th>
                            <th>Total Spent</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($balances as $balance)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $balance->user->avatar_url }}" alt="{{ $balance->user->name }}" 
                                             class="rounded-circle me-2" width="40" height="40">
                                        <div>
                                            <div class="font-weight-bold">{{ $balance->user->name }}</div>
                                            <div class="text-muted small">{{ $balance->user->email }}</div>
                                            <span class="badge badge-{{ $balance->user->role == 'admin' ? 'danger' : 'primary' }}">
                                                {{ ucfirst($balance->user->role) }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="font-weight-bold text-warning">
                                        {{ $balance->formatted_balance }}
                                    </span>
                                </td>
                                <td>
                                    <span class="text-success">
                                        {{ $balance->formatted_balance_idr }}
                                    </span>
                                </td>
                                <td>{{ number_format($balance->total_earned, 0, ',', '.') }} UTX</td>
                                <td>{{ number_format($balance->total_spent, 0, ',', '.') }} UTX</td>
                                <td>
                                    <span class="badge badge-{{ $balance->is_active ? 'success' : 'danger' }}">
                                        {{ $balance->is_active ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                                data-bs-toggle="dropdown">
                                            Aksi
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="#" 
                                                   onclick="showAdjustBalanceModal({{ $balance->user->id }}, '{{ $balance->user->name }}', {{ $balance->balance }})">
                                                    <i class="fas fa-edit me-2"></i>Sesuaikan Saldo
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ route('admin.uangtix.transactions') }}?search={{ $balance->user->email }}">
                                                    <i class="fas fa-history me-2"></i>Lihat Transaksi
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="#" 
                                                   onclick="toggleBalanceStatus({{ $balance->user->id }}, {{ $balance->is_active ? 'false' : 'true' }})">
                                                    <i class="fas fa-{{ $balance->is_active ? 'ban' : 'check' }} me-2"></i>
                                                    {{ $balance->is_active ? 'Nonaktifkan' : 'Aktifkan' }}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-coins fa-3x mb-3"></i>
                                        <p>Tidak ada data saldo UangTix yang ditemukan</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($balances->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $balances->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Adjust Balance Modal -->
<div class="modal fade" id="adjustBalanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sesuaikan Saldo UangTix</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="adjustBalanceForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Pengguna</label>
                        <input type="text" class="form-control" id="userName" readonly>
                        <input type="hidden" id="userId">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Saldo Saat Ini</label>
                        <input type="text" class="form-control" id="currentBalance" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="adjustType" class="form-label">Jenis Penyesuaian</label>
                        <select class="form-control" id="adjustType" name="type" required>
                            <option value="add">Tambah UangTix</option>
                            <option value="deduct">Kurangi UangTix</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="adjustAmount" class="form-label">Jumlah (UTX)</label>
                        <input type="number" class="form-control" id="adjustAmount" name="amount" 
                               min="0.01" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="adjustDescription" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="adjustDescription" name="description" 
                                  rows="3" required placeholder="Alasan penyesuaian saldo UangTix..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Sesuaikan Saldo</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showAdjustBalanceModal(userId, userName, currentBalance) {
    document.getElementById('userId').value = userId;
    document.getElementById('userName').value = userName;
    document.getElementById('currentBalance').value = new Intl.NumberFormat('id-ID').format(currentBalance) + ' UTX';
    
    new bootstrap.Modal(document.getElementById('adjustBalanceModal')).show();
}

document.getElementById('adjustBalanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const userId = document.getElementById('userId').value;
    const formData = new FormData(this);
    
    try {
        const response = await fetch(`/admin/uangtix/${userId}/adjust`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('adjustBalanceModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

async function toggleBalanceStatus(userId, isActive) {
    if (!confirm(`Apakah Anda yakin ingin ${isActive === 'true' ? 'mengaktifkan' : 'menonaktifkan'} akun UangTix pengguna ini?`)) {
        return;
    }

    try {
        const response = await fetch(`/admin/uangtix/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: isActive === 'true'
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

async function exportData(type, format = 'json') {
    try {
        const url = `/admin/uangtix/export?type=${type}&format=${format}`;

        if (format === 'csv') {
            // For CSV, directly download the file
            window.location.href = url;
            showAlert('success', 'Data CSV berhasil didownload');
            return;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        if (data.success) {
            // Download JSON file
            const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            a.click();
            window.URL.revokeObjectURL(url);

            showAlert('success', 'Data berhasil diexport');
        } else {
            showAlert('error', 'Gagal export data');
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

function showAlert(type, message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}
</script>
@endpush
