<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'variant' => 'primary', // primary, secondary, success, warning, error, info, ghost, outline
    'size' => 'md', // xs, sm, md, lg, xl
    'icon' => null,
    'iconPosition' => 'left', // left, right
    'loading' => false,
    'disabled' => false,
    'href' => null,
    'target' => null,
    'fullWidth' => false
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'variant' => 'primary', // primary, secondary, success, warning, error, info, ghost, outline
    'size' => 'md', // xs, sm, md, lg, xl
    'icon' => null,
    'iconPosition' => 'left', // left, right
    'loading' => false,
    'disabled' => false,
    'href' => null,
    'target' => null,
    'fullWidth' => false
]); ?>
<?php foreach (array_filter(([
    'variant' => 'primary', // primary, secondary, success, warning, error, info, ghost, outline
    'size' => 'md', // xs, sm, md, lg, xl
    'icon' => null,
    'iconPosition' => 'left', // left, right
    'loading' => false,
    'disabled' => false,
    'href' => null,
    'target' => null,
    'fullWidth' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    $variants = [
        'primary' => 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white border-transparent',
        'secondary' => 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500 text-white border-transparent',
        'success' => 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white border-transparent',
        'warning' => 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white border-transparent',
        'error' => 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white border-transparent',
        'info' => 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white border-transparent',
        'ghost' => 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-gray-500 text-gray-700 dark:text-gray-300 border-transparent',
        'outline' => 'bg-transparent hover:bg-gray-50 dark:hover:bg-gray-800 focus:ring-gray-500 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600'
    ];
    
    $sizes = [
        'xs' => 'px-2.5 py-1.5 text-xs',
        'sm' => 'px-3 py-2 text-sm',
        'md' => 'px-4 py-2 text-sm',
        'lg' => 'px-4 py-2 text-base',
        'xl' => 'px-6 py-3 text-base'
    ];
    
    $iconSizes = [
        'xs' => 'w-3 h-3',
        'sm' => 'w-4 h-4',
        'md' => 'w-4 h-4',
        'lg' => 'w-5 h-5',
        'xl' => 'w-5 h-5'
    ];
    
    $variantClass = $variants[$variant] ?? $variants['primary'];
    $sizeClass = $sizes[$size] ?? $sizes['md'];
    $iconSizeClass = $iconSizes[$size] ?? $iconSizes['md'];
    
    $classes = collect([
        'inline-flex items-center justify-center',
        $sizeClass,
        $variantClass,
        'font-medium rounded-lg border',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'transition-all duration-200',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        $fullWidth ? 'w-full' : '',
        ($disabled || $loading) ? 'pointer-events-none' : ''
    ])->filter()->implode(' ');
?>

<?php if($href && !$disabled && !$loading): ?>
    <a href="<?php echo e($href); ?>" 
       <?php if($target): ?> target="<?php echo e($target); ?>" <?php endif; ?>
       <?php echo e($attributes->merge(['class' => $classes])); ?>>
        <?php if($icon && $iconPosition === 'left'): ?>
            <i data-lucide="<?php echo e($icon); ?>" class="<?php echo e($iconSizeClass); ?> <?php echo e($slot->isNotEmpty() ? 'mr-2' : ''); ?>"></i>
        <?php endif; ?>
        
        <?php echo e($slot); ?>

        
        <?php if($icon && $iconPosition === 'right'): ?>
            <i data-lucide="<?php echo e($icon); ?>" class="<?php echo e($iconSizeClass); ?> <?php echo e($slot->isNotEmpty() ? 'ml-2' : ''); ?>"></i>
        <?php endif; ?>
    </a>
<?php else: ?>
    <button <?php echo e($attributes->merge(['class' => $classes, 'disabled' => $disabled || $loading])); ?>>
        <?php if($loading): ?>
            <svg class="animate-spin <?php echo e($iconSizeClass); ?> <?php echo e($slot->isNotEmpty() ? 'mr-2' : ''); ?>" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        <?php elseif($icon && $iconPosition === 'left'): ?>
            <i data-lucide="<?php echo e($icon); ?>" class="<?php echo e($iconSizeClass); ?> <?php echo e($slot->isNotEmpty() ? 'mr-2' : ''); ?>"></i>
        <?php endif; ?>
        
        <?php echo e($slot); ?>

        
        <?php if(!$loading && $icon && $iconPosition === 'right'): ?>
            <i data-lucide="<?php echo e($icon); ?>" class="<?php echo e($iconSizeClass); ?> <?php echo e($slot->isNotEmpty() ? 'ml-2' : ''); ?>"></i>
        <?php endif; ?>
    </button>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/components/button.blade.php ENDPATH**/ ?>