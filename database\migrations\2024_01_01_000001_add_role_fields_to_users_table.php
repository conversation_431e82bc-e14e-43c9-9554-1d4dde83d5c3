<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->enum('role', ['admin', 'staff', 'penjual', 'pembeli'])->default('pembeli')->after('phone');
            $table->string('avatar')->nullable()->after('role');
            $table->date('birth_date')->nullable()->after('avatar');
            $table->enum('gender', ['male', 'female'])->nullable()->after('birth_date');
            $table->text('address')->nullable()->after('gender');
            $table->enum('user_level', ['star', 'star_plus', 'premium', 'platinum'])->default('star')->after('address');
            $table->boolean('is_active')->default(true)->after('user_level');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
            $table->string('otp_code')->nullable()->after('last_login_at');
            $table->timestamp('otp_expires_at')->nullable()->after('otp_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone', 'role', 'avatar', 'birth_date', 'gender',
                'address', 'is_active', 'last_login_at', 'otp_code', 'otp_expires_at'
            ]);
        });
    }
};
