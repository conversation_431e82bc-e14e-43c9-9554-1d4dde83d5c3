<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SettingsController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        
        // Get user settings or create default ones
        $settings = $user->settings ?? [
            'email_notifications' => true,
            'sms_notifications' => false,
            'marketing_emails' => true,
            'event_reminders' => true,
            'order_notifications' => true,
            'payment_notifications' => true,
            'timezone' => 'Asia/Jakarta',
            'language' => 'id',
            'currency' => 'IDR',
            'privacy_profile' => 'public',
            'show_attendance' => true,
        ];

        return view('pages.user.settings.index', compact('user', 'settings'));
    }

    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
            'event_reminders' => 'boolean',
            'order_notifications' => 'boolean',
            'payment_notifications' => 'boolean',
            'timezone' => 'required|string|max:50',
            'language' => 'required|string|in:id,en',
            'currency' => 'required|string|in:IDR,USD',
            'privacy_profile' => 'required|string|in:public,private',
            'show_attendance' => 'boolean',
        ]);

        $settings = [
            'email_notifications' => $request->boolean('email_notifications'),
            'sms_notifications' => $request->boolean('sms_notifications'),
            'marketing_emails' => $request->boolean('marketing_emails'),
            'event_reminders' => $request->boolean('event_reminders'),
            'order_notifications' => $request->boolean('order_notifications'),
            'payment_notifications' => $request->boolean('payment_notifications'),
            'timezone' => $request->timezone,
            'language' => $request->language,
            'currency' => $request->currency,
            'privacy_profile' => $request->privacy_profile,
            'show_attendance' => $request->boolean('show_attendance'),
        ];

        $user->update(['settings' => $settings]);

        return redirect()->route('settings.index')
            ->with('success', 'Settings updated successfully.');
    }
}
