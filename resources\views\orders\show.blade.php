@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Detail Pesanan</h1>
                    <p class="text-gray-600 mt-1">{{ $order->order_number }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('orders.index') }}" 
                       class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200">
                        Kembali
                    </a>
                    @if($order->payment_status == 'pending' && $order->status != 'cancelled')
                        <a href="{{ route('orders.payment', $order) }}" 
                           class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">
                            Bayar Sekarang
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Order Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Order Status -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Status Pesanan</h2>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-600">Status Pesanan</p>
                            @if($order->status == 'pending')
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800 mt-1">
                                    Menunggu Konfirmasi
                                </span>
                            @elseif($order->status == 'confirmed')
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 mt-1">
                                    Dikonfirmasi
                                </span>
                            @elseif($order->status == 'cancelled')
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 mt-1">
                                    Dibatalkan
                                </span>
                            @endif
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Status Pembayaran</p>
                            @if($order->payment_status == 'pending')
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-orange-100 text-orange-800 mt-1">
                                    Menunggu Pembayaran
                                </span>
                            @elseif($order->payment_status == 'paid')
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800 mt-1">
                                    Sudah Dibayar
                                </span>
                            @elseif($order->payment_status == 'failed')
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 mt-1">
                                    Pembayaran Gagal
                                </span>
                            @elseif($order->payment_status == 'cancelled')
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800 mt-1">
                                    Dibatalkan
                                </span>
                            @endif
                        </div>
                    </div>

                    @if($order->payment_status == 'pending' && $order->expires_at)
                        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <p class="text-sm text-yellow-800">
                                    <strong>Batas waktu pembayaran:</strong> {{ $order->expires_at->format('d M Y, H:i') }}
                                </p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Event Information -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Informasi Event</h2>
                    <div class="flex items-start space-x-4">
                        <img src="{{ $order->event->poster_url }}" alt="{{ $order->event->title }}" 
                             class="w-20 h-20 rounded-lg object-cover">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900">{{ $order->event->title }}</h3>
                            <div class="mt-2 space-y-1 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    {{ $order->event->formatted_date }}
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    {{ $order->event->venue_name }}, {{ $order->event->city }}
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                    </svg>
                                    {{ $order->event->category->name ?? 'Kategori' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tickets -->
                @if($order->tickets->count() > 0)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Tiket</h2>
                        <div class="space-y-3">
                            @foreach($order->tickets as $ticket)
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900">{{ $ticket->ticket_number }}</p>
                                        <p class="text-sm text-gray-600">
                                            Status: 
                                            @if($ticket->status == 'active')
                                                <span class="text-green-600 font-medium">Aktif</span>
                                            @elseif($ticket->status == 'used')
                                                <span class="text-blue-600 font-medium">Sudah Digunakan</span>
                                            @elseif($ticket->status == 'cancelled')
                                                <span class="text-red-600 font-medium">Dibatalkan</span>
                                            @endif
                                        </p>
                                    </div>
                                    @if($ticket->status == 'active')
                                        <a href="{{ route('tickets.show', $ticket) }}" 
                                           class="px-3 py-1 bg-primary text-white text-sm rounded-lg hover:bg-primary/90 transition-colors">
                                            Lihat Tiket
                                        </a>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Order Summary -->
            <div class="space-y-6">
                <!-- Payment Summary -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Ringkasan Pembayaran</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Harga Tiket</span>
                            <span class="text-gray-900">Rp {{ number_format($order->event->price, 0, ',', '.') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Jumlah Tiket</span>
                            <span class="text-gray-900">{{ $order->quantity }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="text-gray-900">Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                        </div>
                        @if($order->admin_fee > 0)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Biaya Admin</span>
                                <span class="text-gray-900">Rp {{ number_format($order->admin_fee, 0, ',', '.') }}</span>
                            </div>
                        @endif
                        @if($order->discount > 0)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Diskon</span>
                                <span class="text-green-600">-Rp {{ number_format($order->discount, 0, ',', '.') }}</span>
                            </div>
                        @endif
                        <div class="border-t border-gray-200 pt-3">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-gray-900">Total</span>
                                <span class="text-lg font-semibold text-primary">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</span>
                            </div>
                        </div>
                    </div>

                    @if($order->payment_method)
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <p class="text-sm text-gray-600">Metode Pembayaran</p>
                            <p class="font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}</p>
                            @if($order->payment_reference)
                                <p class="text-sm text-gray-600 mt-1">Ref: {{ $order->payment_reference }}</p>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Order Information -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Informasi Pesanan</h2>
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600">Nomor Pesanan:</span>
                            <p class="font-medium text-gray-900">{{ $order->order_number }}</p>
                        </div>
                        <div>
                            <span class="text-gray-600">Tanggal Pesanan:</span>
                            <p class="font-medium text-gray-900">{{ $order->created_at->format('d M Y, H:i') }}</p>
                        </div>
                        @if($order->confirmed_at)
                            <div>
                                <span class="text-gray-600">Dikonfirmasi:</span>
                                <p class="font-medium text-gray-900">{{ $order->confirmed_at->format('d M Y, H:i') }}</p>
                            </div>
                        @endif
                        @if($order->paid_at)
                            <div>
                                <span class="text-gray-600">Dibayar:</span>
                                <p class="font-medium text-gray-900">{{ $order->paid_at->format('d M Y, H:i') }}</p>
                            </div>
                        @endif
                        @if($order->cancelled_at)
                            <div>
                                <span class="text-gray-600">Dibatalkan:</span>
                                <p class="font-medium text-gray-900">{{ $order->cancelled_at->format('d M Y, H:i') }}</p>
                                @if($order->cancellation_reason)
                                    <p class="text-sm text-gray-600 mt-1">Alasan: {{ $order->cancellation_reason }}</p>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                @if($order->payment_status == 'pending' && $order->status != 'cancelled')
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Aksi</h2>
                        <div class="space-y-3">
                            <a href="{{ route('orders.payment', $order) }}" 
                               class="w-full bg-primary text-white text-center py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors duration-200 block">
                                Bayar Sekarang
                            </a>
                            <button onclick="cancelOrder()" 
                                    class="w-full bg-red-100 text-red-700 text-center py-2 px-4 rounded-lg hover:bg-red-200 transition-colors duration-200">
                                Batalkan Pesanan
                            </button>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Cancel Order Modal -->
<div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Batalkan Pesanan</h3>
            <form action="{{ route('orders.cancel', $order) }}" method="POST">
                @csrf
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Alasan Pembatalan</label>
                    <textarea name="reason" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Masukkan alasan pembatalan..."></textarea>
                </div>
                <div class="flex space-x-3">
                    <button type="button" onclick="closeCancelModal()" 
                            class="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        Batal
                    </button>
                    <button type="submit" 
                            class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        Batalkan Pesanan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function cancelOrder() {
    document.getElementById('cancelModal').classList.remove('hidden');
}

function closeCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCancelModal();
    }
});
</script>
@endpush
