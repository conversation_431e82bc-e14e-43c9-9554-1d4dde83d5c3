@extends('layouts.main')

@section('title', 'Notifikasi')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Notifikasi</h1>
                    <p class="text-gray-600 mt-1">Ke<PERSON>la semua notifikasi Anda</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="markAllNotificationsAsRead()" 
                            class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors duration-200">
                        Tandai Semua Dibaca
                    </button>
                    <button onclick="testNotification()" 
                            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200">
                        Test Notifikasi
                    </button>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Notifikasi</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                        <h3 class="font-medium text-gray-900">Suara Notifikasi</h3>
                        <p class="text-sm text-gray-600">Aktifkan suara untuk notifikasi baru</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="soundToggle" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                        <h3 class="font-medium text-gray-900">Browser Notifications</h3>
                        <p class="text-sm text-gray-600">Tampilkan notifikasi browser</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="browserToggle" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                        <h3 class="font-medium text-gray-900">Auto Refresh</h3>
                        <p class="text-sm text-gray-600">Perbarui notifikasi otomatis</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="autoRefreshToggle" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                    </label>
                </div>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">Semua Notifikasi</h2>
                    <div class="flex items-center space-x-2">
                        <select id="filterType" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                            <option value="">Semua Tipe</option>
                            <option value="system">Sistem</option>
                            <option value="event">Event</option>
                            <option value="payment">Pembayaran</option>
                            <option value="order">Pesanan</option>
                            <option value="ticket">Tiket</option>
                        </select>
                        <select id="filterStatus" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                            <option value="">Semua Status</option>
                            <option value="unread">Belum Dibaca</option>
                            <option value="read">Sudah Dibaca</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div id="notificationsList" class="divide-y divide-gray-200">
                <!-- Loading State -->
                <div class="p-8 text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p class="text-gray-500">Memuat notifikasi...</p>
                </div>
            </div>
            
            <!-- Load More Button -->
            <div class="p-6 border-t border-gray-200 text-center">
                <button id="loadMoreBtn" class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200" style="display: none;">
                    Muat Lebih Banyak
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = 1;
    let isLoading = false;
    let hasMore = true;
    
    // Load initial notifications
    loadNotifications();
    
    // Filter change handlers
    document.getElementById('filterType').addEventListener('change', () => {
        currentPage = 1;
        hasMore = true;
        loadNotifications(true);
    });
    
    document.getElementById('filterStatus').addEventListener('change', () => {
        currentPage = 1;
        hasMore = true;
        loadNotifications(true);
    });
    
    // Load more button
    document.getElementById('loadMoreBtn').addEventListener('click', () => {
        currentPage++;
        loadNotifications(false);
    });
    
    // Settings toggles
    document.getElementById('soundToggle').addEventListener('change', function() {
        if (window.liveNotifications) {
            window.liveNotifications.soundEnabled = this.checked;
        }
    });
    
    document.getElementById('browserToggle').addEventListener('change', function() {
        if (this.checked) {
            Notification.requestPermission();
        }
    });
    
    document.getElementById('autoRefreshToggle').addEventListener('change', function() {
        if (window.liveNotifications) {
            if (this.checked) {
                window.liveNotifications.startPolling();
            } else {
                window.liveNotifications.stopPolling();
            }
        }
    });
    
    async function loadNotifications(reset = false) {
        if (isLoading) return;
        isLoading = true;
        
        try {
            const params = new URLSearchParams({
                page: currentPage,
                type: document.getElementById('filterType').value,
                status: document.getElementById('filterStatus').value
            });
            
            const response = await fetch(`/notifications?${params}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                }
            });
            
            if (!response.ok) throw new Error('Failed to load notifications');
            
            const data = await response.json();
            const container = document.getElementById('notificationsList');
            
            if (reset) {
                container.innerHTML = '';
            }
            
            if (data.notifications.length === 0 && currentPage === 1) {
                container.innerHTML = `
                    <div class="p-8 text-center">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                        </svg>
                        <p class="text-gray-500">Tidak ada notifikasi</p>
                    </div>
                `;
                return;
            }
            
            data.notifications.forEach(notification => {
                container.appendChild(createNotificationElement(notification));
            });
            
            hasMore = data.notifications.length === 20; // Assuming 20 per page
            document.getElementById('loadMoreBtn').style.display = hasMore ? 'block' : 'none';
            
        } catch (error) {
            console.error('Error loading notifications:', error);
            document.getElementById('notificationsList').innerHTML = `
                <div class="p-8 text-center">
                    <p class="text-red-500">Gagal memuat notifikasi</p>
                    <button onclick="loadNotifications(true)" class="mt-2 px-4 py-2 bg-primary text-white rounded-lg">Coba Lagi</button>
                </div>
            `;
        } finally {
            isLoading = false;
        }
    }
    
    function createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification-item p-6 hover:bg-gray-50 cursor-pointer ${!notification.is_read ? 'bg-blue-50/50' : ''}`;
        element.dataset.notificationId = notification.id;
        
        const colorClass = getNotificationColorClass(notification.type);
        const priorityBadge = getPriorityBadge(notification.priority);
        
        element.innerHTML = `
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-3 h-3 ${colorClass} rounded-full ${notification.is_read ? 'opacity-50' : ''}"></div>
                </div>
                <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <h3 class="text-sm font-semibold text-gray-900">${notification.title}</h3>
                                ${priorityBadge}
                                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">${notification.type}</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">${notification.message}</p>
                            <p class="text-xs text-gray-500">${notification.created_at}</p>
                        </div>
                        <div class="flex items-center space-x-2 ml-4">
                            ${!notification.is_read ? `
                                <button onclick="markAsRead(${notification.id})" class="text-xs text-primary hover:text-accent">
                                    Tandai Dibaca
                                </button>
                            ` : ''}
                            <button onclick="removeNotification(${notification.id})" class="text-gray-400 hover:text-red-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add click handler for action URL
        if (notification.action_url) {
            element.addEventListener('click', (e) => {
                if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'SVG' && e.target.tagName !== 'PATH') {
                    window.location.href = notification.action_url;
                }
            });
        }
        
        return element;
    }
    
    function getNotificationColorClass(type) {
        const colorMap = {
            'system': 'bg-blue-500',
            'event': 'bg-purple-500',
            'payment': 'bg-green-500',
            'order': 'bg-orange-500',
            'ticket': 'bg-indigo-500'
        };
        return colorMap[type] || 'bg-gray-500';
    }
    
    function getPriorityBadge(priority) {
        const badges = {
            'low': '<span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Rendah</span>',
            'normal': '',
            'high': '<span class="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full">Tinggi</span>',
            'urgent': '<span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full animate-pulse">Mendesak</span>'
        };
        return badges[priority] || '';
    }
    
    window.markAsRead = async function(notificationId) {
        try {
            const response = await fetch(`/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                }
            });
            
            if (response.ok) {
                const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (element) {
                    element.classList.remove('bg-blue-50/50');
                    element.querySelector('.w-3').classList.add('opacity-50');
                    element.querySelector('button[onclick*="markAsRead"]')?.remove();
                }
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    };
    
    window.testNotification = async function() {
        try {
            const response = await fetch('/notifications/test', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                }
            });
            
            if (response.ok) {
                // Reload notifications after a short delay
                setTimeout(() => {
                    currentPage = 1;
                    hasMore = true;
                    loadNotifications(true);
                }, 1000);
            }
        } catch (error) {
            console.error('Error creating test notification:', error);
        }
    };
});
</script>
@endsection
