@echo off
echo Retry Migration for TiXara (Fix Current Issues)...

echo.
echo [1/8] Checking current migration status...
php artisan migrate:status

echo.
echo [2/8] Rolling back problematic migrations...
php artisan migrate:rollback --step=5

echo.
echo [3/8] Checking which tables exist...
php artisan tinker --execute="
try {
    echo 'Current tables in database:' . PHP_EOL;
    \$tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');
    
    if (empty(\$tables)) {
        echo 'No tables found - database is clean' . PHP_EOL;
    } else {
        foreach (\$tables as \$table) {
            \$tableName = array_values((array)\$table)[0];
            echo '- ' . \$tableName . PHP_EOL;
        }
    }
} catch (Exception \$e) {
    echo 'Error checking tables: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [4/8] Disabling foreign key checks...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=0;');
    echo 'Foreign key checks disabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/8] Running migrations in correct order...
echo.
echo Creating base tables first...
php artisan migrate --path=database/migrations/2014_10_12_000000_create_users_table.php
php artisan migrate --path=database/migrations/2014_10_12_100000_create_password_reset_tokens_table.php
php artisan migrate --path=database/migrations/2019_08_19_000000_create_failed_jobs_table.php
php artisan migrate --path=database/migrations/2019_12_14_000001_create_personal_access_tokens_table.php

echo.
echo Adding user role fields...
php artisan migrate --path=database/migrations/2024_01_01_000001_add_role_fields_to_users_table.php

echo.
echo Creating categories table...
php artisan migrate --path=database/migrations/2024_01_01_000002_create_categories_table.php

echo.
echo Creating tickets table...
php artisan migrate --path=database/migrations/2024_01_01_000003_create_tickets_table.php

echo.
echo Creating orders table (fixed)...
php artisan migrate --path=database/migrations/2024_01_01_000004_create_orders_table.php

echo.
echo Creating tickets table (fixed)...
php artisan migrate --path=database/migrations/2024_01_01_000005_create_tickets_table.php

echo.
echo [6/8] Re-enabling foreign key checks...
php artisan tinker --execute="
try {
    \Illuminate\Support\Facades\DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    echo 'Foreign key checks re-enabled' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [7/8] Running remaining migrations...
php artisan migrate

echo.
echo [8/8] Verifying migration success...
php artisan tinker --execute="
try {
    echo 'Verifying all tables exist:' . PHP_EOL;
    
    \$requiredTables = [
        'users',
        'password_reset_tokens', 
        'failed_jobs',
        'personal_access_tokens',
        'categories',
        'tickets', 
        'orders',
        'tickets',
        'notifications'
    ];
    
    \$missingTables = [];
    
    foreach (\$requiredTables as \$table) {
        if (\Illuminate\Support\Facades\Schema::hasTable(\$table)) {
            echo '✓ ' . \$table . PHP_EOL;
        } else {
            echo '✗ ' . \$table . ' MISSING' . PHP_EOL;
            \$missingTables[] = \$table;
        }
    }
    
    if (empty(\$missingTables)) {
        echo PHP_EOL . '🎉 All tables created successfully!' . PHP_EOL;
        
        // Test foreign key relationships
        echo PHP_EOL . 'Testing foreign key relationships:' . PHP_EOL;
        
        try {
            // Test tickets table foreign keys
            \$ticketsColumns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
            if (in_array('category_id', \$ticketsColumns) && in_array('organizer_id', \$ticketsColumns)) {
                echo '✓ Tickets table has correct foreign key columns' . PHP_EOL;
            }
            
            // Test orders table foreign keys  
            \$ordersColumns = \Illuminate\Support\Facades\Schema::getColumnListing('orders');
            if (in_array('event_id', \$ordersColumns) && in_array('user_id', \$ordersColumns)) {
                echo '✓ Orders table has correct foreign key columns' . PHP_EOL;
            }
            
            // Test tickets table foreign keys
            \$ticketsColumns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
            if (in_array('event_id', \$ticketsColumns) && in_array('buyer_id', \$ticketsColumns)) {
                echo '✓ Tickets table has correct foreign key columns' . PHP_EOL;
            }
            
        } catch (Exception \$e) {
            echo 'Foreign key test error: ' . \$e->getMessage() . PHP_EOL;
        }
        
    } else {
        echo PHP_EOL . '❌ Missing tables: ' . implode(', ', \$missingTables) . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Verification error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo ========================================
echo Migration Retry Results
echo ========================================
echo.
echo ✓ FIXED ISSUES:
echo   - Foreign key constraint errors
echo   - Table dependency problems  
echo   - Migration order issues
echo   - Column reference errors
echo.
echo ✓ CORRECTED REFERENCES:
echo   - orders.tiket_id -> orders.event_id
echo   - tickets.tiket_id -> tickets.event_id
echo   - All foreign keys now reference correct tables
echo.
echo ✓ MIGRATION ORDER:
echo   1. users (base table)
echo   2. categories (independent)
echo   3. tickets (depends on users, categories)
echo   4. orders (depends on users, tickets)
echo   5. tickets (depends on tickets, users, orders)
echo.
echo ✓ NEXT STEPS:
echo   - Run: php artisan db:seed (to add sample data)
echo   - Test: php artisan serve (to start server)
echo   - Access: http://localhost:8000
echo.
echo If migration still fails:
echo 1. Run: fresh-migrate.bat (for complete reset)
echo 2. Check .env database configuration
echo 3. Ensure MySQL/MariaDB is running
echo.
pause
