<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\User;
use App\Models\Category;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Exception;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Show admin dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $request->get('range', '30');
        $startDate = Carbon::now()->subDays($dateRange);

        // Platform Statistics
        $platformStats = $this->getPlatformStats($startDate);

        // Revenue Analytics
        $revenueAnalytics = $this->getRevenueAnalytics($startDate);

        // User Analytics
        $userAnalytics = $this->getUserAnalytics($startDate);

        // Event Analytics
        $eventAnalytics = $this->getEventAnalytics($startDate);

        // Geographic Analytics
        $geographicData = $this->getGeographicAnalytics($startDate);

        // Category Performance
        $categoryPerformance = $this->getCategoryPerformance($startDate);

        // Recent Activities
        $recentActivities = $this->getRecentActivities();

        // System Health
        $systemHealth = $this->getSystemHealth();

        // Chart Data
        $chartData = $this->getChartData($startDate);

        return view('pages.admin.dashboard', compact(
            'platformStats',
            'revenueAnalytics',
            'userAnalytics',
            'eventAnalytics',
            'geographicData',
            'categoryPerformance',
            'recentActivities',
            'systemHealth',
            'chartData',
            'dateRange'
        ));
    }

    /**
     * Get platform statistics
     */
    private function getPlatformStats($startDate)
    {
        return [
            'total_users' => User::count(),
            'new_users' => User::where('created_at', '>=', $startDate)->count(),
            'total_organizers' => User::where('role', 'penjual')->count(),
            'active_organizers' => User::where('role', 'penjual')
                ->whereHas('events', function($q) use ($startDate) {
                    $q->where('created_at', '>=', $startDate);
                })->count(),
            'total_tickets' => Event::count(),
            'published_tickets' => Event::where('status', 'published')->count(),
            'new_tickets' => Event::where('created_at', '>=', $startDate)->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'period_revenue' => Order::where('payment_status', 'paid')
                ->where('created_at', '>=', $startDate)->sum('total_amount'),
            'total_tickets_sold' => Ticket::where('status', '!=', 'cancelled')->count(),
            'period_tickets_sold' => Ticket::where('status', '!=', 'cancelled')
                ->where('created_at', '>=', $startDate)->count(),
            'platform_fee_revenue' => Order::where('payment_status', 'paid')->sum('admin_fee'),
            'avg_order_value' => Order::where('payment_status', 'paid')->avg('total_amount') ?? 0,
        ];
    }

    /**
     * Get revenue analytics
     */
    private function getRevenueAnalytics($startDate)
    {
        $dailyRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as total_revenue'),
                DB::raw('SUM(admin_fee) as platform_revenue'),
                DB::raw('COUNT(*) as orders'),
                DB::raw('SUM(quantity) as tickets')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $monthlyRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as total_revenue'),
                DB::raw('SUM(admin_fee) as platform_revenue'),
                DB::raw('COUNT(*) as orders')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'daily' => $dailyRevenue,
            'monthly' => $monthlyRevenue,
            'growth_rate' => $this->calculateRevenueGrowthRate($startDate),
            'top_revenue_tickets' => $this->getTopRevenuetickets($startDate),
        ];
    }

    /**
     * Get user analytics
     */
    private function getUserAnalytics($startDate)
    {
        $userGrowth = User::where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as new_users'),
                'role'
            )
            ->groupBy('date', 'role')
            ->orderBy('date')
            ->get();

        $usersByRole = User::select('role', DB::raw('COUNT(*) as count'))
            ->groupBy('role')
            ->get();

        $activeUsers = User::whereHas('orders', function($q) use ($startDate) {
            $q->where('created_at', '>=', $startDate);
        })->count();

        return [
            'growth' => $userGrowth,
            'by_role' => $usersByRole,
            'active_users' => $activeUsers,
            'user_retention' => $this->calculateUserRetention($startDate),
        ];
    }

    /**
     * Get event analytics
     */
    private function getEventAnalytics($startDate)
    {
        $ticketsByStatus = Event::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();

        $ticketsByCategory = Event::join('categories', 'events.category_id', '=', 'categories.id')
            ->select('categories.name', DB::raw('COUNT(*) as count'))
            ->groupBy('categories.name')
            ->get();

        $popularTickets = Event::withCount(['tickets' => function($q) {
            $q->where('status', '!=', 'cancelled');
        }])
        ->orderBy('tickets_count', 'desc')
        ->take(10)
        ->get();

        return [
            'by_status' => $ticketsByStatus,
            'by_category' => $ticketsByCategory,
            'popular_tickets' => $popularTickets,
            'avg_capacity_utilization' => $this->calculateCapacityUtilization(),
        ];
    }

    /**
     * Get geographic analytics
     */
    private function getGeographicAnalytics($startDate)
    {
        // Get events by city with ticket counts
        $citiesData = Event::select('city',
                DB::raw('COUNT(*) as events_count'),
                DB::raw('SUM(total_capacity) as total_capacity'))
            ->groupBy('city')
            ->orderBy('events_count', 'desc')
            ->take(10)
            ->get()
            ->map(function($city) {
                // Get tickets sold for this city
                $ticketsSold = Event::where('city', $city->city)
                    ->get()
                    ->sum(function($event) {
                        return $event->tickets()->where('status', '!=', 'refunded')->count();
                    });

                return [
                    'city' => $city->city ?? 'Unknown City',
                    'events_count' => $city->events_count ?? 0,
                    'tickets_sold' => $ticketsSold ?? 0,
                    'total_capacity' => $city->total_capacity ?? 0,
                ];
            });

        $revenueByCity = Order::join('events', 'orders.event_id', '=', 'events.id')
            ->where('orders.payment_status', 'paid')
            ->where('orders.created_at', '>=', $startDate)
            ->select('events.city', DB::raw('SUM(orders.total_amount) as revenue'))
            ->groupBy('events.city')
            ->orderBy('revenue', 'desc')
            ->take(10)
            ->get();

        return $citiesData; // Return the formatted cities data for the view
    }

    /**
     * Get category performance
     */
    private function getCategoryPerformance($startDate)
    {
        return Category::withCount(['events' => function($q) use ($startDate) {
            $q->where('created_at', '>=', $startDate);
        }])
        ->with(['events' => function($q) use ($startDate) {
            $q->where('created_at', '>=', $startDate)
              ->with(['orders' => function($oq) {
                  $oq->where('payment_status', 'paid');
              }]);
        }])
        ->get()
        ->map(function($category) {
            $revenue = $category->events->sum(function($event) {
                return $event->orders->sum('total_amount');
            });

            $ticketsSold = $category->events->sum(function($event) {
                return $event->tickets()->where('status', '!=', 'refunded')->count();
            });

            return [
                'name' => $category->name ?? 'Unknown Category',
                'events_count' => $category->events_count ?? 0,
                'revenue' => $revenue ?? 0,
                'total_revenue' => $revenue ?? 0, // Alias for view compatibility
                'tickets_sold' => $ticketsSold ?? 0,
                'avg_revenue_per_event' => $category->events_count > 0 ?
                    round($revenue / $category->events_count, 0) : 0,
            ];
        })
        ->sortByDesc('revenue')
        ->values();
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        $activities = collect();

        // Recent user registrations
        $newUsers = User::latest()->take(5)->get()->map(function($user) {
            return [
                'type' => 'user_registration',
                'title' => 'User baru mendaftar',
                'description' => ($user->name ?? 'Unknown') . " bergabung sebagai " . ($user->role ?? 'user'),
                'created_at' => $user->created_at ?? now(),
                'user' => $user->name ?? 'Unknown User',
            ];
        });

        // Recent event publications
        $newTickets = Event::where('status', 'published')
            ->latest('updated_at')
            ->take(5)
            ->with('organizer')
            ->get()
            ->map(function($event) {
                return [
                    'type' => 'event_published',
                    'title' => 'Event baru dipublikasi',
                    'description' => ($event->title ?? 'Unknown Event') . " oleh " . ($event->organizer->name ?? 'Unknown Organizer'),
                    'created_at' => $event->updated_at ?? $event->created_at ?? now(),
                    'event' => $event->title ?? 'Unknown Event',
                ];
            });

        // High-value orders
        $highValueOrders = Order::where('payment_status', 'paid')
            ->where('total_amount', '>', 1000000)
            ->latest()
            ->take(3)
            ->with(['event', 'user'])
            ->get()
            ->map(function($order) {
                return [
                    'type' => 'high_value_order',
                    'title' => 'Pesanan bernilai tinggi',
                    'description' => "Rp " . number_format($order->total_amount ?? 0, 0, ',', '.') .
                                   " untuk " . ($order->event->title ?? 'Unknown Event'),
                    'created_at' => $order->created_at ?? now(),
                    'amount' => $order->total_amount ?? 0,
                ];
            });

        return $activities->merge($newUsers)
            ->merge($newTickets)
            ->merge($highValueOrders)
            ->sortByDesc('created_at')
            ->take(15)
            ->values();
    }

    /**
     * Get system health metrics
     */
    private function getSystemHealth()
    {
        $health = [
            'overall_status' => 'healthy',
            'database' => $this->checkDatabaseHealth(),
            'storage' => $this->checkStorageHealth(),
            'cache' => $this->checkCacheHealth(),
            'api' => $this->checkApiHealth(),
            'queue' => $this->checkQueueHealth(),
            'last_updated' => now()->format('H:i:s'),
            'uptime' => $this->getSystemUptime(),
            'memory_usage' => $this->getMemoryUsage(),
        ];

        // Determine overall status
        $criticalIssues = 0;
        $warningIssues = 0;

        foreach (['database', 'storage', 'cache', 'api', 'queue'] as $component) {
            if ($health[$component]['status'] === 'critical') {
                $criticalIssues++;
            } elseif ($health[$component]['status'] === 'warning') {
                $warningIssues++;
            }
        }

        if ($criticalIssues > 0) {
            $health['overall_status'] = 'critical';
        } elseif ($warningIssues > 0) {
            $health['overall_status'] = 'warning';
        }

        return $health;
    }

    /**
     * Calculate revenue growth rate
     */
    private function calculateRevenueGrowthRate($startDate)
    {
        $currentPeriod = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', $startDate)
            ->sum('total_amount');

        $previousStart = $startDate->copy()->subDays($startDate->diffInDays(now()));
        $previousPeriod = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$previousStart, $startDate])
            ->sum('total_amount');

        if ($previousPeriod > 0) {
            return round((($currentPeriod - $previousPeriod) / $previousPeriod) * 100, 2);
        }

        return $currentPeriod > 0 ? 100 : 0;
    }

    /**
     * Get top revenue tickets
     */
    private function getTopRevenueTickets($startDate)
    {
        return Event::whereHas('orders', function($q) use ($startDate) {
            $q->where('payment_status', 'paid')
              ->where('created_at', '>=', $startDate);
        })
        ->with(['orders' => function($q) use ($startDate) {
            $q->where('payment_status', 'paid')
              ->where('created_at', '>=', $startDate);
        }])
        ->get()
        ->map(function($event) {
            return [
                'title' => $event->title ?? 'Unknown Event',
                'revenue' => $event->orders->sum('total_amount') ?? 0,
                'tickets_sold' => $event->orders->sum('quantity') ?? 0,
            ];
        })
        ->sortByDesc('revenue')
        ->take(5)
        ->values();
    }

    /**
     * Calculate user retention
     */
    private function calculateUserRetention($startDate)
    {
        $newUsers = User::where('created_at', '>=', $startDate)->count();
        $activeNewUsers = User::where('created_at', '>=', $startDate)
            ->whereHas('orders', function($q) {
                $q->where('created_at', '>', DB::raw('users.created_at'));
            })
            ->count();

        return $newUsers > 0 ? round(($activeNewUsers / $newUsers) * 100, 2) : 0;
    }

    /**
     * Calculate capacity utilization
     */
    private function calculateCapacityUtilization()
    {
        $tickets = Event::where('status', 'published')->get();
        $totalCapacity = $tickets->sum('total_capacity');
        $totalSold = $tickets->sum(function($event) {
            return $event->tickets()->where('status', '!=', 'refunded')->count();
        });

        return $totalCapacity > 0 ? round(($totalSold / $totalCapacity) * 100, 2) : 0;
    }

    // System health check methods
    private function checkDatabaseHealth()
    {
        try {
            $start = microtime(true);
            DB::connection()->getPdo();
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            // Check database size
            $size = DB::select("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size' FROM information_schema.tables WHERE table_schema = ?", [config('database.connections.mysql.database')]);
            $dbSize = $size[0]->size ?? 0;

            $status = 'healthy';
            if ($responseTime > 1000) {
                $status = 'critical';
            } elseif ($responseTime > 500) {
                $status = 'warning';
            }

            return [
                'status' => $status,
                'message' => $status === 'healthy' ? 'Connected' : 'Slow response',
                'response_time' => $responseTime . 'ms',
                'size' => $dbSize . ' MB',
                'details' => [
                    'connection' => 'active',
                    'response_time' => $responseTime,
                    'size_mb' => $dbSize
                ]
            ];
        } catch (Exception $e) {
            return [
                'status' => 'critical',
                'message' => 'Connection failed',
                'response_time' => 'N/A',
                'size' => 'N/A',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    private function checkStorageHealth()
    {
        try {
            $storagePath = storage_path();
            $totalBytes = disk_total_space($storagePath);
            $freeBytes = disk_free_space($storagePath);
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercent = round(($usedBytes / $totalBytes) * 100, 1);

            $status = 'healthy';
            if ($usagePercent > 90) {
                $status = 'critical';
            } elseif ($usagePercent > 80) {
                $status = 'warning';
            }

            return [
                'status' => $status,
                'message' => $usagePercent . '% used',
                'usage_percent' => $usagePercent,
                'free_space' => $this->formatBytes($freeBytes),
                'total_space' => $this->formatBytes($totalBytes),
                'details' => [
                    'used_bytes' => $usedBytes,
                    'free_bytes' => $freeBytes,
                    'total_bytes' => $totalBytes
                ]
            ];
        } catch (Exception $e) {
            return [
                'status' => 'critical',
                'message' => 'Check failed',
                'usage_percent' => 'N/A',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    private function checkCacheHealth()
    {
        try {
            $start = microtime(true);
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';

            // Test cache write
            Cache::put($testKey, $testValue, 60);

            // Test cache read
            $retrieved = Cache::get($testKey);
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            // Clean up
            Cache::forget($testKey);

            $status = 'healthy';
            if ($retrieved !== $testValue) {
                $status = 'critical';
            } elseif ($responseTime > 100) {
                $status = 'warning';
            }

            return [
                'status' => $status,
                'message' => $status === 'healthy' ? 'Working' : 'Issues detected',
                'response_time' => $responseTime . 'ms',
                'driver' => config('cache.default'),
                'details' => [
                    'driver' => config('cache.default'),
                    'response_time' => $responseTime,
                    'read_write_test' => $retrieved === $testValue ? 'passed' : 'failed'
                ]
            ];
        } catch (Exception $e) {
            return [
                'status' => 'critical',
                'message' => 'Cache failed',
                'response_time' => 'N/A',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    private function checkApiHealth()
    {
        try {
            $start = microtime(true);

            // Test internal API endpoint
            $response = Http::timeout(5)->get(url('/api/health-check'));
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            $status = 'healthy';
            if (!$response->successful()) {
                $status = 'critical';
            } elseif ($responseTime > 2000) {
                $status = 'warning';
            }

            return [
                'status' => $status,
                'message' => $response->successful() ? 'Online' : 'Offline',
                'response_time' => $responseTime . 'ms',
                'http_status' => $response->status(),
                'details' => [
                    'response_time' => $responseTime,
                    'status_code' => $response->status(),
                    'endpoint' => '/api/health-check'
                ]
            ];
        } catch (Exception $e) {
            return [
                'status' => 'critical',
                'message' => 'API unavailable',
                'response_time' => 'N/A',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    private function checkQueueHealth()
    {
        try {
            $queueSize = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            $status = 'healthy';
            if ($failedJobs > 10 || $queueSize > 1000) {
                $status = 'critical';
            } elseif ($failedJobs > 5 || $queueSize > 500) {
                $status = 'warning';
            }

            return [
                'status' => $status,
                'message' => $queueSize . ' pending',
                'pending_jobs' => $queueSize,
                'failed_jobs' => $failedJobs,
                'details' => [
                    'pending' => $queueSize,
                    'failed' => $failedJobs,
                    'driver' => config('queue.default')
                ]
            ];
        } catch (Exception $e) {
            return [
                'status' => 'warning',
                'message' => 'Check unavailable',
                'pending_jobs' => 'N/A',
                'failed_jobs' => 'N/A',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    private function getSystemUptime()
    {
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                return 'N/A (Windows)';
            }

            $uptime = shell_exec('uptime -p');
            return trim($uptime) ?: 'N/A';
        } catch (Exception $e) {
            return 'N/A';
        }
    }

    private function getMemoryUsage()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');

        return [
            'current' => $this->formatBytes($memoryUsage),
            'limit' => $memoryLimit,
            'percent' => round(($memoryUsage / $this->parseBytes($memoryLimit)) * 100, 1)
        ];
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    private function parseBytes($val)
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int) $val;

        switch($last) {
            case 'g': $val *= 1024;
            case 'm': $val *= 1024;
            case 'k': $val *= 1024;
        }

        return $val;
    }

    /**
     * Get chart data for dashboard
     */
    private function getChartData($startDate)
    {
        // Revenue chart data
        $revenueData = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as revenue')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // User growth chart data
        $userGrowthData = User::where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as new_users'),
                DB::raw('SUM(CASE WHEN role = "penjual" THEN 1 ELSE 0 END) as new_organizers')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'revenue' => [
                'labels' => $revenueData->pluck('date')->map(function($date) {
                    return Carbon::parse($date)->format('M d');
                })->toArray(),
                'data' => $revenueData->pluck('revenue')->toArray(),
            ],
            'users' => [
                'labels' => $userGrowthData->pluck('date')->map(function($date) {
                    return Carbon::parse($date)->format('M d');
                })->toArray(),
                'new_users' => $userGrowthData->pluck('new_users')->toArray(),
                'new_organizers' => $userGrowthData->pluck('new_organizers')->toArray(),
            ],
        ];
    }

    /**
     * Export dashboard report
     */
    public function exportReport(Request $request)
    {
        $range = $request->get('range', '30');
        $startDate = Carbon::now()->subDays($range);

        $data = [
            'platform_stats' => $this->getPlatformStats($startDate),
            'revenue_analytics' => $this->getRevenueAnalytics($startDate),
            'user_analytics' => $this->getUserAnalytics($startDate),
            'event_analytics' => $this->getEventAnalytics($startDate),
            'geographic_data' => $this->getGeographicAnalytics($startDate),
            'category_performance' => $this->getCategoryPerformance($startDate),
            'generated_at' => now()->format('Y-m-d H:i:s'),
            'date_range' => $range . ' days',
        ];

        $filename = 'dashboard-report-' . now()->format('Y-m-d') . '.json';

        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Generate specific report
     */
    public function generateReport(Request $request, $type)
    {
        $range = $request->get('range', '30');
        $startDate = Carbon::now()->subDays($range);

        $data = match($type) {
            'revenue' => $this->getRevenueAnalytics($startDate),
            'users' => $this->getUserAnalytics($startDate),
            'events' => $this->getEventAnalytics($startDate),
            'comprehensive' => [
                'platform_stats' => $this->getPlatformStats($startDate),
                'revenue_analytics' => $this->getRevenueAnalytics($startDate),
                'user_analytics' => $this->getUserAnalytics($startDate),
                'event_analytics' => $this->getEventAnalytics($startDate),
                'geographic_data' => $this->getGeographicAnalytics($startDate),
                'category_performance' => $this->getCategoryPerformance($startDate),
            ],
            default => ['error' => 'Invalid report type']
        };

        $filename = $type . '-report-' . now()->format('Y-m-d') . '.json';

        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Get platform analytics API
     */
    public function analytics(Request $request)
    {
        $type = $request->get('type', 'revenue');
        $range = $request->get('range', '30');
        $startDate = Carbon::now()->subDays($range);

        return match($type) {
            'revenue' => response()->json($this->getRevenueAnalytics($startDate)),
            'users' => response()->json($this->getUserAnalytics($startDate)),
            'tickets' => response()->json($this->getEventAnalytics($startDate)),
            'geographic' => response()->json($this->getGeographicAnalytics($startDate)),
            default => response()->json(['error' => 'Invalid analytics type'], 400)
        };
    }
}
