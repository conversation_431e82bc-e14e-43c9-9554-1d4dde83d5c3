@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-primary/5 via-white to-secondary/5">

    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8" data-aos="fade-up">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                    </svg>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Selesaikan Pembayaran
                </h1>
                <p class="text-lg text-gray-600 mb-2">
                    Silakan lakukan pembayaran sesuai instruksi di bawah ini
                </p>
                <p class="text-sm text-gray-500">
                    Order #{{ $order->order_number }} • Berlaku hingga:
                    <span id="countdown" class="font-semibold text-orange-600"></span>
                </p>
            </div>
        </div>
    </section>

    <!-- Payment Details -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Mobile Order Summary (shown at top on mobile) -->
            <div class="lg:hidden mb-6">
                <div class="bg-white rounded-2xl shadow-lg p-4 sm:p-6" data-aos="fade-up">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Ringkasan Pesanan</h3>

                    <!-- Event Info -->
                    <div class="mb-4">
                        <h4 class="font-semibold text-gray-900 mb-2 text-sm sm:text-base">{{ $order->event->title }}</h4>
                        <div class="text-xs sm:text-sm text-gray-600 space-y-1">
                            <p>📅 {{ $order->event->start_date->format('d M Y, H:i') }} WIB</p>
                            <p>📍 {{ $order->event->venue_name }}, {{ $order->event->city }}</p>
                            <p>👤 {{ $order->customer_name }}</p>
                            <p>🎫 {{ $order->quantity }} tiket</p>
                        </div>
                    </div>

                    <!-- Payment Summary -->
                    <div class="space-y-2 mb-4 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal</span>
                            <span>Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Biaya admin</span>
                            <span>Rp {{ number_format($order->admin_fee, 0, ',', '.') }}</span>
                        </div>
                        @if($order->discount_amount > 0)
                            <div class="flex justify-between text-green-600">
                                <span>Diskon</span>
                                <span>-Rp {{ number_format($order->discount_amount, 0, ',', '.') }}</span>
                            </div>
                        @endif
                        <div class="border-t border-gray-200 pt-2">
                            <div class="flex justify-between items-center">
                                <span class="font-bold text-gray-900">Total</span>
                                <span class="font-bold text-primary text-lg">
                                    Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="bg-gray-50 rounded-lg p-3 text-sm">
                        <div class="text-gray-600 mb-1">Metode Pembayaran</div>
                        <div class="font-semibold text-gray-900">
                            {{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Payment Instructions -->
                <div class="lg:col-span-2 space-y-6">

                    @if($order->payment_method === 'qris' && isset($paymentData['qr_string']))
                        <!-- QRIS Payment -->
                        <div class="bg-white rounded-2xl shadow-lg p-4 sm:p-6" data-aos="fade-up">
                            <h2 class="text-lg sm:text-xl font-bold text-gray-900 mb-4 sm:mb-6 flex items-center">
                                <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                                </svg>
                                <span class="text-sm sm:text-lg">Scan QR Code QRIS</span>
                            </h2>

                            <div class="text-center">
                                <!-- Responsive QR Code Container -->
                                <div class="w-48 h-48 sm:w-56 sm:h-56 md:w-64 md:h-64 mx-auto bg-white border-2 border-gray-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
                                    <div id="qrcode" class="w-full h-full flex items-center justify-center"></div>
                                </div>

                                <!-- Mobile-friendly instructions -->
                                <div class="space-y-2 sm:space-y-3 text-xs sm:text-sm text-gray-700">
                                    <p class="flex items-center justify-center flex-wrap">
                                        <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-center">Scan dengan aplikasi bank atau e-wallet apapun</span>
                                    </p>
                                    <p class="flex items-center justify-center flex-wrap">
                                        <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-center">Pembayaran akan dikonfirmasi otomatis</span>
                                    </p>
                                </div>

                                <!-- Mobile payment apps suggestion -->
                                <div class="mt-4 sm:mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg">
                                    <p class="text-xs sm:text-sm text-blue-800 font-medium mb-2">Aplikasi yang mendukung QRIS:</p>
                                    <div class="flex flex-wrap justify-center gap-2 text-xs text-blue-700">
                                        <span class="bg-white px-2 py-1 rounded">GoPay</span>
                                        <span class="bg-white px-2 py-1 rounded">OVO</span>
                                        <span class="bg-white px-2 py-1 rounded">DANA</span>
                                        <span class="bg-white px-2 py-1 rounded">ShopeePay</span>
                                        <span class="bg-white px-2 py-1 rounded">LinkAja</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($order->payment_method === 'virtual_account' && isset($paymentData['account_number']))
                        <!-- Virtual Account Payment -->
                        <div class="bg-white rounded-2xl shadow-lg p-4 sm:p-6" data-aos="fade-up">
                            <h2 class="text-lg sm:text-xl font-bold text-gray-900 mb-4 sm:mb-6 flex items-center">
                                <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                </svg>
                                <span class="text-sm sm:text-lg">Virtual Account {{ strtoupper($paymentData['bank_code']) }}</span>
                            </h2>

                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 sm:p-6">
                                <div class="text-center mb-4 sm:mb-6">
                                    <div class="text-xs sm:text-sm text-blue-700 mb-2">Nomor Virtual Account</div>
                                    <div class="text-lg sm:text-xl md:text-2xl font-mono font-bold text-blue-900 mb-3 sm:mb-4 break-all">
                                        {{ $paymentData['account_number'] }}
                                    </div>
                                    <button onclick="copyToClipboard('{{ $paymentData['account_number'] }}')"
                                            class="bg-blue-600 text-white px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm hover:bg-blue-700 transition-colors w-full sm:w-auto">
                                        <svg class="w-3 h-3 sm:w-4 sm:h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                        Salin Nomor
                                    </button>
                                </div>

                                <div class="space-y-2 sm:space-y-3 text-xs sm:text-sm text-blue-800">
                                    <h4 class="font-semibold text-sm sm:text-base">Cara Pembayaran:</h4>
                                    <ol class="list-decimal list-inside space-y-1 sm:space-y-2 text-left">
                                        <li>Buka aplikasi mobile banking atau kunjungi ATM</li>
                                        <li>Pilih menu Transfer atau Bayar</li>
                                        <li>Masukkan nomor virtual account di atas</li>
                                        <li>Masukkan nominal <strong>Rp {{ number_format($order->total_amount, 0, ',', '.') }}</strong></li>
                                        <li>Konfirmasi pembayaran</li>
                                    </ol>
                                </div>

                                <!-- Mobile banking apps -->
                                <div class="mt-4 sm:mt-6 p-3 sm:p-4 bg-white rounded-lg border border-blue-200">
                                    <p class="text-xs sm:text-sm text-blue-800 font-medium mb-2">Aplikasi Mobile Banking:</p>
                                    <div class="flex flex-wrap gap-2 text-xs">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">BCA Mobile</span>
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">Mandiri Online</span>
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">BRI Mobile</span>
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">BNI Mobile</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($order->payment_method === 'e_wallet' && isset($paymentData['payment_url']))
                        <!-- E-Wallet Payment -->
                        <div class="bg-white rounded-2xl shadow-lg p-4 sm:p-6" data-aos="fade-up">
                            <h2 class="text-lg sm:text-xl font-bold text-gray-900 mb-4 sm:mb-6 flex items-center">
                                <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                </svg>
                                <span class="text-sm sm:text-lg">Pembayaran E-Wallet</span>
                            </h2>

                            <div class="text-center">
                                <div class="bg-green-50 border border-green-200 rounded-xl p-4 sm:p-6 mb-4 sm:mb-6">
                                    <p class="text-green-800 mb-3 sm:mb-4 text-sm sm:text-base">Klik tombol di bawah untuk melanjutkan pembayaran</p>
                                    <a href="{{ $paymentData['payment_url'] }}"
                                       class="inline-flex items-center bg-green-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors text-sm sm:text-base w-full sm:w-auto justify-center">
                                        <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                        </svg>
                                        Bayar Sekarang
                                    </a>
                                </div>

                                @if(isset($paymentData['qr_code']))
                                    <div class="border-t border-gray-200 pt-4 sm:pt-6">
                                        <p class="text-gray-600 mb-3 sm:mb-4 text-sm sm:text-base">Atau scan QR Code di bawah ini:</p>
                                        <div class="w-40 h-40 sm:w-48 sm:h-48 mx-auto bg-white border-2 border-gray-200 rounded-lg p-3 sm:p-4">
                                            <div id="ewallet-qrcode" class="w-full h-full flex items-center justify-center"></div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    @if($order->payment_method === 'bank_transfer' && isset($paymentData['payment_url']))
                        <!-- Bank Transfer via Tripay -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                                Transfer Bank
                            </h2>

                            <div class="text-center">
                                <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
                                    <p class="text-blue-800 mb-4">Klik tombol di bawah untuk melihat detail pembayaran</p>
                                    <a href="{{ $paymentData['payment_url'] }}"
                                       class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                        </svg>
                                        Lihat Detail Pembayaran
                                    </a>
                                </div>

                                @if(isset($paymentData['virtual_account']))
                                    <div class="border-t border-gray-200 pt-6">
                                        <div class="text-sm text-gray-600 mb-2">Nomor Virtual Account</div>
                                        <div class="text-xl font-mono font-bold text-gray-900 mb-4">
                                            {{ $paymentData['virtual_account'] }}
                                        </div>
                                        <button onclick="copyToClipboard('{{ $paymentData['virtual_account'] }}')"
                                                class="text-blue-600 hover:text-blue-700 text-sm">
                                            Salin Nomor
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Payment Status Check -->
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-4 sm:p-6" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-base sm:text-lg font-bold text-yellow-900 mb-3 sm:mb-4 flex items-center">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Status Pembayaran
                        </h3>
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                            <div class="flex-1">
                                <p class="text-yellow-800 text-sm sm:text-base">Menunggu pembayaran...</p>
                                <p class="text-xs sm:text-sm text-yellow-700">Halaman akan otomatis refresh setelah pembayaran berhasil</p>
                            </div>
                            <button onclick="checkPaymentStatus()"
                                    class="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-yellow-700 transition-colors w-full sm:w-auto">
                                Cek Status
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Order Summary (Desktop Only) -->
                <div class="lg:col-span-1 hidden lg:block">
                    <div class="sticky top-24">
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                            <h3 class="text-lg font-bold text-gray-900 mb-4">Ringkasan Pesanan</h3>

                            <!-- Event Info -->
                            <div class="mb-6">
                                <h4 class="font-semibold text-gray-900 mb-2">{{ $order->event->title }}</h4>
                                <div class="text-sm text-gray-600 space-y-1">
                                    <p>📅 {{ $order->event->start_date->format('d M Y, H:i') }} WIB</p>
                                    <p>📍 {{ $order->event->venue_name }}, {{ $order->event->city }}</p>
                                    <p>👤 {{ $order->customer_name }}</p>
                                    <p>🎫 {{ $order->quantity }} tiket</p>
                                </div>
                            </div>

                            <!-- Payment Summary -->
                            <div class="space-y-3 mb-6">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal</span>
                                    <span>Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Biaya admin</span>
                                    <span>Rp {{ number_format($order->admin_fee, 0, ',', '.') }}</span>
                                </div>
                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between text-green-600">
                                        <span>Diskon</span>
                                        <span>-Rp {{ number_format($order->discount_amount, 0, ',', '.') }}</span>
                                    </div>
                                @endif
                                <div class="border-t border-gray-200 pt-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-gray-900">Total</span>
                                        <span class="text-lg font-bold text-primary">
                                            Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <div class="text-sm text-gray-600 mb-1">Metode Pembayaran</div>
                                <div class="font-semibold text-gray-900">
                                    {{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}
                                </div>
                            </div>

                            <!-- Important Notes -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h4 class="font-semibold text-blue-900 mb-2">Penting!</h4>
                                <ul class="text-sm text-blue-800 space-y-1">
                                    <li>• Bayar sesuai nominal yang tertera</li>
                                    <li>• Pembayaran akan dikonfirmasi otomatis</li>
                                    <li>• Tiket akan dikirim via email</li>
                                    <li>• Simpan bukti pembayaran</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script src="{{ asset('js/payment-monitoring.js') }}"></script>
<script>
// Generate QR Code for QRIS with responsive sizing
@if($order->payment_method === 'qris' && isset($paymentData['qr_string']))
    function generateQRISCode() {
        const container = document.getElementById('qrcode');
        if (!container) return;

        // Get container size for responsive QR code
        const containerRect = container.getBoundingClientRect();
        const size = Math.min(containerRect.width, containerRect.height, 256);

        QRCode.toCanvas(container, '{{ $paymentData['qr_string'] }}', {
            width: size,
            height: size,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });
    }

    // Generate on load and resize
    generateQRISCode();
    window.addEventListener('resize', generateQRISCode);
@endif

// Generate QR Code for E-Wallet with responsive sizing
@if($order->payment_method === 'e_wallet' && isset($paymentData['qr_code']))
    function generateEWalletCode() {
        const container = document.getElementById('ewallet-qrcode');
        if (!container) return;

        // Get container size for responsive QR code
        const containerRect = container.getBoundingClientRect();
        const size = Math.min(containerRect.width, containerRect.height, 192);

        QRCode.toCanvas(container, '{{ $paymentData['qr_code'] }}', {
            width: size,
            height: size,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });
    }

    // Generate on load and resize
    generateEWalletCode();
    window.addEventListener('resize', generateEWalletCode);
@endif

// Countdown timer
function startCountdown() {
    const expiresAt = new Date('{{ $order->expires_at }}').getTime();
    const countdownElement = document.getElementById('countdown');

    const timer = setInterval(function() {
        const now = new Date().getTime();
        const distance = expiresAt - now;

        if (distance < 0) {
            clearInterval(timer);
            countdownElement.innerHTML = "EXPIRED";
            countdownElement.classList.add('text-red-600', 'font-bold');
            window.location.reload();
            return;
        }

        const hours = Math.floor(distance / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        let timeString = '';
        if (hours > 0) {
            timeString = hours + "h " + minutes + "m " + seconds + "s";
        } else {
            timeString = minutes + "m " + seconds + "s";
        }

        countdownElement.innerHTML = timeString;

        if (distance < 5 * 60 * 1000) {
            countdownElement.classList.add('text-red-600', 'animate-pulse');
        } else if (distance < 15 * 60 * 1000) {
            countdownElement.classList.add('text-orange-600');
        }
    }, 1000);
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('Nomor berhasil disalin!', 'success');
    });
}

// Check payment status
function checkPaymentStatus() {
    fetch('{{ route("orders.check-status", $order) }}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'paid') {
                window.location.href = '{{ route("orders.success", $order) }}';
            } else {
                showNotification('Pembayaran belum diterima. Silakan coba lagi.', 'info');
            }
        })
        .catch(error => {
            showNotification('Gagal mengecek status pembayaran.', 'error');
        });
}

// Initialize payment monitoring
let paymentMonitor;

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

document.addEventListener('DOMContentLoaded', function() {
    startCountdown();

    // Initialize payment monitoring
    if (window.PaymentMonitor) {
        paymentMonitor = new PaymentMonitor();
        paymentMonitor.startMonitoring('{{ $order->id }}');

        // Show initial notification
        showNotification('🔄 Monitoring pembayaran dimulai. Halaman akan otomatis refresh setelah pembayaran berhasil.', 'info');
    }

    // Add payment status indicator
    updatePaymentStatusIndicator();
});

function updatePaymentStatusIndicator() {
    const statusElement = document.querySelector('.payment-status-indicator');
    if (statusElement) {
        statusElement.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                <span class="text-yellow-800">Menunggu pembayaran...</span>
            </div>
        `;
    }
}

// Enhanced check payment status with sound
function checkPaymentStatus() {
    fetch('{{ route("orders.check-status", $order) }}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'paid') {
                // Play success sound
                if (paymentMonitor) {
                    paymentMonitor.playSuccessSound();
                }

                showNotification('🎉 Pembayaran berhasil! Mengalihkan ke halaman tiket...', 'success');

                setTimeout(() => {
                    window.location.href = '{{ route("orders.success", $order) }}';
                }, 2000);
            } else if (data.status === 'failed' || data.status === 'expired') {
                if (paymentMonitor) {
                    paymentMonitor.playErrorSound();
                }
                showNotification('❌ Pembayaran gagal atau kedaluwarsa.', 'error');
            } else {
                showNotification('⏳ Pembayaran belum diterima. Monitoring akan terus berjalan...', 'info');
            }
        })
        .catch(error => {
            console.error('Error checking payment status:', error);
            showNotification('⚠️ Gagal mengecek status pembayaran.', 'error');
        });
}
</script>
@endpush

@push('styles')
<link rel="stylesheet" href="{{ asset('css/mobile-payment.css') }}">
<style>
/* Mobile-specific improvements for payment page */
@media (max-width: 768px) {
    /* Ensure QR codes are properly sized on mobile */
    #qrcode canvas,
    #ewallet-qrcode canvas {
        max-width: 100% !important;
        height: auto !important;
    }

    /* Better spacing for mobile */
    .payment-container {
        padding: 1rem;
    }

    /* Improve button accessibility on mobile */
    button, .btn {
        min-height: 44px; /* iOS recommended touch target */
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Better text readability on mobile */
    .payment-instructions {
        line-height: 1.6;
    }

    /* Sticky elements adjustment for mobile */
    .sticky {
        position: relative !important;
    }
}

/* Improve countdown visibility */
#countdown {
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Loading state for QR codes */
.qr-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
    border-radius: 8px;
}

.qr-loading::after {
    content: "Memuat QR Code...";
    color: #6b7280;
    font-size: 14px;
}

/* Notification improvements */
.notification {
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Payment method badges */
.payment-method-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    font-weight: 500;
}

/* Responsive grid improvements */
@media (max-width: 1024px) {
    .lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
    }
}
</style>
@endpush
