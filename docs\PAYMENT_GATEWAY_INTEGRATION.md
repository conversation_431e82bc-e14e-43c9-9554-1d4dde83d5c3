# Payment Gateway Integration Documentation

## Overview

This document describes the complete integration of multiple payment gateways (Xendit, Midtrans, and Tripay) into the TiXara application, providing a comprehensive payment solution for Indonesian market.

## Integrated Payment Gateways

### 1. ✅ Xendit Integration
- **E-Wallet**: GoPay, OVO, DANA, LinkAja, ShopeePay
- **QRIS**: Universal QR Code payments
- **Virtual Account**: BCA, Mandiri, BNI, BRI, Permata, CIMB
- **Features**: Real-time webhooks, automatic confirmation

### 2. ✅ Midtrans Integration
- **Credit Card**: Visa, Mastercard, JCB, American Express
- **Features**: 3D Secure, fraud detection, installment options
- **Security**: PCI DSS compliant, tokenization

### 3. ✅ Tripay Integration
- **Bank Transfer**: Manual bank transfer with virtual accounts
- **Features**: Multiple bank support, automatic verification
- **Coverage**: All major Indonesian banks

## Architecture Overview

### Payment Flow
```
User Selects Payment Method
         ↓
PaymentGatewayService determines gateway
         ↓
Gateway-specific API call
         ↓
Payment URL/QR/VA generated
         ↓
User completes payment
         ↓
Webhook receives confirmation
         ↓
Order status updated
         ↓
User receives confirmation
```

### Gateway Selection Logic
```php
private function determineGateway(string $paymentMethod): string
{
    $gatewayMapping = [
        'e_wallet' => 'xendit',      // Xendit for e-wallets
        'qris' => 'xendit',          // Xendit for QRIS
        'virtual_account' => 'xendit', // Xendit for VA
        'credit_card' => 'midtrans',  // Midtrans for Credit Card
        'bank_transfer' => 'tripay',  // Tripay for Bank Transfer
        'cash' => 'manual',          // Manual processing
    ];

    return $gatewayMapping[$paymentMethod] ?? 'manual';
}
```

## Implementation Details

### 1. PaymentGatewayService

**Location**: `app/Services/PaymentGatewayService.php`

**Key Methods**:
- `processPayment()` - Main payment processing method
- `processXenditPayment()` - Xendit-specific processing
- `processMidtransPayment()` - Midtrans-specific processing
- `processTripayPayment()` - Tripay-specific processing

**Example Usage**:
```php
$paymentGateway = new PaymentGatewayService();
$result = $paymentGateway->processPayment($order, 'e_wallet', ['provider' => 'GOPAY']);

if ($result['success']) {
    // Redirect to payment URL or show payment details
    return redirect($result['data']['payment_url']);
}
```

### 2. Webhook Controller

**Location**: `app/Http/Controllers/WebhookController.php`

**Endpoints**:
- `POST /webhooks/xendit` - Xendit webhook handler
- `POST /webhooks/midtrans` - Midtrans webhook handler
- `POST /webhooks/tripay` - Tripay webhook handler

**Security Features**:
- Signature verification for all webhooks
- IP whitelist validation (recommended)
- Duplicate payment prevention

### 3. Payment Details View

**Location**: `resources/views/orders/payment-details.blade.php`

**Features**:
- QR Code generation for QRIS payments
- Virtual account number display
- Payment status checking
- Auto-refresh on payment completion
- Countdown timer for payment expiry

## Configuration

### Environment Variables

Add to your `.env` file:

```env
# Xendit Configuration
XENDIT_SECRET_KEY=your_xendit_secret_key
XENDIT_PUBLIC_KEY=your_xendit_public_key
XENDIT_WEBHOOK_TOKEN=your_webhook_token
XENDIT_ENVIRONMENT=test

# Midtrans Configuration
MIDTRANS_SERVER_KEY=your_midtrans_server_key
MIDTRANS_CLIENT_KEY=your_midtrans_client_key
MIDTRANS_MERCHANT_ID=your_merchant_id
MIDTRANS_ENVIRONMENT=sandbox
MIDTRANS_SANITIZED=true
MIDTRANS_3DS=true

# Tripay Configuration
TRIPAY_API_KEY=your_tripay_api_key
TRIPAY_PRIVATE_KEY=your_tripay_private_key
TRIPAY_MERCHANT_CODE=your_merchant_code
TRIPAY_ENVIRONMENT=sandbox
```

### Service Configuration

**Location**: `config/services.php`

```php
'xendit' => [
    'secret_key' => env('XENDIT_SECRET_KEY'),
    'public_key' => env('XENDIT_PUBLIC_KEY'),
    'webhook_token' => env('XENDIT_WEBHOOK_TOKEN'),
    'environment' => env('XENDIT_ENVIRONMENT', 'test'),
],

'midtrans' => [
    'server_key' => env('MIDTRANS_SERVER_KEY'),
    'client_key' => env('MIDTRANS_CLIENT_KEY'),
    'merchant_id' => env('MIDTRANS_MERCHANT_ID'),
    'environment' => env('MIDTRANS_ENVIRONMENT', 'sandbox'),
    'sanitized' => env('MIDTRANS_SANITIZED', true),
    '3ds' => env('MIDTRANS_3DS', true),
],

'tripay' => [
    'api_key' => env('TRIPAY_API_KEY'),
    'private_key' => env('TRIPAY_PRIVATE_KEY'),
    'merchant_code' => env('TRIPAY_MERCHANT_CODE'),
    'environment' => env('TRIPAY_ENVIRONMENT', 'sandbox'),
],
```

## Payment Method Details

### 1. Xendit E-Wallet
```php
// Create E-Wallet payment
$response = Http::withBasicAuth($this->xenditSecretKey, '')
    ->post('https://api.xendit.co/ewallets/charges', [
        'reference_id' => $order->order_number,
        'currency' => 'IDR',
        'amount' => $order->total_amount,
        'checkout_method' => 'ONE_TIME_PAYMENT',
        'channel_code' => 'GOPAY', // or OVO, DANA, etc.
        'channel_properties' => [
            'success_redirect_url' => route('orders.success', $order),
            'failure_redirect_url' => route('orders.payment', $order),
        ],
    ]);
```

### 2. Xendit QRIS
```php
// Create QRIS payment
$response = Http::withBasicAuth($this->xenditSecretKey, '')
    ->post('https://api.xendit.co/qr_codes', [
        'external_id' => $order->order_number,
        'type' => 'DYNAMIC',
        'callback_url' => route('webhooks.xendit'),
        'amount' => $order->total_amount,
    ]);
```

### 3. Xendit Virtual Account
```php
// Create Virtual Account
$response = Http::withBasicAuth($this->xenditSecretKey, '')
    ->post('https://api.xendit.co/virtual_accounts', [
        'external_id' => $order->order_number,
        'bank_code' => 'BCA',
        'name' => $order->customer_name,
        'expected_amount' => $order->total_amount,
        'expiration_date' => $order->expires_at->toISOString(),
        'is_closed' => true,
    ]);
```

### 4. Midtrans Credit Card
```php
// Process credit card payment
$response = Http::withBasicAuth($this->midtransServerKey, '')
    ->post($midtransUrl . '/charge', [
        'payment_type' => 'credit_card',
        'transaction_details' => [
            'order_id' => $order->order_number,
            'gross_amount' => $order->total_amount,
        ],
        'credit_card' => [
            'token_id' => $paymentDetails['token_id'],
            'authentication' => true,
        ],
    ]);
```

### 5. Tripay Bank Transfer
```php
// Create bank transfer payment
$response = Http::withHeaders([
    'Authorization' => 'Bearer ' . $this->tripayApiKey,
])->post($tripayUrl . '/transaction/create', [
    'method' => 'BCAVA',
    'merchant_ref' => $order->order_number,
    'amount' => $order->total_amount,
    'customer_name' => $order->customer_name,
    'customer_email' => $order->customer_email,
    'signature' => $this->generateTripaySignature($order->order_number, $order->total_amount),
]);
```

## Webhook Handling

### Security Verification

**Xendit Webhook**:
```php
$webhookToken = $request->header('x-callback-token');
if ($webhookToken !== config('services.xendit.webhook_token')) {
    return response()->json(['status' => 'error'], 401);
}
```

**Midtrans Webhook**:
```php
$expectedSignature = hash('sha512', $orderId . $statusCode . $grossAmount . $serverKey);
if ($signatureKey !== $expectedSignature) {
    return response()->json(['status' => 'error'], 401);
}
```

**Tripay Webhook**:
```php
$expectedSignature = hash_hmac('sha256', json_encode($payload), $privateKey);
if ($receivedSignature !== $expectedSignature) {
    return response()->json(['status' => 'error'], 401);
}
```

### Payment Status Updates

All webhooks follow the same pattern:
1. Verify webhook signature
2. Find order by reference ID
3. Update order status
4. Send confirmation notification
5. Return success response

## Frontend Integration

### Payment Details Page Features

**QR Code Generation**:
```javascript
// Generate QR Code for QRIS
QRCode.toCanvas(document.getElementById('qrcode'), qrString, {
    width: 256,
    height: 256,
    margin: 2,
});
```

**Payment Status Checking**:
```javascript
// Auto check payment status every 30 seconds
setInterval(checkPaymentStatus, 30000);

function checkPaymentStatus() {
    fetch('/orders/{order}/check-status')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'paid') {
                window.location.href = '/orders/{order}/success';
            }
        });
}
```

**Copy to Clipboard**:
```javascript
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('Nomor berhasil disalin!', 'success');
    });
}
```

## Testing

### Test Credentials

**Xendit Sandbox**:
- Use test API keys from Xendit dashboard
- Test e-wallet payments with provided test numbers
- QRIS payments can be tested with sandbox QR codes

**Midtrans Sandbox**:
- Use sandbox server/client keys
- Test credit cards: 4811 1111 1111 1114 (success)
- CVV: 123, Expiry: any future date

**Tripay Sandbox**:
- Use sandbox API credentials
- All payments in sandbox are automatically successful
- Test with various bank codes

### Webhook Testing

Use tools like ngrok for local webhook testing:
```bash
ngrok http 8000
# Use the HTTPS URL for webhook endpoints
```

## Security Considerations

### 1. API Key Security
- Store all API keys in environment variables
- Never commit API keys to version control
- Use different keys for staging/production

### 2. Webhook Security
- Always verify webhook signatures
- Implement IP whitelist for webhook endpoints
- Use HTTPS for all webhook URLs

### 3. Payment Data
- Never store sensitive payment data
- Log payment attempts for audit trails
- Implement rate limiting for payment endpoints

### 4. Error Handling
- Graceful error handling for all payment methods
- Proper logging of payment failures
- User-friendly error messages

## Monitoring & Logging

### Payment Logs
```php
Log::info('Payment processing started', [
    'order_id' => $order->id,
    'payment_method' => $paymentMethod,
    'amount' => $order->total_amount,
]);
```

### Webhook Logs
```php
Log::info('Webhook received', [
    'gateway' => 'xendit',
    'event_type' => $payload['event'],
    'order_id' => $order->id,
]);
```

### Error Tracking
- Monitor payment success rates
- Track webhook delivery failures
- Alert on payment processing errors

## Production Deployment

### 1. Environment Setup
- Switch to production API keys
- Update webhook URLs to production domains
- Enable SSL certificates

### 2. Database Considerations
- Index payment-related columns
- Set up database backups
- Monitor payment table growth

### 3. Performance Optimization
- Cache payment method configurations
- Optimize webhook processing
- Implement queue for payment notifications

## Troubleshooting

### Common Issues

**1. Webhook Not Received**:
- Check webhook URL accessibility
- Verify SSL certificate
- Check firewall settings

**2. Payment Status Not Updated**:
- Verify webhook signature validation
- Check database transaction handling
- Review error logs

**3. Payment Gateway Errors**:
- Verify API credentials
- Check request format
- Review gateway documentation

### Debug Tools

**Payment Gateway Logs**:
```php
// Enable detailed logging
Log::channel('payment')->info('Gateway request', [
    'url' => $url,
    'payload' => $payload,
    'response' => $response,
]);
```

**Webhook Testing**:
```bash
# Test webhook endpoint
curl -X POST https://your-domain.com/webhooks/xendit \
  -H "Content-Type: application/json" \
  -H "x-callback-token: your-webhook-token" \
  -d '{"event": "payment.paid", "data": {...}}'
```

## File Structure

```
app/
├── Services/
│   └── PaymentGatewayService.php       ✅ Main payment service
├── Http/Controllers/
│   ├── OrderController.php             ✅ Updated with gateway integration
│   └── WebhookController.php           ✅ Webhook handlers
└── Models/
    └── Order.php                       ✅ Payment-related fields

resources/views/orders/
├── payment.blade.php                   ✅ Payment method selection
├── payment-details.blade.php           ✅ Payment instructions
└── success.blade.php                   ✅ Payment success page

routes/
└── web.php                             ✅ Payment and webhook routes

config/
└── services.php                        ✅ Payment gateway configuration

docs/
└── PAYMENT_GATEWAY_INTEGRATION.md      ✅ This documentation
```

## Next Steps

### 1. Additional Features
- Refund processing
- Partial payments
- Recurring payments
- Payment analytics dashboard

### 2. Enhanced Security
- Payment fraud detection
- Risk scoring
- Transaction monitoring

### 3. User Experience
- Saved payment methods
- One-click payments
- Payment preferences
- Mobile app integration

## Support & Maintenance

### Regular Tasks
- Monitor payment success rates
- Update gateway API versions
- Review security configurations
- Optimize payment flows

### Documentation Updates
- Keep API documentation current
- Update troubleshooting guides
- Maintain test procedures
- Document new features

This comprehensive payment gateway integration provides a robust, secure, and user-friendly payment solution for the TiXara application, supporting all major payment methods used in Indonesia.
