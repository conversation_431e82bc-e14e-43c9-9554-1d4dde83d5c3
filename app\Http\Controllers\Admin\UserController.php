<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Role filter
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'verified') {
                $query->whereNotNull('email_verified_at');
            } elseif ($request->status === 'unverified') {
                $query->whereNull('email_verified_at');
            }
        }

        // Sorting
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'name':
                $query->orderBy('name');
                break;
            case 'email':
                $query->orderBy('email');
                break;
            case 'role':
                $query->orderBy('role');
                break;
            case 'oldest':
                $query->oldest();
                break;
            default:
                $query->latest();
        }

        $users = $query->withCount('organizedEvents')->paginate(10)->withQueryString();

        // Statistics
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'admin_count' => User::where('role', 'admin')->count(),
            'organizer_count' => User::where('role', 'penjual')->count(),
            'buyer_count' => User::where('role', 'pembeli')->count(),
            'staff_count' => User::where('role', 'staff')->count(),
        ];

        return view('pages.admin.users.index', compact('users', 'stats'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        return view('pages.admin.users.create');
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,staff,penjual,pembeli',
            'user_level' => 'nullable|in:star,star_plus,premium,platinum',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'boolean',
            'email_verified' => 'boolean',
        ]);

        $userData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
            'user_level' => $validated['user_level'] ?? 'star',
            'phone' => $validated['phone'],
            'birth_date' => $validated['date_of_birth'],
            'gender' => $validated['gender'],
            'address' => $validated['address'],
            'is_active' => $request->boolean('is_active', true),
        ];

        if ($request->boolean('email_verified')) {
            $userData['email_verified_at'] = now();
        }

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            $userData['avatar'] = $request->file('profile_photo')->store('users/profiles', 'public');
        }

        $user = User::create($userData);

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil dibuat!');
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load(['organizedEvents', 'orders', 'tickets']);

        $statistics = [
            'total_events' => $user->organizedEvents->count(),
            'total_orders' => $user->orders->count(),
            'total_tickets' => $user->tickets->count(),
            'total_spent' => $user->orders->where('status', 'completed')->sum('total_amount'),
            'events_revenue' => $user->organizedEvents->sum(function($event) {
                return $event->tickets->where('status', 'used')->sum('price');
            }),
        ];

        return view('pages.admin.users.show', compact('user', 'statistics'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        return view('pages.admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,staff,penjual,pembeli',
            'user_level' => 'nullable|in:star,star_plus,premium,platinum',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'boolean',
            'email_verified' => 'boolean',
        ]);

        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'role' => $validated['role'],
            'phone' => $validated['phone'],
            'birth_date' => $validated['date_of_birth'],
            'gender' => $validated['gender'],
            'address' => $validated['address'],
            'is_active' => $request->boolean('is_active'),
        ];

        // Update password if provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($validated['password']);
        }

        // Handle email verification
        if ($request->boolean('email_verified') && !$user->email_verified_at) {
            $updateData['email_verified_at'] = now();
        } elseif (!$request->boolean('email_verified') && $user->email_verified_at) {
            $updateData['email_verified_at'] = null;
        }

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old photo if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            $updateData['avatar'] = $request->file('profile_photo')->store('users/profiles', 'public');
        }

        $user->update($updateData);

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil diperbarui!');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        // Prevent deleting the current admin user
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'Anda tidak dapat menghapus akun Anda sendiri!');
        }

        // Check if user has organized tickets with sold tickets
        $hasActiveTickets = $user->organizedTickets()
            ->whereHas('tickets', function($query) {
                $query->where ('status', 'used');
            })->exists();

        if ($hasActiveTickets) {
            return redirect()->back()
                ->with('error', 'Tidak dapat menghapus pengguna yang memiliki event dengan tiket terjual!');
        }

        // Delete profile photo if exists
        if ($user->profile_photo && Storage::disk('public')->exists($user->profile_photo)) {
            Storage::disk('public')->delete($user->profile_photo);
        }

        // Delete related data
        $user->organizedTickets()->delete();
        $user->orders()->delete();
        $user->tickets()->delete();
        $user->notifications()->delete();

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil dihapus!');
    }

    /**
     * Toggle user active status
     */
    public function toggleStatus(User $user)
    {
        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'diaktifkan' : 'dinonaktifkan';

        return redirect()->back()
            ->with('success', "Pengguna berhasil {$status}!");
    }

    /**
     * Verify user email
     */
    public function verifyEmail(User $user)
    {
        $user->update(['email_verified_at' => now()]);

        return redirect()->back()
            ->with('success', 'Email pengguna berhasil diverifikasi!');
    }

    /**
     * Bulk actions for users
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,activate,deactivate,verify,unverify',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $users = User::whereIn('id', $validated['user_ids']);

        // Prevent bulk action on current user
        $currentUserId = auth()->id();
        if (in_array($currentUserId, $validated['user_ids'])) {
            return redirect()->back()
                ->with('error', 'Anda tidak dapat melakukan aksi bulk pada akun Anda sendiri!');
        }

        switch ($validated['action']) {
            case 'delete':
                // Check for users with active events
                $usersWithActiveEvents = $users->whereHas('organizedEvents.tickets', function($query) {
                    $query->where('status', 'used');
                })->count();

                if ($usersWithActiveEvents > 0) {
                    return redirect()->back()
                        ->with('error', 'Beberapa pengguna tidak dapat dihapus karena memiliki event dengan tiket terjual!');
                }

                $users->delete();
                $message = 'Pengguna terpilih berhasil dihapus!';
                break;

            case 'activate':
                $users->update(['is_active' => true]);
                $message = 'Pengguna terpilih berhasil diaktifkan!';
                break;

            case 'deactivate':
                $users->update(['is_active' => false]);
                $message = 'Pengguna terpilih berhasil dinonaktifkan!';
                break;

            case 'verify':
                $users->update(['email_verified_at' => now()]);
                $message = 'Email pengguna terpilih berhasil diverifikasi!';
                break;

            case 'unverify':
                $users->update(['email_verified_at' => null]);
                $message = 'Verifikasi email pengguna terpilih berhasil dibatalkan!';
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
