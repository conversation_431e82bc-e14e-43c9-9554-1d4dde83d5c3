<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VoucherUsage extends Model
{
    use HasFactory;

    protected $fillable = [
        'voucher_id',
        'user_id',
        'order_id',
        'event_id',
        'original_amount',
        'discount_amount',
        'final_amount',
        'tickets_quantity',
        'voucher_snapshot',
        'applied_via',
        'used_at',
    ];

    protected $casts = [
        'original_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'tickets_quantity' => 'integer',
        'voucher_snapshot' => 'array',
        'used_at' => 'datetime',
    ];

    // Relationships
    public function voucher(): BelongsTo
    {
        return $this->belongsTo(Voucher::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    // Helper Methods
    public function getSavingsPercentageAttribute(): float
    {
        if ($this->original_amount <= 0) {
            return 0;
        }
        
        return round(($this->discount_amount / $this->original_amount) * 100, 1);
    }

    public function getFormattedDiscountAttribute(): string
    {
        return 'Rp ' . number_format($this->discount_amount, 0, ',', '.');
    }

    public function getFormattedOriginalAmountAttribute(): string
    {
        return 'Rp ' . number_format($this->original_amount, 0, ',', '.');
    }

    public function getFormattedFinalAmountAttribute(): string
    {
        return 'Rp ' . number_format($this->final_amount, 0, ',', '.');
    }
}
