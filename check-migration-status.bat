@echo off
echo Checking Migration Status...

echo.
echo [1/4] Checking migration status...
php artisan migrate:status

echo.
echo [2/4] Checking if notifications table exists...
php artisan tinker --execute="
try {
    \$exists = \Illuminate\Support\Facades\Schema::hasTable('notifications');
    echo 'Notifications table exists: ' . (\$exists ? 'YES' : 'NO') . PHP_EOL;
    
    if (\$exists) {
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('notifications');
        echo 'Columns in notifications table: ' . implode(', ', \$columns) . PHP_EOL;
        
        \$hasUserId = \Illuminate\Support\Facades\Schema::hasColumn('notifications', 'user_id');
        echo 'Has user_id column: ' . (\$hasUserId ? 'YES' : 'NO') . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [3/4] Checking database connection...
php artisan tinker --execute="
try {
    \$connection = \Illuminate\Support\Facades\DB::connection();
    \$pdo = \$connection->getPdo();
    echo 'Database connection: OK' . PHP_EOL;
    echo 'Database name: ' . \$connection->getDatabaseName() . PHP_EOL;
} catch (Exception \$e) {
    echo 'Database connection error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [4/4] Checking for Laravel's built-in notifications table...
php artisan tinker --execute="
try {
    \$exists = \Illuminate\Support\Facades\Schema::hasTable('notifications');
    if (\$exists) {
        \$columns = \Illuminate\Support\Facades\Schema::getColumnListing('notifications');
        echo 'Built-in notifications table columns: ' . implode(', ', \$columns) . PHP_EOL;
    } else {
        echo 'No built-in notifications table found' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error checking built-in notifications: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo ========================================
echo Migration status check completed!
echo ========================================
pause
