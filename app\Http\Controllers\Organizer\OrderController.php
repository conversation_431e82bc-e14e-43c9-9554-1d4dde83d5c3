<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $query = Order::with(['user', 'event'])
            ->whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            });

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by event
        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->latest()->paginate(15);

        // Get organizer's events for filter dropdown
        $events = Event::where('user_id', Auth::id())
            ->select('id', 'title')
            ->get();

        // Statistics
        $stats = [
            'total_orders' => Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })->count(),
            'completed_orders' => Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })->where('status', 'completed')->count(),
            'pending_orders' => Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })->where('status', 'pending')->count(),
            'total_revenue' => Order::whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            })->where('status', 'completed')->sum('total_amount'),
        ];

        return view('pages.organizer.orders.index', compact('orders', 'events', 'stats'));
    }

    public function show(Order $order)
    {
        // Ensure the order belongs to organizer's event
        if ($order->event->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this order.');
        }

        $order->load(['user', 'event', 'payment']);

        return view('pages.organizer.orders.show', compact('order'));
    }

    public function updateStatus(Request $request, Order $order)
    {
        // Ensure the order belongs to organizer's event
        if ($order->event->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this order.');
        }

        $request->validate([
            'status' => 'required|in:pending,completed,cancelled,refunded',
            'notes' => 'nullable|string|max:500'
        ]);

        $order->update([
            'status' => $request->status,
            'notes' => $request->notes
        ]);

        return redirect()->route('organizer.orders.show', $order)
            ->with('success', 'Order status updated successfully.');
    }

    public function export(Request $request)
    {
        $query = Order::with(['user', 'event'])
            ->whereHas('event', function($q) {
                $q->where('user_id', Auth::id());
            });

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->get();

        $csvData = [];
        $csvData[] = [
            'Order Number',
            'Customer Name',
            'Customer Email',
            'Event Title',
            'Quantity',
            'Total Amount',
            'Status',
            'Order Date',
            'Payment Method'
        ];

        foreach ($orders as $order) {
            $csvData[] = [
                $order->order_number,
                $order->user->name,
                $order->user->email,
                $order->event->title,
                $order->quantity,
                'Rp ' . number_format($order->total_amount, 0, ',', '.'),
                ucfirst($order->status),
                $order->created_at->format('Y-m-d H:i:s'),
                $order->payment->payment_method ?? 'N/A'
            ];
        }

        $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
