@props([
    'variant' => 'default', // default, success, warning, error, info, primary, secondary
    'size' => 'md', // xs, sm, md, lg
    'icon' => null,
    'dot' => false,
    'removable' => false,
    'href' => null
])

@php
    $variants = [
        'default' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        'primary' => 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
        'secondary' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        'success' => 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
        'warning' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
        'error' => 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
        'info' => 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
    ];
    
    $sizes = [
        'xs' => 'px-2 py-0.5 text-xs',
        'sm' => 'px-2.5 py-0.5 text-xs',
        'md' => 'px-3 py-1 text-sm',
        'lg' => 'px-4 py-1.5 text-base'
    ];
    
    $dotColors = [
        'default' => 'bg-gray-400',
        'primary' => 'bg-blue-500',
        'secondary' => 'bg-gray-400',
        'success' => 'bg-green-500',
        'warning' => 'bg-yellow-500',
        'error' => 'bg-red-500',
        'info' => 'bg-blue-500'
    ];
    
    $variantClass = $variants[$variant] ?? $variants['default'];
    $sizeClass = $sizes[$size] ?? $sizes['md'];
    $dotColor = $dotColors[$variant] ?? $dotColors['default'];
    
    $classes = collect([
        'inline-flex items-center',
        $sizeClass,
        $variantClass,
        'font-medium rounded-full',
        'transition-colors duration-200'
    ])->implode(' ');
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes . ' hover:opacity-80']) }}>
        @if($dot)
            <div class="w-2 h-2 {{ $dotColor }} rounded-full mr-2"></div>
        @endif
        
        @if($icon)
            <i data-lucide="{{ $icon }}" class="w-3 h-3 {{ $dot || $slot->isNotEmpty() ? 'mr-1' : '' }}"></i>
        @endif
        
        {{ $slot }}
        
        @if($removable)
            <button type="button" class="ml-1 -mr-1 p-0.5 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors duration-200">
                <i data-lucide="x" class="w-3 h-3"></i>
            </button>
        @endif
    </a>
@else
    <span {{ $attributes->merge(['class' => $classes]) }}>
        @if($dot)
            <div class="w-2 h-2 {{ $dotColor }} rounded-full mr-2"></div>
        @endif
        
        @if($icon)
            <i data-lucide="{{ $icon }}" class="w-3 h-3 {{ $dot || $slot->isNotEmpty() ? 'mr-1' : '' }}"></i>
        @endif
        
        {{ $slot }}
        
        @if($removable)
            <button type="button" class="ml-1 -mr-1 p-0.5 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors duration-200">
                <i data-lucide="x" class="w-3 h-3"></i>
            </button>
        @endif
    </span>
@endif
