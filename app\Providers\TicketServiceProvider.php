<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\TicketService;
use App\Services\QRCodeService;
use App\Services\TicketValidationService;

class TicketServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register TicketService
        $this->app->singleton(TicketService::class, function ($app) {
            return new TicketService();
        });

        // Register QRCodeService
        $this->app->singleton(QRCodeService::class, function ($app) {
            return new QRCodeService();
        });

        // Register TicketValidationService
        $this->app->singleton(TicketValidationService::class, function ($app) {
            return new TicketValidationService();
        });

        // Bind interfaces to implementations if needed
        // $this->app->bind(TicketServiceInterface::class, TicketService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Boot logic for ticket services
        // You can add event listeners, view composers, etc. here
        
        // Example: Register ticket-related event listeners
        // Event::listen(TicketPurchased::class, SendTicketEmail::class);
        
        // Example: Register view composers for ticket-related views
        // View::composer('tickets.*', TicketViewComposer::class);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            TicketService::class,
            QRCodeService::class,
            TicketValidationService::class,
        ];
    }
}
