<?php

/**
 * Feedback Controller
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Feedback;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FeedbackController extends Controller
{
    /**
     * Show feedback page
     */
    public function index()
    {
        return view('pages.feedback.index');
    }

    /**
     * Submit feedback
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:suggestion,complaint,compliment,feature_request,bug_report,other',
            'category' => 'required|string|in:ui_ux,performance,feature,content,service,technical,other',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'rating' => 'nullable|integer|min:1|max:5',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'is_anonymous' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $feedbackData = [
                'feedback_number' => $this->generateFeedbackNumber(),
                'user_id' => $request->is_anonymous ? null : Auth::id(),
                'type' => $request->type,
                'category' => $request->category,
                'title' => $request->title,
                'message' => $request->message,
                'rating' => $request->rating,
                'contact_email' => $request->contact_email ?? (Auth::check() && !$request->is_anonymous ? Auth::user()->email : null),
                'contact_phone' => $request->contact_phone,
                'is_anonymous' => $request->boolean('is_anonymous'),
                'status' => 'pending',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];

            $feedback = Feedback::create($feedbackData);

            // Log the feedback
            \Log::info('New feedback submitted', [
                'feedback_id' => $feedback->id,
                'feedback_number' => $feedback->feedback_number,
                'type' => $feedback->type,
                'category' => $feedback->category,
                'rating' => $feedback->rating,
                'is_anonymous' => $feedback->is_anonymous,
                'user_id' => $feedback->user_id,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Terima kasih atas feedback Anda! Nomor feedback: ' . $feedback->feedback_number,
                'feedback_number' => $feedback->feedback_number
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to submit feedback', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengirim feedback. Silakan coba lagi.'
            ], 500);
        }
    }

    /**
     * Show user's feedback
     */
    public function myFeedback()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('message', 'Silakan login untuk melihat feedback Anda.');
        }

        $feedbacks = Feedback::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('pages.feedback.my-feedback', compact('feedbacks'));
    }

    /**
     * Show specific feedback
     */
    public function show(Feedback $feedback)
    {
        // Check if user can view this feedback
        if (!Auth::check() || (Auth::id() !== $feedback->user_id && !Auth::user()->isAdmin())) {
            abort(403, 'Anda tidak memiliki akses ke feedback ini.');
        }

        return view('pages.feedback.show', compact('feedback'));
    }

    /**
     * Generate unique feedback number
     */
    private function generateFeedbackNumber(): string
    {
        do {
            $feedbackNumber = 'FBK-' . date('Ymd') . '-' . strtoupper(\Str::random(6));
        } while (Feedback::where('feedback_number', $feedbackNumber)->exists());

        return $feedbackNumber;
    }
}
