@echo off
echo Diagnosing Database Structure Issues for TiXara...

echo.
echo [1/5] Checking database connection...
php artisan tinker --execute="
try {
    \$connection = \Illuminate\Support\Facades\DB::connection();
    echo 'Database: ' . \$connection->getDatabaseName() . PHP_EOL;
    echo 'Connection: SUCCESS' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Connection: FAILED - ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"

echo.
echo [2/5] Checking current tables...
php artisan tinker --execute="
try {
    echo 'Current tables in database:' . PHP_EOL;
    \$tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');
    
    \$tableNames = [];
    foreach (\$tables as \$table) {
        \$tableName = array_values((array)\$table)[0];
        \$tableNames[] = \$tableName;
        echo '- ' . \$tableName . PHP_EOL;
    }
    
    echo PHP_EOL . 'Total tables: ' . count(\$tableNames) . PHP_EOL;
    
    // Check for problematic tables
    \$requiredTables = ['users', 'categories', 'events', 'orders', 'tickets'];
    \$existingTables = array_intersect(\$requiredTables, \$tableNames);
    \$missingTables = array_diff(\$requiredTables, \$tableNames);
    
    echo PHP_EOL . 'Required tables status:' . PHP_EOL;
    foreach (\$requiredTables as \$table) {
        if (in_array(\$table, \$tableNames)) {
            echo '✓ ' . \$table . ' EXISTS' . PHP_EOL;
        } else {
            echo '✗ ' . \$table . ' MISSING' . PHP_EOL;
        }
    }
    
} catch (Exception \$e) {
    echo 'Error checking tables: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [3/5] Checking table structures...
php artisan tinker --execute="
try {
    echo 'Checking table structures:' . PHP_EOL;
    
    // Check tickets table structure if exists
    if (\Illuminate\Support\Facades\Schema::hasTable('tickets')) {
        echo PHP_EOL . 'TICKETS table structure:' . PHP_EOL;
        \$ticketColumns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
        foreach (\$ticketColumns as \$column) {
            echo '  - ' . \$column . PHP_EOL;
        }
        
        // Check if it has event columns (wrong structure)
        \$eventColumns = ['title', 'description', 'venue_name', 'start_date', 'end_date'];
        \$hasEventColumns = count(array_intersect(\$eventColumns, \$ticketColumns)) > 0;
        
        if (\$hasEventColumns) {
            echo '  WARNING: tickets table contains event columns!' . PHP_EOL;
            echo '  This table should be renamed to events!' . PHP_EOL;
        } else {
            echo '  OK: tickets table has correct structure' . PHP_EOL;
        }
    }
    
    // Check events table
    if (\Illuminate\Support\Facades\Schema::hasTable('events')) {
        echo PHP_EOL . 'EVENTS table structure:' . PHP_EOL;
        \$eventColumns = \Illuminate\Support\Facades\Schema::getColumnListing('events');
        foreach (\$eventColumns as \$column) {
            echo '  - ' . \$column . PHP_EOL;
        }
    } else {
        echo PHP_EOL . 'EVENTS table: MISSING' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Error checking structures: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [4/5] Checking migration status...
php artisan migrate:status

echo.
echo [5/5] Generating diagnosis report...
php artisan tinker --execute="
try {
    echo PHP_EOL . '========================================' . PHP_EOL;
    echo 'DATABASE DIAGNOSIS REPORT' . PHP_EOL;
    echo '========================================' . PHP_EOL;
    
    \$tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');
    \$tableNames = array_map(function(\$table) {
        return array_values((array)\$table)[0];
    }, \$tables);
    
    \$hasUsers = in_array('users', \$tableNames);
    \$hasCategories = in_array('categories', \$tableNames);
    \$hasEvents = in_array('events', \$tableNames);
    \$hasOrders = in_array('orders', \$tableNames);
    \$hasTickets = in_array('tickets', \$tableNames);
    
    echo 'CURRENT STATE:' . PHP_EOL;
    echo '- Users: ' . (\$hasUsers ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Categories: ' . (\$hasCategories ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Events: ' . (\$hasEvents ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Orders: ' . (\$hasOrders ? 'EXISTS' : 'MISSING') . PHP_EOL;
    echo '- Tickets: ' . (\$hasTickets ? 'EXISTS' : 'MISSING') . PHP_EOL;
    
    echo PHP_EOL . 'PROBLEMS IDENTIFIED:' . PHP_EOL;
    
    if (!\$hasEvents && \$hasTickets) {
        // Check if tickets table is actually events table
        \$ticketColumns = \Illuminate\Support\Facades\Schema::getColumnListing('tickets');
        \$eventColumns = ['title', 'description', 'venue_name', 'start_date', 'end_date'];
        \$hasEventColumns = count(array_intersect(\$eventColumns, \$ticketColumns)) > 0;
        
        if (\$hasEventColumns) {
            echo '1. CRITICAL: tickets table contains event data (should be renamed to events)' . PHP_EOL;
            echo '2. CRITICAL: Real tickets table is missing' . PHP_EOL;
            echo PHP_EOL . 'RECOMMENDED ACTION: Run fix-database-structure-complete.bat' . PHP_EOL;
        } else {
            echo '1. CRITICAL: events table is missing' . PHP_EOL;
            echo '2. INFO: tickets table exists with correct structure' . PHP_EOL;
            echo PHP_EOL . 'RECOMMENDED ACTION: Run create-missing-events-table.bat' . PHP_EOL;
        }
    } elseif (!\$hasEvents && !\$hasTickets) {
        echo '1. CRITICAL: Both events and tickets tables are missing' . PHP_EOL;
        echo PHP_EOL . 'RECOMMENDED ACTION: Run fresh-migrate.bat' . PHP_EOL;
    } elseif (\$hasEvents && \$hasTickets) {
        echo '1. INFO: Both tables exist - check for structure conflicts' . PHP_EOL;
        echo PHP_EOL . 'RECOMMENDED ACTION: Run verify-table-structures.bat' . PHP_EOL;
    } else {
        echo '1. WARNING: Partial table structure detected' . PHP_EOL;
        echo PHP_EOL . 'RECOMMENDED ACTION: Run complete-migration.bat' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Diagnosis error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
pause
