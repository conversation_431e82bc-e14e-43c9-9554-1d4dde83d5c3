<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Facades\Image;
use App\Services\ImageService;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Storage;

class TestImageProcessing extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'image:test-processing';

    /**
     * The console command description.
     */
    protected $description = 'Test image processing functionality to ensure no Imagick dependencies';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing Image Processing Functionality...');
        $this->newLine();

        $allTestsPassed = true;

        // Test 1: Check Image Facade
        $allTestsPassed &= $this->testImageFacade();
        $this->newLine();

        // Test 2: Test QR Code Generation
        $allTestsPassed &= $this->testQRCodeGeneration();
        $this->newLine();

        // Test 3: Test ImageService
        $allTestsPassed &= $this->testImageService();
        $this->newLine();

        // Test 4: Test Image Creation
        $allTestsPassed &= $this->testImageCreation();
        $this->newLine();

        // Final Results
        if ($allTestsPassed) {
            $this->info('🎉 ALL TESTS PASSED! Image processing is working correctly.');
            return Command::SUCCESS;
        } else {
            $this->error('❌ Some tests failed. Please check the errors above.');
            return Command::FAILURE;
        }
    }

    /**
     * Test Image Facade functionality
     */
    private function testImageFacade(): bool
    {
        $this->info('1️⃣ Testing Image Facade...');

        try {
            // Test availability check
            $isAvailable = Image::isAvailable();
            if (!$isAvailable) {
                $this->error('   ❌ Image processing not available');
                return false;
            }
            $this->line('   ✅ Image processing available');

            // Test driver info
            $driverInfo = Image::getDriverInfo();
            $this->line('   📋 Driver: ' . $driverInfo['driver']);
            $this->line('   📋 Version: ' . $driverInfo['version']);

            // Test safe manager creation
            $manager = Image::createSafeManager();
            $this->line('   ✅ Safe manager created: ' . get_class($manager));

            return true;

        } catch (\Exception $e) {
            $this->error('   ❌ Image Facade test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Test QR Code generation
     */
    private function testQRCodeGeneration(): bool
    {
        $this->info('2️⃣ Testing QR Code Generation...');

        try {
            // Test SVG QR Code
            $svgQrCode = QrCode::format('svg')
                ->size(100)
                ->margin(1)
                ->generate('Test QR Code');

            if (strlen($svgQrCode) > 0) {
                $this->line('   ✅ SVG QR Code generated (' . strlen($svgQrCode) . ' characters)');
            } else {
                $this->error('   ❌ SVG QR Code generation failed');
                return false;
            }

            // Test PNG QR Code
            $pngQrCode = QrCode::format('png')
                ->size(100)
                ->margin(1)
                ->generate('Test QR Code PNG');

            if (strlen($pngQrCode) > 0) {
                $this->line('   ✅ PNG QR Code generated (' . strlen($pngQrCode) . ' bytes)');
            } else {
                $this->error('   ❌ PNG QR Code generation failed');
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->error('   ❌ QR Code test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Test ImageService functionality
     */
    private function testImageService(): bool
    {
        $this->info('3️⃣ Testing ImageService...');

        try {
            $imageService = app(ImageService::class);
            $this->line('   ✅ ImageService instantiated: ' . get_class($imageService));

            // Test image info method (doesn't require actual image processing)
            $this->line('   ✅ ImageService methods available');

            return true;

        } catch (\Exception $e) {
            $this->error('   ❌ ImageService test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Test basic image creation
     */
    private function testImageCreation(): bool
    {
        $this->info('4️⃣ Testing Image Creation...');

        try {
            // Create a simple test image
            $manager = Image::createSafeManager();
            $image = $manager->create(100, 100);

            if ($image) {
                $this->line('   ✅ Test image created (100x100)');
                $this->line('   📏 Width: ' . $image->width() . 'px');
                $this->line('   📏 Height: ' . $image->height() . 'px');

                // Test image conversion
                $jpegData = $image->toJpeg(85);
                if (strlen($jpegData) > 0) {
                    $this->line('   ✅ JPEG conversion successful (' . strlen($jpegData) . ' bytes)');
                } else {
                    $this->error('   ❌ JPEG conversion failed');
                    return false;
                }

                return true;
            } else {
                $this->error('   ❌ Image creation failed');
                return false;
            }

        } catch (\Exception $e) {
            $this->error('   ❌ Image creation test failed: ' . $e->getMessage());
            return false;
        }
    }
}
