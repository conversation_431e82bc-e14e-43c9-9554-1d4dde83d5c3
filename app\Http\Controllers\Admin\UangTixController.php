<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UangTixBalance;
use App\Models\UangTixTransaction;
use App\Models\UangTixRequest;
use App\Models\UangTixExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UangTixController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display UangTix overview
     */
    public function index(Request $request)
    {
        $query = UangTixBalance::with(['user'])
            ->where(function($q) {
                $q->where('balance', '>', 0)
                  ->orWhere('total_earned', '>', 0)
                  ->orWhere('total_spent', '>', 0)
                  ->orWhere('total_deposited', '>', 0)
                  ->orWhere('total_withdrawn', '>', 0);
            });

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by balance range
        if ($request->filled('min_balance')) {
            $query->where('balance', '>=', $request->min_balance);
        }
        if ($request->filled('max_balance')) {
            $query->where('balance', '<=', $request->max_balance);
        }

        $balances = $query->orderBy('balance', 'desc')->paginate(20);

        // Statistics
        $stats = [
            'total_balances' => UangTixBalance::sum('balance'),
            'total_users' => UangTixBalance::where('balance', '>', 0)->count(),
            'total_transactions_today' => UangTixTransaction::today()->count(),
            'total_volume_today' => UangTixTransaction::today()->sum(DB::raw('ABS(amount)')),
            'pending_deposits' => UangTixRequest::pending()->deposits()->count(),
            'pending_withdrawals' => UangTixRequest::pending()->withdrawals()->count(),
        ];

        $exchangeRate = UangTixExchangeRate::current();

        return view('pages.admin.uangtix.index', compact('balances', 'stats', 'exchangeRate'));
    }

    /**
     * Display UangTix settings
     */
    public function settings()
    {
        $exchangeRate = UangTixExchangeRate::current();
        
        return view('pages.admin.uangtix.settings', compact('exchangeRate'));
    }

    /**
     * Update UangTix settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'rate_idr_to_uangtix' => 'required|numeric|min:0.0001|max:999999',
            'rate_uangtix_to_idr' => 'required|numeric|min:0.0001|max:999999',
            'min_deposit_idr' => 'required|numeric|min:1000',
            'max_deposit_idr' => 'required|numeric|min:10000',
            'min_withdrawal_uangtix' => 'required|numeric|min:1',
            'deposit_fee_percentage' => 'required|numeric|min:0|max:100',
            'withdrawal_fee_percentage' => 'required|numeric|min:0|max:100',
            'deposits_enabled' => 'boolean',
            'withdrawals_enabled' => 'boolean',
            'transfers_enabled' => 'boolean',
            'terms_and_conditions' => 'nullable|string',
        ]);

        try {
            UangTixExchangeRate::updateRate($request->all());

            return redirect()->back()->with('success', 'Pengaturan UangTix berhasil diperbarui!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal memperbarui pengaturan: ' . $e->getMessage());
        }
    }

    /**
     * Display deposit/withdrawal requests
     */
    public function requests(Request $request)
    {
        $query = UangTixRequest::with(['user', 'processedBy']);

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(20);

        // Statistics
        $stats = [
            'pending_count' => UangTixRequest::pending()->count(),
            'pending_deposits' => UangTixRequest::pending()->deposits()->count(),
            'pending_withdrawals' => UangTixRequest::pending()->withdrawals()->count(),
            'completed_today' => UangTixRequest::where('status', 'completed')->whereDate('processed_at', today())->count(),
        ];

        return view('pages.admin.uangtix.requests', compact('requests', 'stats'));
    }

    /**
     * Approve request
     */
    public function approveRequest(Request $request, UangTixRequest $uangTixRequest)
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:500'
        ]);

        if (!$uangTixRequest->isPending()) {
            return response()->json([
                'success' => false,
                'message' => 'Permintaan tidak dapat disetujui'
            ], 400);
        }

        $success = $uangTixRequest->approve(auth()->user(), $request->admin_notes);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Permintaan berhasil disetujui dan diproses'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Gagal menyetujui permintaan'
        ], 500);
    }

    /**
     * Reject request
     */
    public function rejectRequest(Request $request, UangTixRequest $uangTixRequest)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        if (!$uangTixRequest->isPending()) {
            return response()->json([
                'success' => false,
                'message' => 'Permintaan tidak dapat ditolak'
            ], 400);
        }

        $success = $uangTixRequest->reject(auth()->user(), $request->rejection_reason);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Permintaan berhasil ditolak'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Gagal menolak permintaan'
        ], 500);
    }

    /**
     * Bulk approve requests
     */
    public function bulkApproveRequests(Request $request)
    {
        $request->validate([
            'request_ids' => 'required|array',
            'request_ids.*' => 'exists:uangtix_requests,id',
            'admin_notes' => 'nullable|string|max:500'
        ]);

        $requests = UangTixRequest::whereIn('id', $request->request_ids)
            ->where('status', UangTixRequest::STATUS_PENDING)
            ->get();

        $approved = 0;
        foreach ($requests as $uangTixRequest) {
            if ($uangTixRequest->approve(auth()->user(), $request->admin_notes)) {
                $approved++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$approved} permintaan berhasil disetujui"
        ]);
    }

    /**
     * Adjust user UangTix balance
     */
    public function adjustBalance(Request $request, User $user)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:add,deduct',
            'description' => 'required|string|max:255'
        ]);

        $amount = abs($request->amount);
        $userBalance = $user->getUangTixBalance();

        try {
            if ($request->type === 'add') {
                $transaction = $userBalance->addBalance($amount, 'admin_add', [
                    'admin_id' => auth()->id(),
                    'reason' => $request->description
                ]);
            } else {
                $transaction = $userBalance->deductBalance($amount, 'admin_deduct', [
                    'admin_id' => auth()->id(),
                    'reason' => $request->description
                ]);
            }

            // Create notification
            \App\Models\Notification::create([
                'user_id' => $user->id,
                'title' => 'Penyesuaian Saldo UangTix',
                'message' => "Saldo UangTix Anda telah disesuaikan sebesar {$transaction->formatted_amount}. {$request->description}",
                'type' => 'uangtix',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Saldo UangTix berhasil disesuaikan',
                'new_balance' => $userBalance->fresh()->formatted_balance
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Display transactions
     */
    public function transactions(Request $request)
    {
        $query = UangTixTransaction::with(['user', 'admin', 'fromUser', 'toUser']);

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transaction_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('pages.admin.uangtix.transactions', compact('transactions'));
    }

    /**
     * Toggle balance status (activate/deactivate)
     */
    public function toggleBalanceStatus(Request $request, User $user)
    {
        $request->validate([
            'is_active' => 'required|boolean'
        ]);

        try {
            $balance = $user->getUangTixBalance();
            $balance->update(['is_active' => $request->is_active]);

            // Create notification
            \App\Models\Notification::create([
                'user_id' => $user->id,
                'title' => 'Status Akun UangTix Diubah',
                'message' => $request->is_active
                    ? 'Akun UangTix Anda telah diaktifkan kembali.'
                    : 'Akun UangTix Anda telah dinonaktifkan sementara.',
                'type' => 'uangtix',
                'data' => [
                    'is_active' => $request->is_active,
                    'admin_id' => auth()->id()
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => $request->is_active
                    ? 'Akun UangTix berhasil diaktifkan'
                    : 'Akun UangTix berhasil dinonaktifkan',
                'is_active' => $request->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengubah status akun: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Export UangTix data
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'balances');
        $format = $request->get('format', 'json');

        switch ($type) {
            case 'balances':
                $data = UangTixBalance::with(['user'])
                    ->get()
                    ->map(function($balance) {
                        return [
                            'user_name' => $balance->user->name,
                            'user_email' => $balance->user->email,
                            'balance' => $balance->balance,
                            'total_earned' => $balance->total_earned,
                            'total_spent' => $balance->total_spent,
                            'total_deposited' => $balance->total_deposited,
                            'total_withdrawn' => $balance->total_withdrawn,
                            'is_active' => $balance->is_active,
                            'created_at' => $balance->created_at->format('Y-m-d H:i:s'),
                        ];
                    });
                $filename = 'uangtix_balances_' . now()->format('Y-m-d_H-i-s');
                break;

            case 'transactions':
                $data = UangTixTransaction::with(['user'])
                    ->get()
                    ->map(function($transaction) {
                        return [
                            'transaction_number' => $transaction->transaction_number,
                            'user_name' => $transaction->user->name,
                            'user_email' => $transaction->user->email,
                            'type' => $transaction->type,
                            'amount' => $transaction->amount,
                            'balance_before' => $transaction->balance_before,
                            'balance_after' => $transaction->balance_after,
                            'status' => $transaction->status,
                            'description' => $transaction->description,
                            'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                        ];
                    });
                $filename = 'uangtix_transactions_' . now()->format('Y-m-d_H-i-s');
                break;

            default:
                return response()->json(['error' => 'Invalid export type'], 400);
        }

        if ($format === 'csv') {
            return $this->exportToCsv($data, $filename . '.csv');
        }

        return response()->json([
            'success' => true,
            'data' => $data,
            'filename' => $filename . '.json'
        ]);
    }

    /**
     * Export data to CSV format
     */
    private function exportToCsv($data, $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            if (!empty($data)) {
                fputcsv($file, array_keys($data[0]));

                // Add data rows
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
