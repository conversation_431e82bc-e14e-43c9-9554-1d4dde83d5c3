<?php

/**
 * Contact Controller
 * 
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 * 
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 * 
 * All rights reserved.
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Show contact page
     */
    public function index()
    {
        return view('pages.contact');
    }

    /**
     * Send contact message
     */
    public function send(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'Mohon periksa kembali data yang Anda masukkan.');
        }

        try {
            // In real implementation, send email to admin
            // Mail::to('<EMAIL>')->send(new ContactMessage($request->all()));
            
            // For now, just log the message
            \Log::info('Contact message received', [
                'name' => $request->name,
                'email' => $request->email,
                'subject' => $request->subject,
                'message' => $request->message,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return back()->with('success', 'Terima kasih! Pesan Anda telah terkirim. Tim kami akan menghubungi Anda dalam 24 jam.');
            
        } catch (\Exception $e) {
            \Log::error('Failed to send contact message', [
                'error' => $e->getMessage(),
                'email' => $request->email,
            ]);

            return back()
                ->withInput()
                ->with('error', 'Maaf, terjadi kesalahan saat mengirim pesan. Silakan coba lagi atau hubungi kami langsung.');
        }
    }

    /**
     * Get contact information API
     */
    public function getContactInfo()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'email' => [
                    'support' => '<EMAIL>',
                    'info' => '<EMAIL>',
                    'developer' => '<EMAIL>'
                ],
                'phone' => [
                    'support' => '+62 812-3456-7890',
                    'whatsapp' => '+62 812-3456-7890'
                ],
                'address' => [
                    'street' => 'Jl. Teknologi No. 123',
                    'city' => 'Jakarta Selatan',
                    'postal_code' => '12345',
                    'country' => 'Indonesia'
                ],
                'social_media' => [
                    'instagram' => 'https://instagram.com/tixara.official',
                    'facebook' => 'https://facebook.com/tixara.official',
                    'twitter' => 'https://twitter.com/tixara_official',
                    'linkedin' => 'https://linkedin.com/company/tixara'
                ],
                'business_hours' => [
                    'monday_friday' => '09:00 - 18:00 WIB',
                    'saturday' => '09:00 - 15:00 WIB',
                    'sunday' => 'Closed',
                    'support' => '24/7'
                ]
            ]
        ]);
    }

    /**
     * Submit feedback
     */
    public function feedback(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'feedback' => 'required|string|max:1000',
            'category' => 'required|string|in:general,technical,ui_ux,feature_request,bug_report',
            'email' => 'nullable|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Log feedback
            \Log::info('User feedback received', [
                'rating' => $request->rating,
                'category' => $request->category,
                'feedback' => $request->feedback,
                'email' => $request->email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Terima kasih atas feedback Anda! Masukan Anda sangat berharga untuk pengembangan TiXara.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to save feedback', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan feedback. Silakan coba lagi.'
            ], 500);
        }
    }

    /**
     * Get FAQ data
     */
    public function getFaq()
    {
        $faqs = [
            [
                'category' => 'Umum',
                'questions' => [
                    [
                        'question' => 'Apa itu TiXara?',
                        'answer' => 'TiXara adalah platform digital untuk membeli dan menjual tiket event. Kami menghubungkan event organizer dengan calon peserta event di seluruh Indonesia.',
                        'helpful_count' => 245
                    ],
                    [
                        'question' => 'Bagaimana cara mendaftar di TiXara?',
                        'answer' => 'Anda bisa mendaftar dengan mengklik tombol "Daftar" di halaman utama, lalu pilih sebagai Pembeli atau Organizer sesuai kebutuhan Anda.',
                        'helpful_count' => 189
                    ],
                    [
                        'question' => 'Apakah TiXara gratis digunakan?',
                        'answer' => 'Untuk pembeli, TiXara gratis digunakan. Untuk organizer, kami mengenakan fee kecil dari setiap transaksi yang berhasil.',
                        'helpful_count' => 156
                    ]
                ]
            ],
            [
                'category' => 'Pembeli',
                'questions' => [
                    [
                        'question' => 'Bagaimana cara membeli tiket?',
                        'answer' => 'Pilih event yang diinginkan, klik "Beli Tiket", pilih jenis tiket, isi data, pilih metode pembayaran, dan konfirmasi pembelian.',
                        'helpful_count' => 312
                    ],
                    [
                        'question' => 'Metode pembayaran apa saja yang tersedia?',
                        'answer' => 'Kami menerima transfer bank, e-wallet (GoPay, OVO, DANA), virtual account, kartu kredit/debit, dan QRIS.',
                        'helpful_count' => 278
                    ],
                    [
                        'question' => 'Bisakah saya refund tiket?',
                        'answer' => 'Kebijakan refund tergantung pada organizer event. Silakan cek detail kebijakan refund di halaman event.',
                        'helpful_count' => 203
                    ]
                ]
            ],
            [
                'category' => 'Organizer',
                'questions' => [
                    [
                        'question' => 'Berapa fee yang dikenakan TiXara?',
                        'answer' => 'Fee TiXara adalah 5% dari harga tiket + biaya payment gateway. Fee ini sudah termasuk semua fitur platform.',
                        'helpful_count' => 167
                    ],
                    [
                        'question' => 'Kapan saya menerima pembayaran?',
                        'answer' => 'Pembayaran akan ditransfer ke rekening Anda 3-7 hari kerja setelah event selesai, sesuai dengan terms & conditions.',
                        'helpful_count' => 134
                    ],
                    [
                        'question' => 'Bisakah saya menggunakan domain sendiri?',
                        'answer' => 'Untuk paket Enterprise, Anda bisa menggunakan custom domain. Hubungi tim support untuk informasi lebih lanjut.',
                        'helpful_count' => 89
                    ]
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $faqs
        ]);
    }
}
