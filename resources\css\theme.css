/* Dynamic Theme System */
:root {
    /* Default Green Theme */
    --color-primary: #A8D5BA;
    --color-secondary: #E8F5E8;
    --color-accent: #7FB069;
    
    /* Pasta Color Palette */
    --pasta-cream: #F5F5DC;
    --pasta-mint: #E8F5E8;
    --pasta-sage: #9CAF88;
    --pasta-peach: #FFCBA4;
    --pasta-salmon: #FA8072;
    
    /* Dynamic Theme Variables */
    --theme-primary: var(--color-primary);
    --theme-secondary: var(--color-secondary);
    --theme-accent: var(--color-accent);
    
    /* Light Mode Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark Mode */
.dark {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #374151;
    
    /* Dark mode shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Theme Color Classes */
.theme-primary {
    color: var(--theme-primary);
}

.theme-bg-primary {
    background-color: var(--theme-primary);
}

.theme-border-primary {
    border-color: var(--theme-primary);
}

.theme-secondary {
    color: var(--theme-secondary);
}

.theme-bg-secondary {
    background-color: var(--theme-secondary);
}

.theme-accent {
    color: var(--theme-accent);
}

.theme-bg-accent {
    background-color: var(--theme-accent);
}

/* Dynamic Background Classes */
.bg-dynamic-primary {
    background-color: var(--bg-primary);
}

.bg-dynamic-secondary {
    background-color: var(--bg-secondary);
}

.text-dynamic-primary {
    color: var(--text-primary);
}

.text-dynamic-secondary {
    color: var(--text-secondary);
}

.border-dynamic {
    border-color: var(--border-color);
}

/* Theme Gradients */
.gradient-theme-primary {
    background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));
}

.gradient-theme-secondary {
    background: linear-gradient(135deg, var(--theme-secondary), var(--theme-primary));
}

.gradient-pasta {
    background: linear-gradient(135deg, var(--pasta-cream), var(--pasta-mint));
}

/* Button Variants with Dynamic Themes */
.btn-theme-primary {
    background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.btn-theme-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    filter: brightness(1.1);
}

.btn-theme-secondary {
    background-color: var(--theme-secondary);
    color: var(--theme-primary);
    border: 2px solid var(--theme-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-theme-secondary:hover {
    background-color: var(--theme-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Card Styles with Dynamic Themes */
.card-theme {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card-theme:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-theme-accent {
    background: linear-gradient(135deg, var(--theme-secondary), var(--pasta-cream));
    border: 1px solid var(--theme-primary);
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
}

/* Input Styles with Dynamic Themes */
.input-theme {
    background-color: var(--bg-primary);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.input-theme:focus {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 3px rgba(var(--theme-primary), 0.1);
    outline: none;
}

/* Navigation Styles */
.nav-theme {
    background: linear-gradient(135deg, 
        rgba(var(--pasta-cream), 0.95), 
        rgba(var(--pasta-mint), 0.95)
    );
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(var(--theme-primary), 0.2);
}

.dark .nav-theme {
    background: linear-gradient(135deg, 
        rgba(17, 24, 39, 0.95), 
        rgba(31, 41, 55, 0.95)
    );
    border-bottom: 1px solid rgba(var(--theme-primary), 0.3);
}

/* Notification Styles */
.notification-theme {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-left: 4px solid var(--theme-primary);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
}

/* Loading Animation with Theme Colors */
.loading-theme {
    background: linear-gradient(90deg, 
        transparent, 
        var(--theme-secondary), 
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Pulse Animation with Theme Colors */
.pulse-theme {
    animation: pulse-theme 2s infinite;
}

@keyframes pulse-theme {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(var(--theme-primary), 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(var(--theme-primary), 0);
    }
}

/* Responsive Theme Adjustments */
@media (max-width: 768px) {
    .btn-theme-primary,
    .btn-theme-secondary {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
    
    .card-theme {
        border-radius: 0.75rem;
    }
}

/* Theme Transition Classes */
.theme-transition {
    transition: background-color 0.3s ease, 
                color 0.3s ease, 
                border-color 0.3s ease,
                box-shadow 0.3s ease;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .theme-transition,
    .btn-theme-primary,
    .btn-theme-secondary,
    .card-theme,
    .input-theme {
        transition: none;
    }
    
    .loading-theme,
    .pulse-theme {
        animation: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
    
    .dark {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Print Styles */
@media print {
    .nav-theme,
    .btn-theme-primary,
    .btn-theme-secondary {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}
