<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))" :class="{ 'dark': darkMode }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="theme-color" content="#A8D5BA">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <?php if(auth()->guard()->check()): ?>
        <meta name="user-authenticated" content="true">
    <?php endif; ?>

    <title><?php echo $__env->yieldContent('title', 'Admin Dashboard'); ?> - <?php echo e(config('app.name', 'TiXara')); ?></title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Custom Styles -->
    <style>
        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glassmorphism {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-collapsed {
            width: 4rem;
        }

        .sidebar-expanded {
            width: 16rem;
        }

        .transition-width {
            transition: width 0.3s ease-in-out;
        }

        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .dark .hover-lift:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }

        .gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .gradient-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .gradient-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .gradient-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-slide-in {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        .scrollbar-thin {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }

        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: transparent;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
            background-color: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background-color: rgba(156, 163, 175, 0.7);
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-inter antialiased bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300" x-data="{ sidebarCollapsed: false, showMobileMenu: false }">
    <div id="app" class="flex h-screen overflow-hidden">
        <!-- Mobile Sidebar Overlay -->
        <div x-show="showMobileMenu"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-40 lg:hidden">
            <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="showMobileMenu = false"></div>
        </div>

        <!-- Mobile Sidebar -->
        <div x-show="showMobileMenu"
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full"
             class="fixed inset-y-0 left-0 z-50 w-64 lg:hidden">
            <div class="flex flex-col h-full bg-white dark:bg-gray-800 shadow-xl">
                <?php echo $__env->make('layouts.partials.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </div>

        <!-- Desktop Sidebar -->
        <aside class="hidden lg:flex lg:flex-shrink-0 z-30">
            <div class="flex flex-col transition-width duration-300 ease-in-out"
                 :class="sidebarCollapsed ? 'w-16' : 'w-64'">
                <div class="flex flex-col flex-grow h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-lg">
                    <!-- Logo -->
                    <div class="flex items-center flex-shrink-0 px-4 py-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center w-full">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                <span class="text-white font-bold text-lg">T</span>
                            </div>
                            <div class="ml-3 transition-opacity duration-300"
                                 :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">
                                <h1 class="text-xl font-bold text-gray-900 dark:text-white">TiXara</h1>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Admin Panel</p>
                            </div>
                            <button @click="sidebarCollapsed = !sidebarCollapsed"
                                    class="ml-auto p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                                    :class="sidebarCollapsed ? 'ml-0' : 'ml-auto'">
                                <i data-lucide="chevron-left"
                                   class="w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-300"
                                   :class="sidebarCollapsed ? 'rotate-180' : ''"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <nav class="flex-1 px-3 py-4 space-y-2 overflow-y-auto scrollbar-thin">
                        <?php echo $__env->make('layouts.partials.admin-sidebar-nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </nav>

                    <!-- User Info -->
                    <div class="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4">
                        <div class="flex items-center space-x-3 p-3 rounded-xl bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 cursor-pointer"
                             :class="sidebarCollapsed ? 'justify-center' : ''">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0">
                                <span class="text-white text-sm font-bold">
                                    <?php echo e(substr(auth()->user()->name ?? 'A', 0, 1)); ?>

                                </span>
                            </div>
                            <div class="min-w-0 flex-1 transition-opacity duration-300"
                                 :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">
                                <p class="text-sm font-semibold text-gray-900 dark:text-white truncate">
                                    <?php echo e(auth()->user()->name ?? 'Admin'); ?>

                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                    <?php echo e(ucfirst(auth()->user()->role ?? 'admin')); ?>

                                </p>
                            </div>
                            <div class="transition-opacity duration-300"
                                 :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">
                                <i data-lucide="chevron-right" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Modern Header -->
            <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm z-20">
                <div class="flex items-center justify-between h-16 px-4 lg:px-6">
                    <!-- Left Section -->
                    <div class="flex items-center space-x-4">
                        <!-- Mobile menu button -->
                        <button @click="showMobileMenu = true"
                                class="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 lg:hidden transition-colors duration-200">
                            <i data-lucide="menu" class="w-6 h-6"></i>
                        </button>

                        <!-- Page Title -->
                        <div class="hidden lg:block">
                            <h1 class="text-xl font-semibold text-gray-900 dark:text-white"><?php echo $__env->yieldContent('title', 'Dashboard'); ?></h1>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo $__env->yieldContent('subtitle', 'Manage your platform'); ?></p>
                        </div>
                    </div>

                    <!-- Center Section - Search -->
                    <div class="flex-1 max-w-lg mx-4 hidden md:block">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="search" class="w-5 h-5 text-gray-400"></i>
                            </div>
                            <input type="text"
                                   placeholder="Search users, events, orders..."
                                   class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200">
                        </div>
                    </div>

                    <!-- Right Section -->
                    <div class="flex items-center space-x-3">
                        <!-- Quick Actions -->
                        <div class="hidden md:flex items-center space-x-2">
                            <button class="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200"
                                    title="Quick Add Event">
                                <i data-lucide="plus" class="w-5 h-5"></i>
                            </button>
                            <button class="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200"
                                    title="Export Data">
                                <i data-lucide="download" class="w-5 h-5"></i>
                            </button>
                        </div>

                        <!-- Theme Toggle -->
                        <button @click="darkMode = !darkMode"
                                class="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200"
                                title="Toggle Theme">
                            <i data-lucide="sun" class="w-5 h-5 dark:hidden"></i>
                            <i data-lucide="moon" class="w-5 h-5 hidden dark:block"></i>
                        </button>

                        <!-- Notifications -->
                        <div class="relative" x-data="{ showNotifications: false }">
                            <button @click="showNotifications = !showNotifications"
                                    class="relative p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 transition-colors duration-200">
                                <i data-lucide="bell" class="w-5 h-5"></i>
                                <!-- Notification Badge -->
                                <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">3</span>
                            </button>

                            <!-- Notifications Dropdown -->
                            <div x-show="showNotifications"
                                 @click.away="showNotifications = false"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
                                        <button class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                            Mark all read
                                        </button>
                                    </div>
                                </div>
                                <div class="max-h-96 overflow-y-auto">
                                    <!-- Notification Items -->
                                    <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 border-b border-gray-100 dark:border-gray-700">
                                        <div class="flex items-start space-x-3">
                                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">New user registered</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">John Doe just created an account</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">2 minutes ago</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 border-b border-gray-100 dark:border-gray-700">
                                        <div class="flex items-start space-x-3">
                                            <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">Payment received</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">Rp 150,000 from ticket purchase</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">5 minutes ago</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                        <div class="flex items-start space-x-3">
                                            <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">System maintenance</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">Scheduled maintenance in 2 hours</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">1 hour ago</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                                    <a href="#" class="block text-center text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                        View all notifications
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ showUserMenu: false }">
                            <button @click="showUserMenu = !showUserMenu"
                                    class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                <img src="<?php echo e(auth()->user()->avatar_url); ?>"
                                     alt="<?php echo e(auth()->user()->name); ?>"
                                     class="w-8 h-8 rounded-lg object-cover ring-2 ring-gray-200 dark:ring-gray-600">
                                <div class="hidden md:block text-left">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e(auth()->user()->name); ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e(ucfirst(auth()->user()->role)); ?></p>
                                </div>
                                <i data-lucide="chevron-down" class="w-4 h-4 text-gray-500 dark:text-gray-400 hidden md:block"></i>
                            </button>

                            <!-- User Dropdown -->
                            <div x-show="showUserMenu"
                                 @click.away="showUserMenu = false"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                    <div class="flex items-center space-x-3">
                                        <img src="<?php echo e(auth()->user()->avatar_url); ?>"
                                             alt="<?php echo e(auth()->user()->name); ?>"
                                             class="w-10 h-10 rounded-lg object-cover">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e(auth()->user()->name); ?></p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e(auth()->user()->email); ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="py-2">
                                    <a href="<?php echo e(route('admin.profile.edit')); ?>"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <i data-lucide="user" class="w-4 h-4 mr-3"></i>
                                        Profile Settings
                                    </a>
                                    <a href="<?php echo e(route('admin.settings.index')); ?>"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <i data-lucide="settings" class="w-4 h-4 mr-3"></i>
                                        System Settings
                                    </a>
                                    <a href="<?php echo e(route('admin.help.index')); ?>"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <i data-lucide="help-circle" class="w-4 h-4 mr-3"></i>
                                        Help & Support
                                    </a>
                                </div>
                                <div class="border-t border-gray-200 dark:border-gray-700 py-2">
                                    <button onclick="document.getElementById('logout-form').submit()"
                                            class="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200">
                                        <i data-lucide="log-out" class="w-4 h-4 mr-3"></i>
                                        Sign Out
                                    </button>
                                    <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="hidden">
                                        <?php echo csrf_field(); ?>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 focus:outline-none">
                <div class="animate-fade-in">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Toast Notifications Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


    <!-- AOS Init -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 600,
            once: true,
            offset: 50
        });

        // Initialize Lucide icons
        lucide.createIcons();

        // Toast notification system
        function showToast(type, title, message, duration = 5000) {
            const toastContainer = document.getElementById('toast-container');
            const toastId = 'toast-' + Date.now();

            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };

            const icons = {
                success: 'check-circle',
                error: 'x-circle',
                warning: 'alert-triangle',
                info: 'info'
            };

            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
            toast.innerHTML = `
                <div class="max-w-sm w-full ${colors[type]} shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i data-lucide="${icons[type]}" class="h-6 w-6 text-white"></i>
                            </div>
                            <div class="ml-3 w-0 flex-1 pt-0.5">
                                <p class="text-sm font-medium text-white">${title}</p>
                                <p class="mt-1 text-sm text-white opacity-90">${message}</p>
                            </div>
                            <div class="ml-4 flex-shrink-0 flex">
                                <button onclick="removeToast('${toastId}')" class="rounded-md inline-flex text-white hover:text-gray-200 focus:outline-none">
                                    <i data-lucide="x" class="h-5 w-5"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            toastContainer.appendChild(toast);
            lucide.createIcons();

            // Animate in
            setTimeout(() => {
                toast.className = `transform transition-all duration-300 ease-in-out translate-x-0 opacity-100`;
            }, 100);

            // Auto remove
            setTimeout(() => {
                removeToast(toastId);
            }, duration);
        }

        function removeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.className = `transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // Global search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[placeholder*="Search"]');
            if (searchInput) {
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        // Implement global search
                        console.log('Searching for:', this.value);
                    }
                });
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[placeholder*="Search"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Ctrl/Cmd + D for dark mode toggle
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                const darkModeToggle = document.querySelector('[title="Toggle Theme"]');
                if (darkModeToggle) {
                    darkModeToggle.click();
                }
            }
        });
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/layouts/admin.blade.php ENDPATH**/ ?>